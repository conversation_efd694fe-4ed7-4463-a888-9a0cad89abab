<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.UI</name>
    </assembly>
    <members>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.BitmapRegion">
            <summary>
            不规则窗体
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.BitmapRegion.CreateControlRegion(System.Windows.Forms.Control,System.Drawing.Bitmap)">
            <summary>
            Create and apply the region on the supplied control
            创建支持位图区域的控件（目前有button和form）
            </summary>
            <param name="control">The Control object to apply the region to控件</param>
            <param name="bitmap">The Bitmap object to create the region from位图</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.BitmapRegion.CalculateControlGraphicsPath(System.Drawing.Bitmap)">
            <summary>
            Calculate the graphics path that representing the figure in the bitmap
            excluding the transparent color which is the top left pixel.
            //计算位图中不透明部分的边界
            </summary>
            <param name="bitmap">The Bitmap object to calculate our graphics path from</param>
            <returns>Calculated graphics path</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.OnUpdatedEventHandler">
            <summary>
            检定方案更新委托
            </summary>
            <param name="sender"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem">
            <summary>
            检定方案项(检定方案窗口的栏位)
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.DgvImage">
            <summary>
            方案视图图标栏位
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.DgvItemName">
            <summary>
            方案视图检定项名称栏位
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.DgvUseCode">
            <summary>
            方案视图检定项功能代号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.DgvItemID">
            <summary>
            同一个检定项的方案序号(如误差方案：1,2,...)
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.CheckState">
            <summary>
            当前检定项状态
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.OnUpdated">
            <summary>
            检定方案状态更新事件   
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CheckItem.UpdateCheckState(CLDC.CLAT.CLWBS.DataModel.Enum.EmCheckState,System.Drawing.Image)">
            <summary>
            更新检定项状态
            </summary>
            <param name="state">检定状态</param>
            <param name="image">图标</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param">
            <summary>
            系统参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param.AddXml(System.String)">
            <summary>
            读区参数
            </summary>
            <param name="path">文件路径</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param.SaveXml(System.String,CLDC.CLAT.CLWBS.UI.Assistant.Common.CL2018Param)">
            <summary>
            保存参数
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam">
            <summary>
            系统参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.AddXml(System.String)">
            <summary>
            读区参数
            </summary>
            <param name="path">文件路径</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.SaveXml(System.String,CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam)">
            <summary>
            保存参数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.PortParam(System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits)">
            <summary>
            转换通讯参数
            </summary>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
            <returns>如：38400,n,8,1</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLEquipMentParam.PortParam(System.String,System.Int32@,System.IO.Ports.Parity@,System.Int32@,System.IO.Ports.StopBits@)">
            <summary>
            参数转换
            </summary>
            <param name="param">输入参数</param>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage">
            <summary>
            消息机制
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnClosedForm">
            <summary>
            用于关闭窗体的委托
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnShownForm">
            <summary>
            用于显示窗体的委托
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.ShownForm">
            <summary>
            显示窗体触发事件
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnLoginMode">
            <summary>
            改变系统登录模式的委托
            </summary>
            <param name="runMode">系统登录模式</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnRefreshStateText">
            <summary>
            实时刷新状态提示信息的委托
            </summary>
            <param name="strMessage">当前提示信息</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.RefreshStateText">
            <summary>
            状态栏提示信息实时刷新事件
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnControlVisible">
            <summary>
            设置控件的可见性委托
            </summary>
            <param name="visible">可见性</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.OnSendMessage">
            <summary>
            刷新电量的委托
            </summary>
            <param name="power">当前标准表电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.TS_METER_POWER">
            <summary>
            标准表电量消息标志
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.TS_CONTROL_VISIBLE">
            <summary>
            Label可视化消息标志
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.WM_VIFORM_INIT">
            <summary>
            初始化检定方案信息消息
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.TS_CONTROL_ENABLED">
            <summary>
            设置工具栏按钮的活动状态
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClMessage.WM_COLOR_RESET">
            <summary>
            
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam">
            <summary>
            系统表位参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.AddXml(System.String)">
            <summary>
            读区参数
            </summary>
            <param name="path">文件路径</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.SaveXml(System.String,CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam)">
            <summary>
            保存参数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.PortParam(System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits)">
            <summary>
            转换通讯参数
            </summary>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
            <returns>如：38400,n,8,1</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLMeterParam.PortParam(System.String,System.Int32@,System.IO.Ports.Parity@,System.Int32@,System.IO.Ports.StopBits@)">
            <summary>
            参数转换
            </summary>
            <param name="param">输入参数</param>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam">
            <summary>
            系统误差板参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.AddXml(System.String)">
            <summary>
            读区参数
            </summary>
            <param name="path">文件路径</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.SaveXml(System.String,CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam)">
            <summary>
            保存参数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.PortParam(System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits)">
            <summary>
            转换通讯参数
            </summary>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
            <returns>如：38400,n,8,1</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.CLPanelParam.PortParam(System.String,System.Int32@,System.IO.Ports.Parity@,System.Int32@,System.IO.Ports.StopBits@)">
            <summary>
            参数转换
            </summary>
            <param name="param">输入参数</param>
            <param name="BaudRate">波特率</param>
            <param name="Party">校验位</param>
            <param name="DataBits">数据位</param>
            <param name="Stopbts">停止位</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam">
            <summary>
            系统参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.TitleNameID">
            <summary>
            台体类型（单相，三相）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.MainBluetoothPower">
            <summary>
            主蓝牙模块功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.MeterBluetoothPower">
            <summary>
            电表模块功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.IsSetBluetoothPower">
            <summary>
            是否设置蓝牙发射功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.EncryptType">
            <summary>
            加密机类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.JjfType">
            <summary>
            加密机类型
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.ReadXml(System.String)">
            <summary>
            读取XML
            </summary>
            <param name="path"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam.SaveXml(System.String,CLDC.CLAT.CLWBS.UI.Assistant.Common.ClSysParam)">
            <summary>
            保存XML
            </summary>
            <param name="path"></param>
            <param name="_ClSysParam"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ScreenSize.setTag(System.Windows.Forms.Control)">
            <summary>
            获取控件的width、height、left、top、字体大小的值,存放在控件的Tag属性中
            </summary>
            <param name="cons"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.Common.ScreenSize.setControls(System.Single,System.Single,System.Windows.Forms.Control)">
            <summary>
            根据窗体大小调整控件大小
            </summary>
            <param name="newx"></param>
            <param name="newy"></param>
            <param name="cons"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath">
            <summary>
            各种文件路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Ini_Config_FileName">
            <summary>
            系统配置INI文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Ini_Carrier_FileName">
            <summary>
            载波通讯参数INI文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Ini_Adjust_FileName">
            <summary>
            火线校准参数INI文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Xml_Assembly_FileName">
            <summary>
            程序集参数XML文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Xml_ErrorCode_FileName">
            <summary>
            异常信息编码XML文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Xml_Server_FileName">
            <summary>
            电能表数据标识XML文档路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Xml_ShelfConfig_FileName">
            <summary>
            设备通讯配置
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Config.FilePath.Xml_Config_FileName">
            <summary>
            配置
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb">
            <summary>
            程序集内全局变量
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.ToolBarHandle">
            <summary>
            工具栏窗体句柄
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.HWnd_VIForm">
            <summary>
            检定方案窗体句柄
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.HWnd_DataView">
            <summary>
            数据视图句柄
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_RunMode">
            <summary>
            当前运行模式
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_IsRunCon">
            <summary>
            是否继续检定
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_StationType">
            <summary>
            当前台体功能
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_LogFile">
            <summary>
            日志文件标识
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_LinkMode">
            <summary>
            客户端和服务器的链接模式
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.IsRecordError">
            <summary>
            是否记录异常信息(写入异常信息文件)
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.G_UserName">
            <summary>
            操作员
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.Const.GlobalVerb.ClientSysBgColor">
            <summary>
            系统背景色
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.ExcelData.ExcelOperation.ReturnStatus">
            <summary>
            执行返回状态
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.ExcelData.ExcelOperation.ReturnMessage">
            <summary>
            执行返回信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.ExcelData.ExcelOperation.ImportExcel(System.String)">
            <summary>
            导入EXCEL到DataSet
            </summary>
            <param name="fileName">Excel全路径文件名</param>
            <returns>导入成功的DataSet</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.ExcelData.ExcelOperation.ExportExcel(System.Collections.Generic.List{System.String},System.Data.DataTable,System.String)">
            <summary>
            把DataTable导出到EXCEL
            </summary>
            <param name="reportName">报表名称</param>
            <param name="dt">数据源表</param>
            <param name="saveFileName">Excel全路径文件名</param>
            <returns>导出是否成功</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ColumnNode">
            <summary>
            列节点
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ColumnNode.Column">
            <summary>
            列数据
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ColumnNode.Visible">
            <summary>
            列可视化
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode">
            <summary>
            方案节点
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.Count">
            <summary>
            行节点个数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.ItemID">
            <summary>
            检定项序号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.Item(System.Int32)">
            <summary>
            索引器
            </summary>
            <param name="index">行索引号</param>
            <returns>行节点</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.Init(System.Int32,System.Int32)">
            <summary>
            初始化
            </summary>
            <param name="rowCount">行节点个数</param>
            <param name="colCount">列节点个数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ItemNode.Clear">
            <summary>
            清空列表
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes">
            <summary>
            检定节点列表
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.Count">
            <summary>
            检定项总数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.Item(System.Int32)">
            <summary>
            索引器
            </summary>
            <param name="index">检定项索引号</param>
            <returns>检定项</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.#ctor">
            <summary>
            节点列表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.Add(System.Int32)">
            <summary>
            加载一个检定点
            </summary>
            <param name="itemId">检定项ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.Init(System.Int32)">
            <summary>
            初始化
            </summary>
            <param name="itemCount"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.ContainsId(System.Int32)">
            <summary>
            查找是否包含检定项ID
            </summary>
            <param name="itemID">检定项ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.ListNodes.Clear">
            <summary>
            清空列表
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode">
            <summary>
            行节点
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.Count">
            <summary>
            列节点个数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.Checked">
            <summary>
            指定行是否选中
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.Item(System.Int32)">
            <summary>
            索引器
            </summary>
            <param name="index">列索引号</param>
            <returns>列节点</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.Init(System.Int32)">
            <summary>
            初始化列表
            </summary>
            <param name="colCount">列表大小</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.NodeView.RowNode.Clear">
            <summary>
            清空列表
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ApplyScheme.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ApplyScheme.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ApplyScheme.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.DitControlLocation">
            <summary>
            所有格子的区域集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.DitControlEquipMent">
            <summary>
            所有设备集合
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.TextNum">
            <summary>
            名字
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.StartPort">
            <summary>
            起始端口号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.RemotePort">
            <summary>
            远端端口
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.SeverID">
            <summary>
            服务器ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.IP">
            <summary>
            ip地址
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.SystemEvent_ComName(System.Int32,System.String,System.String)">
            <summary>
            移除回收站的端口名和ID号
            </summary>
            <param name="id"></param>
            <param name="ComName"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.SystemEvent_EventLocation(System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.Int32,System.String)">
            <summary>
            接收到坐标后移动浮动窗体
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <param name="h"></param>
            <param name="w"></param>
            <param name="BName"></param>
            <param name="id"></param>
            <param name="ComName"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.button1_Click(System.Object,System.EventArgs)">
            <summary>
            浮动窗体确定
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.ParseData(System.String)">
            <summary>
            解析“端口管理表位”数据
            </summary>
            <param name="para">用户输入的信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.button2_Click(System.Object,System.EventArgs)">
            <summary>
            新增设备
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.LoadNewEquipMent(System.String,System.String,System.Collections.Generic.List{System.Int32},System.String,System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            加载设备
            </summary>
            <param name="EquipMentName">设备名</param>
            <param name="EquipMentId">受信ID</param>
            <param name="LstEquipMeterId">设备管理表位号</param>
            <param name="CommParams">通讯参数</param>
            <param name="CommType">通讯类型</param>
            <param name="ReCommNum">重复通讯次数</param>
            <param name="OutTime">超时时间</param>
            <param name="ComName">端口名</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCL2018.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCOMPanle._BackName">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCOMPanle.BackName">
            <summary>
            
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCOMPanle.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCOMPanle.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlCOMPanle.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.Id">
            <summary>
            总ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.DitMainLocation">
            <summary>
            坐标
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent._ThisText">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.ThisText">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.EquipMentId">
            <summary>
            受信ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.LstEquipMeterId">
            <summary>
            设备管控表位
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.CommParams">
            <summary>
            通讯参数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.CommType">
            <summary>
            通讯类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.ReCommNum">
            <summary>
            重复通讯次数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.OutTime">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.ComName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.ParentControlName">
            <summary>
            父控件名字
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.MaxLocation">
            <summary>
            最大区域
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.ConfigType">
            <summary>
            配置类型：0通用，1直接，2互感
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.LoadEquipMent">
            <summary>
            加载控件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ControlEquipMent.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.YsTime">
            <summary>
            亮屏延时时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.CheckMeterType">
            <summary>
            是否检测表类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.CheckMeterBarcode">
            <summary>
            是否检测表条码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.SaveImgOrNo">
            <summary>
            是否保持图像
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.MouseOffset">
            <summary>
            鼠标位置
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.IsMouseDown">
            <summary>
            是否按下鼠标
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.CreateLED_Resize(System.Object,System.EventArgs)">
            <summary>
            窗体大小改变时触发该事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.GetInstance(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterCount">表数量</param>
            <param name="cameraCount">相机数量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.lbl_Title_MouseDown(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标按下事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.lbl_Title_MouseUp(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标松开事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.lbl_Title_MouseMove(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标移动事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.btn_PowerOn_Click(System.Object,System.EventArgs)">
            <summary>
            升源按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.btn_PowerOff_Click(System.Object,System.EventArgs)">
            <summary>
            关源按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.btn_LightOn_Click(System.Object,System.EventArgs)">
            <summary>
            开灯按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.btn_LightOff_Click(System.Object,System.EventArgs)">
            <summary>
            关灯按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLED.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.YsTime">
            <summary>
            亮屏延时时间
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.MouseOffset">
            <summary>
            鼠标位置
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.IsMouseDown">
            <summary>
            是否按下鼠标
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.CreateLED_Resize(System.Object,System.EventArgs)">
            <summary>
            窗体大小改变时触发该事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.GetInstance(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterCount">表数量</param>
            <param name="cameraCount">相机数量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.lbl_Title_MouseDown(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标按下事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.lbl_Title_MouseUp(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标松开事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.lbl_Title_MouseMove(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标移动事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.btn_PowerOn_Click(System.Object,System.EventArgs)">
            <summary>
            升源按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.btn_PowerOff_Click(System.Object,System.EventArgs)">
            <summary>
            关源按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.btn_LightOn_Click(System.Object,System.EventArgs)">
            <summary>
            开灯按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.btn_LightOff_Click(System.Object,System.EventArgs)">
            <summary>
            关灯按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.CreateLEDGD.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ErrorData.StartThread(System.Threading.WaitCallback,System.Object)">
            <summary>
            线程池控制开始线程
            </summary>
            <param name="CallBack">方法</param>
            <param name="s">参数</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ErrorData.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ErrorData.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ErrorData.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCarriers.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCarriers.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCarriers.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateLableData.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateLableData.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateLableData.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateModel.ParamChangeHandle">
            <summary>
            拖动后窗体显示相应效果
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateModel.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateModel.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmCreateModel.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDataSearch.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDataSearch.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDataSearch.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmDataUpLoad.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmDataUpLoad.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmDataUpLoad.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDecode.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDecode.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmDecode.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.FrmLogin_Load(System.Object,System.EventArgs)">
            <summary>
            窗体加载
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.pbLogin_Click(System.Object,System.EventArgs)">
            <summary>
            登录按钮事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.pbReset_Click(System.Object,System.EventArgs)">
            <summary>
            取消按钮事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.hook_KeyDown(System.Object,System.Windows.Forms.KeyEventArgs)">
            <summary>
            键盘回调
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.PictureBox_MouseMove(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            鼠标移动
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.PictureBox_MouseLeave(System.Object,System.EventArgs)">
            <summary>
            鼠标离开
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.frmLogin.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterErrorLogin.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterErrorLogin.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterErrorLogin.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMinLogin.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMinLogin.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMinLogin.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmOCR.ParamChangeHandle">
            <summary>
            拖动后窗体显示相应效果
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmOCR.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmOCR.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmOCR.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRow">
            <summary>
            Display a check box user interface(UI) to the dataGridViewRow
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRow.#ctor">
            <summary>
            constructor
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRow.#ctor(System.Boolean)">
            <summary>
            constructor
            </summary>
            <param name="threeState"> 
            Indicates whether the checkbox will allow three check states rather than two. 
            </param>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRow.DefaultCellStyle">
            <summary>
            Gets or sets the default cell style of the band.
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRow.CheckHeaderCell">
            <summary>
            Gets or sets the current height of the row.
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderEventHander">
            <summary>
            Represents the method that will handle row-related events of a DataGridViewCheckBoxRowHeaderCell. 
            </summary>
            <param name="sender">
            The source of the event. 
            </param>
            <param name="e">
            A DataGridViewCheckBoxRowHeaderEventArgs that contains the event data. 
            </param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderEventArgs">
            <summary>
            Provides data for row-related System.Windows.Forms.DataGridView events. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderEventArgs.Checked">
            <summary>
            Gets or sets a value indicating whether the DataGridViewCheckBoxRowHeaderCell
            is in checked state. 
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell">
            <summary>
            Displays a check box user interface (UI) to use in a System.Windows.Forms.DataGridView control. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.Checked">
            <summary>
            Gets or sets a value indicating whether the DataGridViewCheckBoxRowHeaderCell
            is in checked state. 
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.SetChecked(System.Boolean)">
            <summary>
            Sets the value of check box
            </summary>
            <param name="bChecked">check box state</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.SetValue(System.Int32,System.Object)">
            <summary>
            Sets the value of the cell. 
            </summary>
            <param name="rowIndex">The index of the cell's parent row. </param>
            <param name="value">The cell value to set. </param>
            <returns>true if the value has been set; otherwise, false.</returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.CheckState">
            <summary>
            Gets the state of the CheckBoxRowHeaderCell. 
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.OnCheckBoxClicked">
            <summary>
            Occurs when the value of the Checked property changes between posts to the server. 
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.Paint(System.Drawing.Graphics,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Int32,System.Windows.Forms.DataGridViewElementStates,System.Object,System.Object,System.String,System.Windows.Forms.DataGridViewCellStyle,System.Windows.Forms.DataGridViewAdvancedBorderStyle,System.Windows.Forms.DataGridViewPaintParts)">
            <summary>
            Ovveride Painting the current DataGridViewCheckBoxRowHeader. 
            </summary>
            <param name="graphics">The Graphics used to paint the DataGridViewCell.</param>
            <param name="clipBounds">
            A Rectangle that represents the area of the DataGridView that needs to be repainted.
            </param>
            <param name="cellBounds">
            A Rectangle that contains the bounds of the DataGridViewCell that is being painted.
            </param>
            <param name="rowIndex">The row index of the cell that is being painted.</param>
            <param name="cellState">
            A bitwise combination of DataGridViewElementStates values that specifies the state of the cell.
            </param>
            <param name="value">The data of the DataGridViewCell that is being painted.</param>
            <param name="formattedValue">
            The formatted data of the DataGridViewCell that is being painted.
            </param>
            <param name="errorText">An error message that is associated with the cell.</param>
            <param name="cellStyle">
            A DataGridViewCellStyle that contains formatting and style information about the cell.
            </param>
            <param name="advancedBorderStyle">
            A DataGridViewAdvancedBorderStyle that contains border styles for the cell that is being painted.
            </param>
            <param name="paintParts">
            A bitwise combination of the DataGridViewPaintParts values 
            that specifies which parts of the cell need to be painted.
            </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxRowHeaderCell.OnMouseClick(System.Windows.Forms.DataGridViewCellMouseEventArgs)">
            <summary>
            Called when the user clicks a mouse button while the pointer is on a row header cell. (override)
            </summary>
            <param name="e">
            A DataGridViewCellMouseEventArgs that contains the event data. 
            </param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeaderEventHander">
            <summary>
            Represents the method that will handle row-related events of a DataGridViewCheckBoxRowHeaderCell. 
            </summary>
            <param name="sender">
            The source of the event. 
            </param>
            <param name="e">
            A DataGridViewCheckBoxRowHeaderEventArgs that contains the event data. 
            </param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeaderEventArgs">
            <summary>
            Provides data for row-related System.Windows.Forms.DataGridView events. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeaderEventArgs.Checked">
            <summary>
            Gets or sets a value indicating whether the DataGridViewCheckBoxRowHeaderCell
            is in checked state. 
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader">
            <summary>
            Represents the checkBox cell in the top left corner of the DataGridView 
            that sits above the row headers and to the left of the column headers. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader.Checked">
            <summary>
            Gets or sets a value indicating whether the DataGridViewCheckBoxRowHeaderCell
            is in checked state. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader.CheckState">
            <summary>
            Gets the state of the CheckBoxTopLeftHeaderCell.
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader.OnCheckBoxClicked">
            <summary>
            Occurs when the value of the Checked property changes between posts to the server. 
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader.Paint(System.Drawing.Graphics,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Int32,System.Windows.Forms.DataGridViewElementStates,System.Object,System.Object,System.String,System.Windows.Forms.DataGridViewCellStyle,System.Windows.Forms.DataGridViewAdvancedBorderStyle,System.Windows.Forms.DataGridViewPaintParts)">
            <summary>
            Ovveride Painting the current DataGridViewCheckBoxRowHeader. 
            </summary>
            <param name="graphics">The Graphics used to paint the DataGridViewCell.</param>
            <param name="clipBounds">
            A Rectangle that represents the area of the DataGridView that needs to be repainted.
            </param>
            <param name="cellBounds">
            A Rectangle that contains the bounds of the DataGridViewCell that is being painted.
            </param>
            <param name="rowIndex">The row index of the cell that is being painted.</param>
            <param name="cellState">
            A bitwise combination of DataGridViewElementStates values that specifies the state of the cell.
            </param>
            <param name="value">The data of the DataGridViewCell that is being painted.</param>
            <param name="formattedValue">
            The formatted data of the DataGridViewCell that is being painted.
            </param>
            <param name="errorText">An error message that is associated with the cell.</param>
            <param name="cellStyle">
            A DataGridViewCellStyle that contains formatting and style information about the cell.
            </param>
            <param name="advancedBorderStyle">
            A DataGridViewAdvancedBorderStyle that contains border styles for the cell that is being painted.
            </param>
            <param name="paintParts">
            A bitwise combination of the DataGridViewPaintParts values 
            that specifies which parts of the cell need to be painted.
            </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentClass.DataGridViewCheckBoxTopLeftHeader.OnMouseClick(System.Windows.Forms.DataGridViewCellMouseEventArgs)">
            <summary>
            Called when the user clicks a mouse button 
            while the pointer is on a row header cell. (override)
            </summary>
            <param name="e">
            A DataGridViewCellMouseEventArgs that contains the event data. 
            </param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentDataGridView">
            <summary>
            Extends System.Windows.Forms's DataGridView
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentDataGridView.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentDataGridView.TopLeftHeaderCell">
            <summary>
            Gets or sets the header cell located in the upper left corner of the DataGridView control. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentDataGridView.TopLeftHeaderCellText">
            <summary>
            Gets or sets the value associated with this cell.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.ExtentDataGridView.OnRowsAdded(System.Windows.Forms.DataGridViewRowsAddedEventArgs)">
            <summary>
            Occurs after a new row is added to the ExtendedDataGridView. 
            </summary>
            <param name="e">
            A DataGridViewRowsAddedEventArgs that contains the event data. 
            </param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmDataAndNotes.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmDataAndNotes.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmDataAndNotes.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.SetPowerVisible(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            是否显示累计电量和累计脉冲(true-显示)
            </summary>
            <param name="trialType">试验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.SetMonitor(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData,System.Single)">
            <summary>
            更新监视器消息
            </summary>   
            <param name="monitorData">标准表监视数据</param>
            <param name="ua_Ia_Angle">A相电压与A相电流之间的夹角</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.SetPowerPulse(System.UInt64,System.Single)">
            <summary>
            更新累计电量和脉冲
            </summary>
            <param name="pulse">累计脉冲</param>
            <param name="power">累计电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitor2.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.SetPowerVisible(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            是否显示累计电量和累计脉冲(true-显示)
            </summary>
            <param name="trialType">试验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.SetMonitor(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData,System.Single)">
            <summary>
            更新监视器消息
            </summary>   
            <param name="monitorData">标准表监视数据</param>
            <param name="ua_Ia_Angle">A相电压与A相电流之间的夹角</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.SetPowerPulse(System.UInt64,System.Single)">
            <summary>
            更新累计电量和脉冲
            </summary>
            <param name="pulse">累计脉冲</param>
            <param name="power">累计电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMonitorUniPhase2.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMsgText.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMsgText.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmMsgText.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmNewImageShow.GetInstance">
            <summary>
            图片显示控件
            </summary>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmNewImageShow.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmNewImageShow.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Inlay.frmNewImageShow.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterInfoManage.AddRows(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            添加一行数据
            </summary>
            <param name="GUID_ID"></param>
            <param name="ASSET_NUM"></param>
            <param name="BAR_CODE"></param>
            <param name="METER_ID"></param>
            <param name="MANU_CODE"></param>
            <param name="METER_ADDR"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterInfoManage.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterInfoManage.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.FrmMeterInfoManage.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.InputMeterInfo.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.InputMeterInfo.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.InputMeterInfo.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.BaseInputMeterNo.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.BaseInputMeterNo.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.BaseInputMeterNo.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.InputMeterNo.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.InputMeterNo.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MeterNoInput.InputMeterNo.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.StartThread(System.Threading.WaitCallback,System.Object)">
            <summary>
            线程池控制开始线程
            </summary>
            <param name="CallBack">方法</param>
            <param name="s">参数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.ControlMeter(System.Object)">
            <summary>
            控制表位隔离
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.UNControlMeter(System.Object)">
            <summary>
            控制表位恢复
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.PowerOn(CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceParams)">
            <summary>
            升源
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.PowerOff(System.Object)">
            <summary>
            关源
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.IniFactor(System.String,System.Single@,System.Single@)">
            <summary>
            计算角度
            </summary>
            <param name="sFactor">功率因数</param>
            <param name="Up">电压角度</param>
            <param name="Ip">电流角度</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.ParseMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Single@,System.Single@)">
            <summary>
            解析表信息
            </summary>
            <param name="MeterInfo">表信息集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.IniFactor(System.String,CLDC.Framework.DataModel.Enum.EmWireMode,CLDC.Framework.DataModel.Enum.EmPhase,CLDC.Framework.DataModel.Enum.EmWattType,System.Single@,System.Single@,System.Single@,System.Single@,System.Single@,System.Single@,System.Single@,System.Single@,System.Single@)">
            <summary>
            角度计算
            </summary>
            <param name="sFactor">功率因素</param>
            <param name="WireMode">接线方式</param>
            <param name="Phase">相位</param>
            <param name="Watt">做功方式</param>
            <param name="VoltAPhase">A相电压角度</param>
            <param name="VoltBPhase">B相电压角度</param>
            <param name="VoltCPhase">C相电压角度</param>
            <param name="CurtAPhase">A相电流角度</param>
            <param name="CurtBPhase">B相电流角度</param>
            <param name="CurtCPhase">C相电流角度</param>
            <param name="FactorA">A相功率因素</param>
            <param name="FactorB">B相功率因素</param>
            <param name="FactorC">C相功率因素</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.MeterChecked">
            <summary>
            获取选中表位
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.MeterUnChecked">
            <summary>
            获取未选中表位
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.ClearState(System.Object)">
            <summary>
            控制表位恢复
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.button9_Click(System.Object,System.EventArgs)">
            <summary>
            设置485通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SetCommDoor(System.Object)">
            <summary>
            设置通讯端口
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.button8_Click(System.Object,System.EventArgs)">
            <summary>
            蓝牙通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.button7_Click(System.Object,System.EventArgs)">
            <summary>
            232通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.IsPowerCurrentOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32)">
            <summary>
            检测电流是否关闭
            </summary>
            <param name="timeOut">超时时间 默认10秒</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.PowerCurrentOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam)">
            <summary>
            关电流
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.button10_Click(System.Object,System.EventArgs)">
            <summary>
            连接国网蓝牙
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SetSamePortPin(System.Object)">
            <summary>
            同端口引脚设置
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SetNoSamePortPin(System.Object)">
            <summary>
            不同端口引脚设置
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.InteractiveRadioAutoSetAddr(System.Object)">
            <summary>
            自动分配地址
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SetPortParams(System.Object)">
            <summary>
            设置串口参数
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SetErrPanelParam(System.Object)">
            <summary>
            设置误差检定参数
            </summary>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.GetErrorCurrent(System.Object)">
            <summary>
            查询当前误差
            </summary>
            <param name="meterIds"></param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.UpgradeEnabled(System.Object)">
            <summary>
            升级使能
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.SoftwareReset(System.Object)">
            <summary>
            软件重启
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.WriteUpgradeData(System.Object)">
            <summary>
            升级数据
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.IsPointstr(System.String[],System.Int32,System.Collections.Generic.List{System.Int32})">
            <summary>
            表位频点检查
            </summary>
            <param name="arrMeter"></param>
            <param name="AisleNum"></param>
            <param name="ListMeter"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.GetCode(System.Int32,System.String)">
            <summary>
            获取功能代码
            </summary>
            <param name="type"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.B_Connect(System.Object)">
            <summary>
            连接蓝牙国网
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.MulSourceControl.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.FormTitleText">
            <summary>
            窗体标题文本
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.SaveParams">
            <summary>
            保存配置参数,可重载
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.CancelParams">
            <summary>
            取消参数设置,可重载
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.ParamsFormBase.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AdjustTimeParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AdjustTimeParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AdjustTimeParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AVCParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AVCParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.AVCParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockDeviationParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockDeviationParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockDeviationParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ConstParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ConstParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ConstParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockTimeParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockTimeParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ClockTimeParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.EnergyDegressionParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.EnergyDegressionParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.EnergyDegressionParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorChangingParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorChangingParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorChangingParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorConsistencyParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorConsistencyParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorConsistencyParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorCurrUpDownParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorCurrUpDownParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ErrorCurrUpDownParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ItemsData.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ItemsData.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.ItemsData.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MaxDemandParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MaxDemandParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MaxDemandParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MeterStartParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MeterStartParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.MeterStartParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.Rs485Params.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.Rs485Params.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.Rs485Params.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SdChangeParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SdChangeParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SdChangeParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SourcePowerData.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SourcePowerData.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SourcePowerData.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SzErrorParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SzErrorParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeFrms.SzErrorParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeManage.datarow(System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            新的行
            </summary>
            <param name="col1"></param>
            <param name="col3"></param>
            <param name="col9"></param>
            <param name="col6"></param>
            <param name="col7"></param>
            <param name="col8"></param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeManage.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeManage.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SchemeManage.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SerialForm.SerialForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SerialForm.SerialForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SerialForm.SerialForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.ThreadReadExcel(System.String)">
            <summary> 
            使用COM，多线程读取Excel（1 主线程、4 副线程） 
            </summary> 
            <param name="excelFilePath">路径</param> 
            <returns>DataTabel</returns> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.ExtractErrorData(System.Data.DataTable,System.String)">
            <summary>
            标准表送检报告数据解析
            </summary>
            <param name="dtexcledata"></param>
            <param name="txtName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.ValueIsNull(System.String)">
            <summary>
            判断值是否为有效值
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.AddRows(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            显示数据到控件
            </summary>
            <param name="MeterID">表编号</param>
            <param name="ThatDonT">相别</param>
            <param name="WorkType">做功类型</param>
            <param name="Voltag">电压</param>
            <param name="Electricity">电流</param>
            <param name="PowerFactor">功率因素</param>
            <param name="ErrorSum">原始误差</param>
            <param name="OffsetBound">误差限</param>
            <param name="UpdateTime">修改时间</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.AddRowsErr(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            显示数据到控件
            </summary>
            <param name="MeterID">表编号</param>
            <param name="ThatDonT">相别</param>
            <param name="WorkType">做功类型</param>
            <param name="Voltag">电压</param>
            <param name="Electricity">电流</param>
            <param name="PowerFactor">功率因素</param>
            <param name="ErrorSum">原始误差</param>
            <param name="OffsetBound">误差限</param>
            <param name="UpdateTime">修改时间</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SetOriginalError.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.StartThread(System.Threading.WaitCallback,System.Object)">
            <summary>
            线程池控制开始线程
            </summary>
            <param name="CallBack">方法</param>
            <param name="s">参数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.ControlMeter(System.Object)">
            <summary>
            控制表位隔离
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.UNControlMeter(System.Object)">
            <summary>
            控制表位恢复
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.PowerOn(CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceParams)">
            <summary>
            升源
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.PowerOff(System.Object)">
            <summary>
            关源
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.IsPowerCurrentOff(System.Int32)">
            <summary>
            检测电流是否关闭
            </summary>
            <param name="timeOut">超时时间 默认5秒</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.ParseMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Single@,System.Single@)">
            <summary>
            解析表信息
            </summary>
            <param name="MeterInfo">表信息集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.IniFactor(System.String,System.Single@,System.Single@)">
            <summary>
            计算角度
            </summary>
            <param name="sFactor">功率因数</param>
            <param name="Up">电压角度</param>
            <param name="Ip">电流角度</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.MeterChecked">
            <summary>
            获取选中表位
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.MeterUnChecked">
            <summary>
            获取未选中表位
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button2_Click(System.Object,System.EventArgs)">
            <summary>
            读取时段投切时间
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.ClearState(System.Object)">
            <summary>
            控制表位恢复
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button8_Click(System.Object,System.EventArgs)">
            <summary>
            485通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetCommDoor(System.Object)">
            <summary>
            设置通讯端口
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button7_Click(System.Object,System.EventArgs)">
            <summary>
            蓝牙通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button6_Click(System.Object,System.EventArgs)">
            <summary>
            232通讯
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.IsPowerCurrentOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32)">
            <summary>
            检测电流是否关闭
            </summary>
            <param name="timeOut">超时时间 默认10秒</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.PowerCurrentOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam)">
            <summary>
            关电流
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button10_Click(System.Object,System.EventArgs)">
            <summary>
            连接国网蓝牙
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.IsPointstr(System.String[],System.Int32,System.Collections.Generic.List{System.Int32})">
            <summary>
            表位频点检查
            </summary>
            <param name="arrMeter"></param>
            <param name="AisleNum"></param>
            <param name="ListMeter"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.GetCode(System.Int32,System.String)">
            <summary>
            获取功能代码
            </summary>
            <param name="type"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.B_Connect(System.Object)">
            <summary>
            连接蓝牙国网
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.button9_Click(System.Object,System.EventArgs)">
            <summary>
            清屏
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.InteractiveRadioAutoSetAddr(System.Object)">
            <summary>
            自动分配地址
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetPortParams(System.Object)">
            <summary>
            设置串口参数
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetSamePortPin(System.Object)">
            <summary>
            同端口引脚设置
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetNoSamePortPin(System.Object)">
            <summary>
            不同端口引脚设置
            </summary>
            <param name="o"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetErrPanelParam(System.Object)">
            <summary>
            设置误差检定参数
            </summary>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.GetErrorCurrent(System.Object)">
            <summary>
            查询当前误差
            </summary>
            <param name="meterIds"></param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.UpgradeEnabled(System.Object)">
            <summary>
            升级使能
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SoftwareReset(System.Object)">
            <summary>
            软件重启
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.WriteUpgradeData(System.Object)">
            <summary>
            升级数据
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.SetUAndIOutputLoop(System.Object)">
            <summary>
            设置电压电流输出回路
            </summary>
            <param name="s"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SourceControl.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SwitchCheck.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SwitchCheck.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.SwitchCheck.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp20.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp20.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp20.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp24.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp24.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp24.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp60.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp60.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp60.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp72.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp72.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp72.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp96.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp96.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.DataGridViewForTemp96.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.Temperature.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.Temperature.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.Temprature.Temperature.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCCDControl.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCCDControl.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCCDControl.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.Index">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.ManuCode">
            <summary>
            资产编号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.CheckUser">
            <summary>
            检定员
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.PrintTime">
            <summary>
            打印时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.GetOutData">
            <summary>
            获取输出数据
            </summary>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcCreateLableData.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.Id">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.MeterNo">
            <summary>
            表条码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.StateStr">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.NotPassItems">
            <summary>
            不合格项
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.FontColor">
            <summary>
            设置结论字体颜色
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.FontBarCodeColor">
            <summary>
            设置输入框字体
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.CurrentImage">
            <summary>
            当前图片
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.UcMeterInfo.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.nowMeter">
            <summary>
            要保存的表类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.p">
            <summary>
            配置文件管理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.GetInstance">
            <summary>
            配置文件管理实例化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.LoadParams">
            <summary>
            从配置文件加载控件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.SaveSystemConfig">
            <summary>
            保存系统参数配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.LoadSystemConfig">
            <summary>
            加载系统配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.SaveParams">
            <summary>
            保存数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParams.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.FormTitleText">
            <summary>
            窗体标题文本
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.SaveParams">
            <summary>
            保存配置参数,可重载
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.CancelParams">
            <summary>
            取消参数设置,可重载
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Assistant.PrjUI.XMLParamsFormBase.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.SubItemId">
            <summary>
            一个试验项目的小项
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.PaintColumnHeaderEventHandler">
            <summary>
            委托
            </summary>
            <param name="trlNo"></param>
            <param name="itemId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.GetInstance">
            <summary>
            静态实例初始化函数
            </summary>
            <returns>静态实例</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.dgvDataView_RefreshData(System.Int32,CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.String)">
            <summary>
            刷新检定数据
            </summary>
            <param name="rowIndex">检定方案行索引</param>
            <param name="trlNo">检定功能代码</param>
            <param name="itemId">检定功能中检定项编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.dgvDataView_Refresh_Run(System.String,System.String)">
            <summary>
            刷新特定数据
            </summary>
            <param name="Column0"></param>
            <param name="Column1"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.dgvDataView_PaintColumnHeader(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.String)">
            <summary>
            重画DataGridView列头
            </summary>
            <param name="trlNo">检定功能代码</param>
            <param name="itemId">检定功能中检定项编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.PaintColumnHeaderInvoked(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.String)">
            <summary>
            重绘表头  // 陈大伟 2011-8-20
            </summary>
            <param name="trlNo"></param>
            <param name="itemId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.dgvDataView_AddRow(System.Int32,System.Int32,System.Collections.ArrayList,System.Boolean,System.Boolean,CLDC.Framework.DataModel.Enum.EmRegCheckMode)">
            <summary>
            增加一行数据(走字)
            </summary>
            <param name="nItemId">当前需要增加的检定项序号</param>
            <param name="nRowIndex">行号索引(从零开始的下标)</param>
            <param name="arrRowValues">行值集合，各检定项的值排列见WORD文档</param>
            <param name="isStYard">是否起码数据</param>
            <param name="isPulse">是否脉冲数据</param>
            <param name="mode">走字模型</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckData.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.SetPowerVisible(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            是否显示累计电量和累计脉冲(true-显示)
            </summary>
            <param name="trialType">试验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.SetMonitor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            更新监视器消息
            </summary>   
            <param name="monitorData">标准表监视数据</param>
            <param name="ua_Ia_Angle">A相电压与A相电流之间的夹角</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.SetPowerPulse(System.UInt64,System.Single)">
            <summary>
            更新累计电量和脉冲
            </summary>
            <param name="pulse">累计脉冲</param>
            <param name="power">累计电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorACV.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.SetPowerVisible(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            是否显示累计电量和累计脉冲(true-显示)
            </summary>
            <param name="trialType">试验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.SetMonitor(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData,System.Single)">
            <summary>
            更新监视器消息
            </summary>   
            <param name="monitorData">标准表监视数据</param>
            <param name="ua_Ia_Angle">A相电压与A相电流之间的夹角</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.SetPowerPulse(System.UInt64,System.Single)">
            <summary>
            更新累计电量和脉冲
            </summary>
            <param name="pulse">累计脉冲</param>
            <param name="power">累计电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitorUniPhase.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.OnLoadedEventHandler">
            <summary>
            加载方案委托
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.DataViewRefreshedEventHandler">
            <summary>
            数据视图刷新委托
            </summary>
            <param name="rowIndex">当前行号</param>
            <param name="funcNo">功能代号</param>
            <param name="itemId">检定点ID</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.RunRefreshedEventHandler">
            <summary>
            数据视图刷新委托
            </summary>
            <param name="Column0">当前行号</param>
            <param name="Column1">功能代号</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.DataDetailRefreshedEventHandler">
            <summary>
            数据视图刷新委托
            </summary>
            <param name="rowIndex">当前行号</param>
            <param name="funcNo">功能代号</param>
            <param name="itemId">检定点ID</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.DataViewRefreshed">
            <summary>
            数据视图刷新事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.RunRefreshed">
            <summary>
            数据视图刷新事件
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.CheckItemCount">
            <summary>
            检定方案项个数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.SelectedRowIndex">
            <summary>
            Gets or sets the index specifying the currently selected row. 
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.FunCode">
            <summary>
            方案检定点
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.CurrentIndexId">
            <summary>
            获取当前检定项序号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.CurrentItemId">
            <summary>
            获取当前检定项目点
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.ItemRowCount">
            <summary>
            获取检定点数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.TreeCount">
            <summary>
            获取总检定点数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.GetInstance">
            <summary>
            静态实例初始化函数
            </summary>
            <returns>静态实例</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.#ctor">
            <summary>
            初始化函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.SystemEvent_SchemeTreeColor(System.Int32,System.DateTime,System.DateTime)">
            <summary>
            改变试验项目方案颜色
            </summary>
            <param name="index">当前检定项索引</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.LoadCheckItem(System.Windows.Forms.TreeView,System.Int32)">
            <summary>
            加载方案
            </summary>
            <param name="treeView"></param>
            <param name="m_TreeNode"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.TreeSolution_AfterSelect(System.Object,System.Windows.Forms.TreeViewEventArgs)">
            <summary>
            当前选中项触发事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.CheckControl(System.Windows.Forms.TreeNode)">
            <summary>
            选择节点状态
            </summary>
            <param name="node"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.CheckAllChildNodes(System.Windows.Forms.TreeNode,System.Boolean)">
            <summary>
            改变所有子节点的状态
            </summary>
            <param name="pn"></param>
            <param name="IsChecked"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.UseChildNodes(System.Windows.Forms.TreeNode)">
            <summary>
            总可用节点
            </summary>
            <param name="pn"></param>
            <param name="IsChecked"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.InitCheckAllChildNodes(System.Windows.Forms.TreeNode,System.Boolean)">
            <summary>
            改变所有子节点的状态
            </summary>
            <param name="pn"></param>
            <param name="IsChecked"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.AllCheckTreeBox">
            <summary>
            选中所有节点
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.dgvSolution_RowsAdded(System.Object,System.Windows.Forms.DataGridViewRowsAddedEventArgs)">
            <summary>
            触发增加行事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.DataGridView_Clear">
            <summary>
            清空数据视图
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.ClearDictionary">
            <summary>
            Clears the data dictionary.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmCheckItem.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.SetPowerVisible(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            是否显示累计电量和累计脉冲(true-显示)
            </summary>
            <param name="trialType">试验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.SetMonitor(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData,System.Single)">
            <summary>
            更新监视器消息
            </summary>   
            <param name="monitorData">标准表监视数据</param>
            <param name="ua_Ia_Angle">A相电压与A相电流之间的夹角</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.SetPowerPulse(System.UInt64,System.Single)">
            <summary>
            更新累计电量和脉冲
            </summary>
            <param name="pulse">累计脉冲</param>
            <param name="power">累计电量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmMonitor.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.trNowTime_Tick(System.Object,System.EventArgs)">
            <summary>
            显示累计运行时长
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.LoadDeskParam">
            <summary>
            加载检定台体参数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.frmTopAuto.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.Id">
            <summary>
            唯一标识
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.MeterNo">
            <summary>
            表条码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.StateStr">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.NotPassItemName">
            <summary>
            不合格项
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.FontColor">
            <summary>
            设置结论字体颜色
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.FontBarCodeColor">
            <summary>
            设置输入框字体
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.CurrentImage">
            <summary>
            当前图片
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Inlay.UcMeterInfo.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.VisibleColumns">
            <summary>
            返回检定数据可视列
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterCollection">
            <summary>
            返回待检定表(选中的)集合
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.SelectedItemIndex">
            <summary>
            当前选中的检定项序号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.DataGridViewBase_Load(System.Object,System.EventArgs)">
            <summary>
            加载数据视图
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.topLeftCheckBox_CheckStateChanged(System.Object,System.EventArgs)">
            <summary>
            数据视图CheckBox事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_RowHeaderMouseClick(System.Object,System.Windows.Forms.DataGridViewCellMouseEventArgs)">
            <summary>
            行表头鼠标事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_RowsAdded(System.Object,System.Windows.Forms.DataGridViewRowsAddedEventArgs)">
            <summary>
            增加新行数据
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_AddCellValue(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            增加单元格数据
            </summary>
            <param name="nItemId">当前需要增加的检定项序号</param>
            <param name="nColIndex">列号索引(从零开始的下标)</param>
            <param name="nRowIndex">行号索引(从零开始的下标)</param>
            <param name="strCellValue">单元格值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_RefreshCellValue(System.Int32)">
            <summary>
            刷新单元格式数据
            </summary>
            <param name="nItemId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_AddRow(System.Int32,System.Int32,System.Collections.ArrayList)">
            <summary>
            增加一整行数据
            </summary>
            <param name="nItemId">当前需要增加的检定项序号</param>
            <param name="nRowIndex">行号索引(从零开始的下标)</param>
            <param name="arrRowValues">行值集合，各检定项的值排列见WORD文档</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_AddRow_2(System.Int32,System.Int32,System.Collections.ArrayList)">
            <summary>
            增加一整行数据
            </summary>
            <param name="nItemId">当前需要增加的检定项序号</param>
            <param name="nRowIndex">行号索引(从零开始的下标)</param>
            <param name="arrRowValues">行值集合，各检定项的值排列见WORD文档</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_GridColor(System.Drawing.Color)">
            <summary>
            设置数据视图窗格颜色
            </summary>
            <param name="gridColor"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_RowsInit(System.Int32)">
            <summary>
            初始化数据视图行数
            </summary>
            <param name="rowCount">行数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_Scroll(System.Object,System.Windows.Forms.ScrollEventArgs)">
            <summary>
            拖动滚动条
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_RefreshData(System.Int32,CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.String)">
            <summary>
            刷新检定数据
            </summary>
            <param name="rowIndex">检定方案行索引</param>
            <param name="funcNo">检定功能代码</param>
            <param name="itemId">检定功能中检定项编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_ResetColumnHeader(System.String[])">
            <summary>
            重新设置dataGridView列表头的显示列
            </summary>
            <param name="strHeaderTexts">表头文本</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_ResetColumnWidth(System.Int32[])">
            <summary>
            重新设置dataGridView列表头的SizeMode
            </summary>
            <param name="nFillWeights">宽度填充比例</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterInfo_Load(System.Collections.Generic.List{System.Int32})">
            <summary>
            加载待检表
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterEventHandler">
            <summary>
            插入表信息委托
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.LoadingInvoked(System.Collections.Generic.List{System.Int32})">
            <summary>
            插入表信息回调方法
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterInfo_AutoLoad(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StMeterCheckState})">
            <summary>
            加载待检表
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterAutoEventHandler">
            <summary>
            插入表信息委托
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterInfoLoadInvoked(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StMeterCheckState})">
            <summary>
            插入表信息回调方法
            </summary>
            <param name="lstMeterId">表位号集合</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.UnCheckEventHandler">
            <summary>
            隔离表位去掉勾
            </summary>
            <param name="meterId">要隔离的表位</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.UnCheckMeterId(System.Collections.Generic.List{System.Int32})">
            <summary>
            隔离表位去掉勾
            </summary>
            <param name="meterId">要隔离的表位集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.MeterIdUnCheckInvoked(System.Collections.Generic.List{System.Int32})">
            <summary>
             隔离表位去掉勾
            </summary>
            <param name="meterID">要隔离的表位集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.dgvDataView_Clear">
            <summary>
            清空当前视图数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.DataTable_ClearAll">
            <summary>
            做连续检定前，清空所有检定结果
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.DataTable_Clear(System.Int32)">
            <summary>
            清空表格所有数据，待新数据插入
            </summary>
            <param name="itemId">检定项序号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.DataCollection_Init(System.Int32,System.Int32)">
            <summary>
            初始化所有检定结果
            </summary>
            <param name="itemCount">检定项数量</param>
            <param name="meterCount">待检表数量</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.DataGridViewBase.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.frmViewLayout.pgrid_Layout_PropertyValueChanged(System.Object,System.Windows.Forms.PropertyValueChangedEventArgs)">
            <summary>
            
            </summary>
            <param name="s"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.frmViewLayout.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.frmViewLayout.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.frmViewLayout.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmAbout.FrmAbout_Load(System.Object,System.EventArgs)">
            <summary>
            
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmAbout.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmAbout.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmAbout.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmTech.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmTech.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.PrjUI.Help.FrmTech.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.frmTester">
            <summary>
            检定工位界面
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.frmTester.cameraCount">
            <summary>
            相机总数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.frmTester.meterCount">
            <summary>
            表位总数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.frmTester.BCM">
            <summary>
            检定系统
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.SetTitle">
            <summary>
            根据配置文件加载不同的界面Title
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.ReadSystemConfig">
            <summary>
            读配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.DeleLogFile">
            <summary>
            删除上一月日志文件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.IsShowOriginalErroe">
            <summary>
            是否显示主副表原有误差操作按钮
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.IsShowAcvMonitor">
            <summary>
            是否显示耐压监视操作按钮
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.SystemEvent_ConnControlState(System.Boolean)">
            <summary>
            连接主控图标改变
            </summary>
            <param name="isconn"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.InitCheckCell">
            <summary>
            加载检定系统
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.DisposeCheckCell">
            <summary>
            释放检定系统
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.LoadMeterSheme">
            <summary>
            加载表信息和方案信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.ReceivedAVCMonitor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            耐压数据
            </summary>
            <param name="u"></param>
            <param name="i"></param>
            <param name="a"></param>
            <param name="z"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.SystemEvent_EventSendTrialDataToUI(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            从数据加载数据到界面
            </summary>
            <param name="LstTrialData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.ReceivedTrialData(CLDC.CLAT.CLWBS.DataModel.EventMessage.TrialDataArgs)">
            <summary>
            接受业务实时数据
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.UnCheckMeter(System.Collections.Generic.List{System.Int32})">
            <summary>
            需要隔离的表位去掉勾
            </summary>
            <param name="lstMeterId">需要隔离的表位</param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.frmTester.lockrefForm">
            <summary>
            锁
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.refrunform(System.Int32)">
            <summary>
            刷新运行中的界面
            </summary>
            <param name="trialtype"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.RefFrom(System.Int32)">
            <summary>
            刷新检定界面
            </summary>
            <param name="trialtype">实验类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.StarStepAll(System.Boolean)">
            <summary>
            自动连续检测
            </summary>
            <param name="isBluetoothInit">是否蓝牙初始化</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.tsbtn_Tester_Pause_Click(System.Object,System.EventArgs)">
            <summary>
            暂停和继续
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.toolStripSplitButton1_ButtonClick(System.Object,System.EventArgs)">
            <summary>
            控源
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.tsbtn_DownLoadSh_Click(System.Object,System.EventArgs)">
            <summary>
            方案主动申请
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.toolStripButton1_Click_1(System.Object,System.EventArgs)">
            <summary>
            本地数据查询
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.OnTurnOffComputer">
            <summary>
            关闭计算机
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.ContinueCheckItems">
            <summary>
            继续（待检）试验项目检定
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.toolStripButton2_Click(System.Object,System.EventArgs)">
            <summary>
            耐压监测
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.frmTester.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.frmTester.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.Program.Main">
            <summary>
            应用程序的主入口点。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.Properties.Resources">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Culture">
            <summary>
              重写当前线程的 CurrentUICulture 属性，对
              使用此强类型资源类的所有资源查找执行重写。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._1110">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._21">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._22">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._23">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._24">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._25">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._26">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._27">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._28">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._29">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._30">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._31">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._32">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources._lock">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Acv">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.cancel">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.cancel_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.ccd">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.cdz">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.clearKey">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.CLOU">
            <summary>
              查找类似于 (图标) 的 System.Drawing.Icon 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Clou1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Connected">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.DataSearch">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.DataSearch_1_">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Disconnected">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.display">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.down1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.ErrorCalibration">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.fanganguanli">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.keyUpdata">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.keyUpdata1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.keyUpdata11">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.label">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.login_bg">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.login_bg3">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.logon">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.logon_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.LogSearch_1_">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.meterdownload">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.MeterInfoDownLoad">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox11">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox12">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox13">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox21">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox22">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox23">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox27">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox28">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox29">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox3">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox4">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.pictureBox5">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.powerOf">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.powerOf_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.poweron">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.poweron_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.preplace">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.press">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.PushSwich">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.save">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.save_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.Sn_BackgroundImage">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.step">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.step_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.stepall">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.stepall_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.stop">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.stop_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.system">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.system_active">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle10">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle103">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle11">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle12">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle13">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle14">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle15">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle16">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle17">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle18">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle19">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle2">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle20">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle3">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle4">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle5">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle6">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle7">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle8">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.TesterTitle9">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.testTitle">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.undisplay">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.unlock">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.up">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.up1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.vsdown">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.vsup">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.图标">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.图标Clou">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.图片2">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.图片3">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.密钥下装">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.开始检测">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.打印标签">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.断开主控">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.断开主控1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.方案下载1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.暂停">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.最小化">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.自动检表线上下料工位">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.自动检表线条码扫描工位">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.自动选择否">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.自动选择是">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.表信息下载2">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.资源_2">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.资源_5">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.资源_51">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.载波设置">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.Properties.Resources.连接主控">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.UI.UIBase.FormBase">
            <summary>
            工位界面基类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox17">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox3">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox5">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox4">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox11">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox27">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox28">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox29">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox12">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox13">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox21">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox22">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ImagePictureBox23">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.Sn_BackgroundImage">
            <summary>
            背景属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.FormCaptionImage">
            <summary>
            窗体标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.WorkAreaCaption">
            <summary>
            工作区域标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExpHeight">
            <summary>
            信息栏高度属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp1Visible">
            <summary>
            第一个信息栏显示/隐藏属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp1Caption">
            <summary>
            第一个信息栏标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp1Height">
            <summary>
            第一个信息栏高度属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp2Visible">
            <summary>
            第二个信息栏显示/隐藏属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp2Caption">
            <summary>
            第二个信息栏标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp2Height">
            <summary>
            第二个信息栏高度属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp3Visible">
            <summary>
            第三个信息栏显示/隐藏属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.SubExp3Caption">
            <summary>
            第三个信息栏标题属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ToolsVisible">
            <summary>
            获取或设置一个值，该值指示工具栏显示/隐藏
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.IsWorkareaLocked">
            <summary>
            获取或设置一个值，该值指示工作区域是否锁定;ture,工作区域锁定
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.StatusVisible">
            <summary>
            获取或设置一个值，该值指示状态栏显示/隐藏;true,显示状态栏
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.PanelVisible">
            <summary>
            获取或设置一个值，该值指示运行模式面板显示/隐藏;true，显示运行模式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.RunModeText">
            <summary>
            获取或设置一个值，该值指示运行模式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ConnectToServer">
            <summary>
            获取或设置一个值,该值指示连接服务器状态;true,表示与服务器处于连接状态
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.RunningMessage">
            <summary>
            设置一个值,该值指示运行时的信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.ExitClick">
            <summary>
            退出按钮事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.HelpClick">
            <summary>
            帮助按钮事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.LockClick">
            <summary>
            锁定按钮事件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.FormBase.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.AboutFormBase.FormTitleText">
            <summary>
            窗体标题文本
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.AboutFormBase.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.AboutFormBase.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.AboutFormBase.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.FormTitleText">
            <summary>
            窗体标题文本
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.SaveParams">
            <summary>
            保存配置参数,可重载
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.CancelParams">
            <summary>
            取消参数设置,可重载
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UIBase.PrjUI.ParamsFormBase.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.AddItems(System.String,System.String,System.String,System.Boolean)">
            <summary>
            插入一条记录
            </summary>
            <param name="Name">方案名称</param>
            <param name="StartTime">开始时间</param>
            <param name="EndTime">结束时间</param>
            <param name="IsCheck">是否要检定</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.lstVaddItem(System.String,System.String,System.String,System.Boolean)">
            <summary>
            插入一条记录
            </summary>
            <param name="Name">方案名称</param>
            <param name="StartTime">开始时间</param>
            <param name="EndTime">结束时间</param>
            <param name="IsCheck">是否要检定</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.UpdateStartTime(System.String,System.String)">
            <summary>
            更新记录开始时间
            </summary>
            <param name="Name">方案名称</param>
            <param name="StartTime">开始时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.UpdateEndTime(System.String,System.String)">
            <summary>
            更新记录结束时间
            </summary>
            <param name="Name">方案名称</param>
            <param name="EndTime">结束时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.UpdateChecked(System.String,System.Boolean)">
            <summary>
            更新选中状态
            </summary>
            <param name="Name">方案名称</param>
            <param name="IsCheck">是否选中</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.lstSchemeInfo_ItemSelectionChanged(System.Object,System.Windows.Forms.ListViewItemSelectionChangedEventArgs)">
            <summary>
            单击事件外发
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.UI.UserControlsItem.UcSchemeItems.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
    </members>
</doc>
