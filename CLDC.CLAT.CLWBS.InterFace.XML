<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.InterFace</name>
    </assembly>
    <members>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EventUploadEnvironmentTemp">
            <summary>
            温湿度数据上传事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ReportEnvironmentTempSender">
            <summary>
            温湿度数据上报事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EnvironmentThAlarmSender">
            <summary>
            环境温湿度报警事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EventGetMeterInfo">
            <summary>
            上传表位状态数据
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ReportMsgSender">
            <summary>
            报文信息事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ErrorMsgSender">
            <summary>
            异常信息事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.TrialDataSender">
            <summary>
            试验数据事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.NoticeMsgSender">
            <summary>
            通知信息事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.RunStateCmdSender">
            <summary>
            暂停恢复命令事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.MonitorMsgSender">
            <summary>
            标准表监视器示值发送事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.MonitorNoticeMsgSender">
            <summary>
            流水日志事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendCheckItemID">
            <summary>
            当前检定ID事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EquipMentResultEvent">
            <summary>
            设备返回数据集事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.TemratureEquipMentResult">
            <summary>
            设备返回温度数据集事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ConnControlState">
            <summary>
            连接主控状态
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UIButton">
            <summary>
            界面按钮控制
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SchemeTreeColor">
            <summary>
            已检试验方案改变颜色
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ImageReslut">
            <summary>
            图像事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ImageCheckResult">
             <summary>
            图片检测结果事件 
             </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.MsgToEquip">
            <summary>
            底层数据发送到设备事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EventLocation">
            <summary>
            发送坐标宽度和高度
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ComName">
            <summary>
            界面发送端口名
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendToControlCenterErrMsg">
            <summary>
            与主控通信，错误信息
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendToControlCenterWithoutQueue">
            <summary>
            监视信息
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ApplyScheme">
            <summary>
            手动获取方案
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SaveAndUploadData">
            <summary>
            保存并上传数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SaveAndUploadDataFun">
            <summary>
            保存并上传数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EnvironmentThAlarm">
            <summary>
            环境温湿度报警
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ReceiveTemp(System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Object}})">
            <summary>
            接收温度数据
            </summary>
            <param name="temp"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ReportEnvironmentTemp(System.Single,System.Single)">
            <summary>
            上传上报环境温湿度数据
            </summary>
            <param name="temp"></param>
            <param name="hum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadEnvironmentTemp(System.String)">
            <summary>
            上传环境温湿度数据
            </summary>
            <param name="datastr"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetMeterInfo">
            <summary>
            获取表位状态数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendAppleyScheme(System.Int32,System.String)">
            <summary>
            手动获取方案
            </summary>
            <param name="SchemeId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendControlMsgDataWithoutQueue(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            
            </summary>
            <param name="monitorData">监视器信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendControlErrMsgData(System.String,System.String,System.Int32,System.String)">
            <summary>
            发送错误警告运行信息到主控
            </summary>
            <param name="code">日志编码</param>
             <param name="expandMsg">扩展信息</param>
             <param name=" sys">所属系统</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ApplyMeterInfoByMeterNos(System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            根据条码申请表信息
            </summary>
            <param name="Info">表位号，表条码</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutTrialDataEvent">
            <summary>
            业务外发试验结果
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            业务外发试验结果
            </summary>
            <param name="lstTrialData">试验结果集</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutPlanBModeTrialDataEvent">
            <summary>
            业务外发试验结果
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutPlanBModeTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            业务B模式外发试验结果
            </summary>
            <param name="lstTrialData">试验结果集</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutComprehensiveTrialDataEvent">
            <summary>
            业务外发综合试验结果
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutComprehensiveTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
             业务外发综合试验结果
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetAuthorizationCodeCenter">
            <summary>
            获取授权码
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetAuthorizationCode">
            <summary>
            获取授权码
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EnterAdministratorModeCenter">
            <summary>
            进入管理员模式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.EnterAdministratorMode(System.String)">
            <summary>
            进入管理员模式
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutCardInfoToCenter">
            <summary>
            业务外发信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutCardInfo(CLDC.Framework.DataModel.Enum.EmPlugCardType,CLDC.Framework.DataModel.Enum.EmRunState)">
            <summary>
            业务外发信息
            </summary>
            <param name="CardType">类型</param>
            <param name="RunState">状态信息</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutStateInfoToCenter">
            <summary>
            向外发送状态信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutStateInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            向外发送状态信息
            </summary>
            <param name="stationState">状态信息集合</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutActionTypeInfoToCenter">
            <summary>
            向外发送动作信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutActionInfo(CLDC.CLAT.CLWBS.DataModel.Enum.EmActionType)">
            <summary>
            向外发送动作信息
            </summary>
            <param name="stationState">状态信息集合</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.StartCarveVerifyEvent">
            <summary>
            开始雕刻验证事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.StartCarveVerify">
            <summary>
            开始雕刻验证事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadCCDImage(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            上传外观图片
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendStartCheckImage">
            <summary>
            开始通知图像处理软件检测
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendBitmapToProcess">
            <summary>
            图像上传处理层
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendBitmapProcess(System.Int32,HalconDotNet.HObject)">
            <summary>
            图像上传处理层
            </summary>
            <param name="carmraID">相机ID</param>
            <param name="bitmap">图像</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ProcessSendToBusiness">
            <summary>
            图像处理层告知业务可以移位
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendToBusiness(System.String)">
            <summary>
            图像处理层告知业务可以移位
            </summary>
            <param name="meterBarCode">表条码号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendComText(System.Int32,System.String,System.String)">
            <summary>
            界面发送端口名
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendLocationAndWH(System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.Int32,System.String)">
            <summary>
            发送坐标
            </summary>
            <param name="x">X坐标</param>
            <param name="y">Y坐标</param>
            <param name="h">高度</param>
            <param name="w">宽度</param>
            <param name="BName">父控件名称</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendMsgToEquip(System.String)">
            <summary>
            底层数据发送到设备
            </summary>
            <param name="Msg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendResultFormProcess(System.Boolean,System.String)">
            <summary>
            图像结果
            </summary>
            <param name="Result">结果</param>
            <param name="MeterBarCode">表条码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendImageCheckResult(System.String)">
            <summary>
            图像检测结果
            </summary>
            <param name="Result">图像检测是否成功</param>
            <param name="ErrorMsg">图像检测失败原因</param>
            <param name="CheckData">图像检测成功数据，失败时为空</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SetUIButton(System.Boolean)">
            <summary>
            设置界面按钮
            </summary>
            <param name="IsShow"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendConnStateToUi(System.Boolean)">
            <summary>
            连接主控状态
            </summary>
            <param name="isconn"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendAvcData(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            发送耐压屏幕数据
            </summary>
            <param name="u"></param>
            <param name="i"></param>
            <param name="a"></param>
            <param name="z"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendEquipMentResult(CLDC.CLAT.CLWBS.DataModel.Struct.StResultData)">
            <summary>
            设备数据事件发送方法
            </summary>
            <param name="Result">结果集</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendTemratureEquipMentResult(CLDC.CLAT.CLWBS.DataModel.Struct.StResultData)">
            <summary>
            设备温度数据事件发送方法
            </summary>
            <param name="Result">结果集</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendSchemeToUIEvent(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Boolean,System.Boolean,System.Int32)">
            <summary>
            发送方案到UI
            </summary>
            <param name="TrialScheme">方案集合</param>
            <param name="isUpdateScheme">是否更新方案</param>
            <param name="selectNodeIndex">选择的节点索引</param>
            <param name="flag">标志true-加载方案中的时间，false-不加载时间</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendMeterInfoToUIEvent(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            发送表信息到UI
            </summary>
            <param name="MeterInfo"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendCheckID(System.Int32,System.Boolean,System.DateTime,System.DateTime,System.Boolean)">
            <summary>
            发送当前检定ID到UI
            </summary>
            <param name="id">实验项目索引</param>
            <param name="isClick">该实验项目是否被选中</param>
            <param name="startTime">实验项目开始时间</param>
            <param name="endTime">实验项目结束时间</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendTrialDataToUIEvent(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            发送试验数据集(加载试验数据到界面显示)
            </summary>
            <param name="LstTrialData"></param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UnCheckMeterIdEvent">
            <summary>
            不要检的表去掉勾
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendUnCheckMeterIdToUI(System.Collections.Generic.List{System.Int32})">
            <summary>
            隔离的表去掉界面上的勾
            </summary>
            <param name="lstMeterId">需要隔离表位号集合</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendSwitchCheckEvent">
            <summary>
            启动远程拉合闸人工确认窗体事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendSwitchCheck">
            <summary>
            启动远程拉合闸人工确认窗体
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendSwitchCheckResultEvent">
            <summary>
            发送远程拉合闸人工确认结果事件
            </summary>
            <param name="checkResult"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendSwitchCheckResult(System.Object)">
            <summary>
            发送远程拉合闸人工确认结果
            </summary>
            <param name="result"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UpLoadPictureEvent">
            <summary>
            不合格图片上传事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadPicture(CLDC.CLAT.CLWBS.DataModel.Class.ClBuHeGePicture)">
            <summary>
            上传不合格图片
            </summary>
            <param name="picture"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadDeviceEvent">
            <summary>
            上传设备状态信息事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadDevice(CLDC.CLAT.CLWBS.DataModel.Class.ClDeviceState)">
            <summary>
            上传设备状态信息
            </summary>
            <param name="device"></param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.LightMeterLed">
            <summary>
            点亮LED屏事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.LightMeterLED">
            <summary>
            点亮LED屏
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.BindCarveInfoEvent">
            <summary>
            表条码与雕刻信息绑定事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.BindCarveInfo(System.String,System.String)">
            <summary>
            表条码与雕刻信息绑定
            </summary>
            <param name="meterNO"></param>
            <param name="carveInfo"></param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.TurnOffComputerSender">
            <summary>
            关闭计算机命令事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.TurnOffComputerCommand">
            <summary>
            关机命令，通知到主界面进行界面更新、关源等准备工作后再强制关闭电脑
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ContinueCheckItemsSender">
            <summary>
            继续检测（待检）试验项目
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ContinueCheckItemsCommand">
            <summary>
            继续（待检）试验项目检定
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetEquipEleSealEventHandler">
            <summary>
            电子封印信息查询事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetEquipEleSeal">
            <summary>
            获取电子封印信息
            </summary>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UpLoadEquipEleSealEventHandler">
            <summary>
            电子封印结果上传事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UpLoadEquipEleSeal(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.EquipEleSealUpLoadArg})">
            <summary>
            上传电子封印结果
            </summary>
            <param name="lstEquipEleSealUpLoadArg">电子封印结果</param>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetEquipEleLabelEventHandler">
            <summary>
            电子封印信息查询事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.GetEquipEleLabel">
            <summary>
            获取电子封印信息
            </summary>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UpLoadEquipEleLabelEventHandler">
            <summary>
            电子封印结果上传事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UpLoadEquipEleLabel(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.UpLoadEquipEleLabelArg})">
            <summary>
            上传电子封印结果
            </summary>
            <param name="lstEquipEleLabelUpLoadArg">电子封印结果</param>
            <returns></returns>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadEquipCarveCodeEventHandler">
            <summary>
            雕刻认证结果上传事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.UploadEquipCarve(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.UploadEquipCarveReqArg})">
            <summary>
            上传雕刻认证数据结果
            </summary>
            <param name="uploadEquipCarveReqArgs">雕刻验证数据结果</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.ChipIDAuthentication(CLDC.CLAT.CLWBS.DataModel.Struct.ChipIDAuthentication)">
            <summary>
            芯片ID认证
            </summary>
            <param name="chipID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.MoveMeterPosition(System.Int32)">
            <summary>
            移动表位
            </summary>
            <param name="meterId">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutReportMessage(CLDC.CLAT.CLWBS.DataModel.EventMessage.ReportMsgArgs)">
            <summary>
            报文数据发送器
            </summary>
            <param name="sender">发送者</param>
            <param name="rptArgs">报文数据内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutExceptionMessage(CLDC.CLAT.CLWBS.DataModel.EventMessage.ExceptionArgs)">
            <summary>
            异常信息发送器
            </summary>
            <param name="sender">发送者</param>
            <param name="errArgs">异常信息内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutDataMessage(CLDC.CLAT.CLWBS.DataModel.EventMessage.TrialDataArgs)">
            <summary>
            试验数据发送器
            </summary>
            <param name="sender">发送者</param>
            <param name="dataArgs">试验数据内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutNoticeMessage(CLDC.CLAT.CLWBS.DataModel.EventMessage.NoticeMsgArgs)">
            <summary>
            通知信息发送器
            </summary>
            <param name="sender">发送者</param>
            <param name="noticeArgs">通知信息内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutMonitorMessage(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData,System.Single)">
            <summary>
            标准表监视数据发送器
            </summary>
            <param name="sender">发送者</param>
            <param name="monitorArgs">要发送数据信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.OutMonitorNoticeMsg(System.String)">
            <summary>
            流水日志数据发送器
            </summary>
            <param name="Msg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendNoticeMessage(System.Int32)">
            <summary>
            外发检定消息[检定完成消息]
            </summary>
            <param name="itemId">检定项ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendNoticeMessage(System.Int32,System.String)">
            <summary>
            外发检定消息[检定完成消息]
            </summary>
            <param name="itemId">检定项ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendNoticeMessage(System.String)">
            <summary>
            外发检定消息[提示消息]
            </summary>
            <param name="hintMsg">提示消息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendRunStateCommand">
            <summary>
            外发检定消息[提示消息]
            </summary>
            <param name="hintMsg">提示消息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="errorId">错误编码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32,System.DateTime)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="errorId">错误编码</param>
            <param name="arsTime">异常发生时刻</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32,System.String)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="errorId">错误编码</param>
            <param name="errorMsg">错误信息内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32,System.String,System.DateTime)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="errorId">错误编码</param>
            <param name="errorMsg">错误信息内容</param>
            <param name="arsTime">异常发生时刻</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32,System.Int32,System.String)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="meterId">表位号</param>
            <param name="errorId">错误编码</param>
            <param name="errorMsg">错误信息内容</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendErrorMessage(System.Int32,System.Int32,System.String,System.DateTime)">
            <summary>
            外发检定消息[异常信息]
            </summary>
            <param name="meterId">表位号</param>
            <param name="errorId">错误编码</param>
            <param name="errorMsg">错误信息内容</param>
            <param name="arsTime">异常发生时刻</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendDataMessage(System.String,System.Int32,System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmTrialConclusion,CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.Decimal,System.Decimal)">
            <summary>
            外发检定消息[试验数据]
            </summary>
            <param name="meterNo">表条码号</param>
            <param name="meterId">表位号(1,2...)</param>
            <param name="trialValue">试验数据</param>
            <param name="roundValue">试验数据化整值</param>
            <param name="pristValues">试验原始数据列表</param>
            <param name="trialType">试验项目类型</param>
            <param name="result">试验结论</param>
            <param name="extentValue">试验项目扩展数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.InterFace.SystemEvent.SendMonitorMessage(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            外发检定消息[监视器数据]
            </summary>
            <param name="monitorData">监视器数据结构</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ApplyMeterInfoAndSchemeInfo">
            <summary>
            手动申请表信息方案信息
            </summary>
            <param name="lstTrialData">试验结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendErrorMsg">
            <summary>
            异常信息委托
            </summary>
            <param name="sender">发送者</param>
            <param name="e">异常信息</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendDataMsg">
            <summary>
            试验数据委托
            </summary>
            <param name="sender">发送者</param>
            <param name="e">试验数据</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendNoticeMsg">
            <summary>
            通知信息委托
            </summary>
            <param name="sender">发送者</param>
            <param name="e">提示信息</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendRunStateCmd">
            <summary>
            通知信息委托OnSendRunStateCmd
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnTurnOffComputerCmd">
            <summary>
            通知信息委托TurnOffComputerCmd
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnContinueCheckItemsCmd">
            <summary>
            通知信息委托ContinueCheckItemsCmd
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendMonitorMsg">
            <summary>
            标准表监视器示值
            </summary>
            <param name="sender">发送者</param>
            <param name="e">标准表参数</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendMonitorNoticeMsg">
            <summary>
            界面流水日志信息
            </summary>
            <param name="sender"></param>
            <param name="Msg"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.OnSendCheckItemID">
            <summary>
            发送检定系统当前检定项目ID号
            </summary>
            <param name="ID"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ResultEventHandler">
            <summary>
            设备返回结果数据集
            </summary>
            <param name="st">设备结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.TempratureResultEventHandler">
            <summary>
            设备返回温度结果数据集
            </summary>
            <param name="st">设备结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendToCenterWithoutQueue">
            <summary>
            发送试验结果    不入队
            </summary>
            <param name="trialdata">结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendToCenterErrMsg">
            <summary>
            发送警告信息    入队列
            </summary>
            <param name="trialdata">结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendTrialDataToCenter">
            <summary>
            业务向外发送试验结果集
            </summary>
            <param name="lstTrialData">试验结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendPlanBModeTrialDataToCenter">
            <summary>
            业务向外发送B模式试验结果集
            </summary>
            <param name="lstTrialData">试验结果集</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendComprehensiveTrialDataToCenter">
            <summary>
            / 业务向外发送综合试验结果集
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="SchemelData">方案信息集合</param>
            <param name="lstTrialstTrial">试验数据集合</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendStateInfoToCenter">
            <summary>
            向外发送状态信息
            </summary>
            <param name="stationState">状态</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendActionTypeInfoToCenter">
            <summary>
            向外发送动作信息
            </summary>
            <param name="emActionType"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendCardInfoToCenter">
            <summary>
            业务向外发送信息
            </summary>
            <param name="CardType">类型</param>
            <param name="RunState">状态</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.GetAuthorizationCode">
            <summary>
            获取授权码
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.EnterAdministratorMode">
            <summary>
            进去管理员模式
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendUiConnControl">
            <summary>
            连接主控状态
            </summary>
            <param name="isconn"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SetUIButton">
            <summary>
            设置界面按钮
            </summary>
            <param name="ShowOrVis"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.StartCarveVerify">
            <summary>
            开始雕刻验证
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UploadCCDImage">
            <summary>
            上传CCD图片
            </summary>
            <param name="data"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendAvcMData">
            <summary>
            发送耐压仪数据
            </summary>
            <param name="u">电压</param>
            <param name="i">电流</param>
            <param name="a"></param>
            <param name="z"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ChangeSchemeTreeColor">
            <summary>
            已检方案改变颜色
            </summary>
            <param name="index">当前在检的方案索引</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendInPutMeterInfo">
            <summary>
            发送表信息临检表
            </summary>
            <param name="MeterInfo">表信息</param>
            <param name="TepMeterInfo">集合表信息</param>
            <param name="NotPassItems">数据</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendImageResult">
            <summary>
            发送图像处理表结果
            </summary>
            <param name="Result"></param>
            <param name="MeterBarCode"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendMsgSocketToEquip">
            <summary>
            底层数据发送到设备
            </summary>
            <param name="RecvData"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendLocation">
            <summary>
            发送坐标和高度宽度
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <param name="h"></param>
            <param name="w"></param>
            <param name="BName">父控件名称</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendComName">
            <summary>
            发送端口名
            </summary>
            <param name="ComName"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendBitmapToProcess">
            <summary>
            相机事件获得图像，上传给处理层事件
            </summary>
            <param name="bmp"></param>
            <param name="cameraID"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ProcessSendToBusiness">
            <summary>
            图像处理层告知业务可以移位
            </summary>
            <param name="bmp"></param>
            <param name="cameraID"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UnCheckMeterIdEventHandler">
            <summary>
            发送不要检的表的表位号
            </summary>
            <param name="UnCheckMeterId"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendSwitchCheckEventHandler">
            <summary>
            启动远程拉合闸人工确认窗体
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendFailMeterInfos">
            <summary>
            上传检定失败表信息
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SendSwitchCheckResultEventHandler">
            <summary>
            发送远程拉合闸人工确认结果
            </summary>
            <param name="checkResult"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UploadBuHeGePicture">
            <summary>
            上传不合格图片
            </summary>
            <param name="picture"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.LightMeterLed">
            <summary>
            相机收到照片后点亮下一表位的LED屏
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.BindCarveInfoEventHandler">
            <summary>
            将拍到的二维码雕刻信息与表绑定
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UploadEnvironmentTemp">
            <summary>
            上传环境温湿度数据
            </summary>
            <param name="datastr"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ReportEnvironmentTemp">
            <summary>
            上报环境温湿度数据
            </summary>
            <param name="datastr"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.GetMeterInfo">
            <summary>
            获取表位状态信息
            </summary>
            <param name="meterId"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.EnvironmentThAlarm">
            <summary>
            环境温湿度报警
            </summary>
            <param name="meterId"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.GetEquipEleSealEventHandler">
            <summary>
            电子封印信息查询
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UpLoadEquipEleSealEventHandler">
            <summary>
            电子封印结果上传
            </summary>
            <param name="lstEquipEleSealUpLoadArg">电子封印结果数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.GetEquipEleLabelEventHandler">
            <summary>
            电子标签写入信息查询
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UpLoadEquipEleLabelEventHandler">
            <summary>
            电子标签结果上传
            </summary>
            <param name="lstEquipEleLabelUpLoadArg">电子标签写入结果数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.UploadEquipCarveCodeEventHandler">
            <summary>
            
            </summary>
            <param name="uploadEquipCarveReqArgs">雕刻写入结果数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.ChipIDAuthenticationEventHandler">
            <summary>
            芯片ID认证
            </summary>
            <param name="chipID"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.SaveAndUploadData">
            <summary>
            保存并上传数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.StartCheckImage">
            <summary>
            图像处理软件开始检测
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.GetAppearanceResultEventHandler">
            <summary>
            获取（MES）外观结论
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.InterFace.MoveMeterPosition">
            <summary>
            移动表位
            </summary>
            <param name="meterId">表位号</param>
        </member>
    </members>
</doc>
