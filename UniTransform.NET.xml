<?xml version="1.0"?>
<doc>
    <assembly>
        "UniTransform.NET"
    </assembly>
    <members>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMImageTransform_3x3(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert AVT interlaced Raw8 to variable image format with 3x3 filtering </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source AVT interlaced Raw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert AVT interlaced Raw8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source AVT interlaced Raw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMImageTransform_Bin(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert binned AVT interlaced Raw8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source binned AVT interlaced Raw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)">
            <summary> 
    Convert several Raw16 format variations to arbitrary image formats.
</summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source Y16 image </param>
            <param name="eImageFormat"> requested destination image format</param>
            <param name="FormatInfo"> detailed input format specification</param>
            <remarks>
                <paramref name="Dst" /> is expected to be appropriately initialized before function call.
    Image dimensions need to match source image size.
    The following set of PixelFormats is supported (given the according destination format 
    <paramref name="eImageFormat" />, quoted in parentheses, is requested):
    <list type="bullet"><item>Format8bppIndexed (MonoRec601/MonoRec709)</item><item>Format24bppRgb (BGR24)</item><item>Format32bppRgb (BGRA32)</item><item>Format32bppArgb (BGRA32)</item><item>Format16bppGrayScale (Mono16Rec601/Mono16Rec709)</item><item>Format48bppRgb (BGR48)</item></list>
    Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> 
    Convert IIDC Raw16 to variable image format.
</summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Y16 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
                    <paramref name="Dst" /> is expected to be appropriately initialized before function call.
        Image dimensions need to match source image size.
        The following set of PixelFormats is supported (given the according destination format 
        <paramref name="eImageFormat" />, quoted in parentheses, is requested):
        <list type="bullet"><item>Format8bppIndexed (MonoRec601/MonoRec709)</item><item>Format24bppRgb (BGR24)</item><item>Format32bppRgb (BGRA32)</item><item>Format32bppArgb (BGRA32)</item><item>Format16bppGrayScale (Mono16Rec601/Mono16Rec709)</item><item>Format48bppRgb (BGR48)</item></list>
        Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw12ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source AVT Raw12 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y12ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert AVT 12 bit packed mono to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source AVT 12 Bit packed Mono image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ImageTransform_IntLCAAV(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert IIDC Baw8 to variable image format with local color anti aliasing vertical smothing </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Baw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ImageTransform_IntLCAA(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert IIDC Baw8 to variable image format with local color anti aliasing </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Baw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ImageTransform_IntYUV422(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert IIDC Baw8 to variable image format with color interpolation in the YUV color space </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Baw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ImageTransform_3x3(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert IIDC Raw8 to variable image format with 3x3 filtering </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Baw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert IIDC Raw8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Raw8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <param name="bayerPattern"> camera color filter id </param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)">
            <summary> 
    Convert several Mono16 format variations to arbitrary image formats.
</summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source Y16 image </param>
            <param name="eImageFormat"> requested destination image format</param>
            <param name="FormatInfo"> detailed input format specification</param>
            <remarks>
                <paramref name="Dst" /> is expected to be appropriately initialized before function call.
    Image dimensions need to match source image size.
    The following set of PixelFormats is supported (given the according destination format 
    <paramref name="eImageFormat" />, quoted in parentheses, is requested):
    <list type="bullet"><item>Format8bppIndexed (MonoRec601/MonoRec709)</item><item>Format24bppRgb (BGR24)</item><item>Format32bppRgb (BGRA32)</item><item>Format32bppArgb (BGRA32)</item><item>Format16bppGrayScale (Mono16Rec601/Mono16Rec709)</item><item>Format48bppRgb (BGR48)</item></list>
    Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> 
    Convert IIDC Y16 to variable image format.
</summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Y16 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        mono images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Y16 formats.
    </para>
                <para>
                    <paramref name="Dst" /> is expected to be appropriately initialized before function call.
        Image dimensions need to match source image size.
        The following set of PixelFormats is supported (given the according destination format 
        <paramref name="eImageFormat" />, quoted in parentheses, is requested):
        <list type="bullet"><item>Format8bppIndexed (MonoRec601/MonoRec709)</item><item>Format24bppRgb (BGR24)</item><item>Format32bppRgb (BGRA32)</item><item>Format32bppArgb (BGRA32)</item><item>Format16bppGrayScale (Mono16Rec601/Mono16Rec709)</item><item>Format48bppRgb (BGR48)</item></list>
        Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y8ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC Y8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC Y8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV411ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC YUV411BGR8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC YUV411 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV422ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC YUV422 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC YUV422 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV444ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC YUV444 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC YUV444 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGBA8ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC BGR8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC BGR8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGB8ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat)">
            <summary> convert IIDC BGR8 to variable image format </summary>
            <param name="Dst"> destination image (variable image format)</param>
            <param name="Src"> source IIDC BGR8 image </param>
            <param name="eImageFormat"> variable image format of the destination image</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGB8ToRGBYPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert RGB8 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Y"> image data for luma channel</param>
            <param name="Src"> source RGB8 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV411ToRGBYPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV411 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Y"> image data for luma channel</param>
            <param name="Src"> source YUV411 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV422ToRGBYPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV422 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Y"> image data for luma channel</param>
            <param name="Src"> source YUV422 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV444ToRGBYPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV444 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Y"> image data for luma channel</param>
            <param name="Src"> source YUV444 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV411ToRGBPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV411 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Src"> source YUV411 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV422ToRGBPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV422 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Src"> source YUV422 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV444ToRGBPlanes(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert YUV444 to RGB planes. </summary>
            <param name="R"> image data for red channel</param>
            <param name="G"> image data for green channel</param>
            <param name="B"> image data for blue channel</param>
            <param name="Src"> source YUV444 image </param>
            <param name="XSize"> width of image in pixel</param>
            <param name="YSize"> height of image in pixel</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.MakeGrayScalePalette(System.Drawing.Bitmap)">
            <summary> Initialize a gray scale palette for the bitmap. </summary>
            <param name="Image"> Format8bppIndexed bitmap to set the palette for</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.GetBitsPerPixel(System.Drawing.Imaging.PixelFormat)">
            <summary> get bits per pixel for a given pixel format. </summary>
            <param name="pixel_format"> PixelFormat</param>
            <returns> bits per pixel</returns>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.GetBitsPerPixel(AlliedVisionTech.UniFoundationNet.enUniColorCode)">
            <summary> get bits per pixel for a given color format. </summary>
            <param name="code"> IIDC color Code</param>
            <returns> bits per pixel</returns>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.GetSIS(AlliedVisionTech.UniFoundationNet.SISData,System.Byte[],System.UInt32,System.UInt32,AlliedVisionTech.UniFoundationNet.enUniColorCode,System.Int32,AlliedVisionTech.UniFoundationNet.enUniSISType)">
            <summary> filter the SIS from a given transport image. </summary>
            <param name="Data"> return of SIS data</param>
            <param name="Image"> image in transport format, unprocessed</param>
            <param name="XSize"> Width of the image</param>
            <param name="YSize"> Height of the image</param>
            <param name="Code"> IIDC color code</param>
            <param name="SISPos"> Line pos of the SIS</param>
            <param name="Type"> SIS type</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw12ToBGR16_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary> convert packed 12bit Raw to 48bit Bitmap using color correction </summary>
            <param name="Dst"> Bitmap Format48bppRgb</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="BayerPattern"> bayer pattern for demosaicing</param>
            <param name="ColCor"> color correction to be applied for each pixel</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw12ToBGR16(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert packed 12bit Raw to 48bit Bitmap using color correction </summary>
            <param name="Dst"> Bitmap Format48bppRgb</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="BayerPattern"> bayer pattern for demosaicing</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw12ToBGR_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary> convert packed 12bit Raw to 24bit Bitmap using color correction </summary>
            <param name="Dst"> Bitmap Format24bppRgb</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="BayerPattern"> bayer pattern for demosaicing</param>
            <param name="ColCor"> color correction to be applied for each pixel</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw12ToBGR(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert packed 12bit Raw to Bitmap </summary>
            <param name="Dst"> Bitmap Format24bppRgb</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="BayerPattern"> bayer pattern for demosaicing</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Packed12BitTo16Bit(System.UInt16[],System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert packed 12bit Raw to Raw16 </summary>
            <param name="Dst"> Raw16 destination as byte array</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="XSize"> Width of the image</param>
            <param name="YSize"> Height of the image</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Packed12BitTo8Bit(System.Byte[],System.Byte[],System.UInt32,System.UInt32)">
            <summary> convert packed 12bit Raw to Raw8 </summary>
            <param name="Dst"> Raw8 destination as byte array</param>
            <param name="Src"> Raw12 packed as byte array</param>
            <param name="XSize"> Width of the image</param>
            <param name="YSize"> Height of the image</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Flip(System.Byte[],System.UInt32,System.UInt32,AlliedVisionTech.UniFoundationNet.enUniFlipMode,AlliedVisionTech.UniFoundationNet.enUniColorCode)">
            <summary> universal image flip </summary>
            <param name="Image"> Source and destination array to flip</param>
            <param name="XSize"> Width of the image in pixel</param>
            <param name="YSize"> Height of the image in pixel</param>
            <param name="FlipMode"> modus for flipping the image</param>
            <param name="ColorCode"> IIDC color code for correct flipping</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.FlipVertical(System.Byte[],System.UInt32,System.UInt32)">
            <summary>Flip image vertical top to bottom </summary>
            <param name="Image"> Source and Destination as Byte Array</param>
            <param name="BytesPerLine"> bytes per line</param>
            <param name="Lines"> line count</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMToBGR_Mono(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert interlaced Raw8 Guppy Mode1 to Bitmap </summary>
            <param name="Dst"> Bitmap Format24bppRgb</param>
            <param name="Src"> interlaced Raw8 CYGM Source as Byte Array</param>
            <param name="BayerPattern"> Pattern to use for demosaicing</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMToBGR_Bin(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary> convert interlaced Raw8 binning mode Guppy mode0 to Bitmap </summary>
            <param name="Dst"> Bitmap Format24bppRgb</param>
            <param name="Src"> interlaced Raw8 CYGM binning Source as Byte Array</param>
            <param name="BayerPattern"> currently unused</param>
            <remarks> Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMToBGR_3x3(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert interlaced Raw8 Guppy Mode1 to Bitmap with 3x3 mask demosaicing </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">interlaced Raw8 CYGM Source as Byte Array</param>
            <param name="BayerPattern">currently unused</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.IRaw8CYGMToBGR(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert interlaced Raw8 Guppy Mode1 to Bitmap </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">interlaced Raw8 CYGM Source as Byte Array</param>
            <param name="BayerPattern">currently unused</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.InterlacedToProgressive(System.Drawing.Bitmap)">
            <summary>convert Interlaced lines to progressive lines </summary>
            <param name="SrcDst">source Bitmap will be used to return non interlaced Bitmap</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.InterlacedToProgressive(System.Drawing.Bitmap,System.Drawing.Bitmap)">
            <summary>convert Interlaced lines to progressive lines </summary>
            <param name="Dst">destination Bitmap</param>
            <param name="Src">source Bitmap</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.InterlacedToProgressive(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert Interlaced lines to progressive lines </summary>
            <param name="Dst">Bitmap Format8bppIndexed determines the line width</param>
            <param name="Src">Interlaced Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ToY16_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,System.UInt32,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary>convert Raw16 to 16bit monochrome Bitmap with demosaicing and color correction </summary>
            <param name="Dst">Bitmap Format16bppGrayScale</param>
            <param name="Src">Raw16 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ShiftBits">bits to shift the source pixel values</param>
            <param name="ColCor">color correction to be applied for each pixel</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
        The 48Bit RGB seams not to be well supported by the NET framework, displaying and save will fail badly.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ToY16(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,System.UInt32)">
            <summary>convert Raw16 to 16bit monochrome  Bitmap </summary>
            <param name="Dst">Bitmap Format16bppGrayScale</param>
            <param name="Src">Raw16 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ShiftBits">bits to shift the source pixel values</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
        The 48Bit RGB seams not to be well supported by the NET framework, displaying and save will fail badly.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ToBGR16_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,System.UInt32,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary>convert Raw16 to 16bit Bitmap with demosaicing and color correction </summary>
            <param name="Dst">Bitmap Format48bppRgb</param>
            <param name="Src">Raw16 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ShiftBits">bits to shift the source pixel values</param>
            <param name="ColCor">color correction to be applied to each pixel</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
        The 48Bit RGB seams not to be well supported by the NET framework, displaying and save will fail badly.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ToBGR16(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,System.UInt32)">
            <summary>convert Raw16 to 16 bit Bitmap </summary>
            <param name="Dst">Bitmap Format48bppRgb</param>
            <param name="Src">Raw16 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ShiftBits">Bits to shift the source pixel values</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
        The 48Bit RGB seams not to be well supported by the NET framework, displaying and save will fail badly.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToY8_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary>convert Raw8 to monochrome Bitmap with demosaicing and color correction copying to R,G and B </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ColCor">color correction to be applied to each pixel</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToY8(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to monochrome Bitmap with demosaicing copying to R,G and B </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ToY8_MMX(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert Mono16 to Bitmap using MMX technology </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">Mono16 Source as Byte Array</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        mono images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Y16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown, determine support with \ref TechInfo
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ToY8(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniEndianness,AlliedVisionTech.UniFoundationNet.enUniAlignment,System.UInt32)">
            <summary>convert Mono16 to Bitmap </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">Mono16 Source as Byte Array</param>
            <param name="ImageEndianness">Endianness of image data</param>
            <param name="ImageAlignment">Alignment of image data</param>
            <param name="PixelDepth">Number of bits per pixel</param>
            <remarks>Src Size and Dst size have to match exactly, otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ToY8(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert Mono16 to Bitmap </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">Mono16 Source as Byte Array</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        mono images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Y16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_CC(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern,AlliedVisionTech.UniTransformNet.UniColorCorrection)">
            <summary>convert Raw8 to Bitmap with color correction </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <param name="ColCor">ColorCorrection color correction to be applied to each pixel</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_IntLCAAV(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap with edge preserving demosaicing and local color anti aliasing and vertical color smoothing </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_IntLCAA(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap with edge preserving demosaicing and local color anti aliasing </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Bayer pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_IntYUV422(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap with  edge preserving demosaicing </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_3x3(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap with 3x3 mask demosaicing </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR_mono(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap with debayering and copying to R,G and B </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ToBGR(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniEndianness,AlliedVisionTech.UniFoundationNet.enUniAlignment,System.UInt32)">
            <summary>convert Mono16 to Bitmap </summary>
            <returns />
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Mono16 Source as Byte Array</param>
            <param name="ImageEndianness">Endianness of image data</param>
            <param name="ImageAlignment">Alignment of image data</param>
            <param name="PixelDepth">Number of bits per pixel</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ToBGR(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert Mono16 to Bitmap using the most significant bits and copying to R,G and B </summary>
            <returns />
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Mono16 Source as Byte Array</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        mono images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Y16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Y16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ToBGR(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw16 to Bitmap using the most significant bits </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw16 Source as Byte Array</param>
            <param name="BayerPattern">Pattern to use for demosaicing</param>
            <remarks>
                <para>
        This function is provided for backwards compatibility. When using GigE-Vision cameras, 
        raw images without any compatibility adjustments will be converted incorrectly.
        See <see cref="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw16ImageTransform(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat,AlliedVisionTech.UniTransformNet.UniTransportFormatInfo)" />
        for all-purpose image conversions from Raw16 formats.
    </para>
                <para>
        Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown.
    </para>
            </remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.Raw8ToBGR(System.Drawing.Bitmap,System.Byte[],AlliedVisionTech.UniFoundationNet.enUniBayerPattern)">
            <summary>convert Raw8 to Bitmap </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">Raw8 Source as Byte Array</param>
            <param name="BayerPattern">Bayer pattern to use for demosaicing </param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGB8ToY8(System.Drawing.Bitmap,System.Drawing.Bitmap)">
            <summary>convert RGB8 to Mono8 </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">RGB8 Source bitmap Format24bppRgb</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGB8ToY8(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert RGB8 to Mono8 </summary>
            <param name="Dst">Bitmap Format8bppIndexed</param>
            <param name="Src">RGB8 Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.RGB8ToBGR(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert RGB8 to Bitmap swaps R and B. </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">RGB8 Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV411ToBGR(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert YUV411 to Bitmap </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">YUV411 Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV422ToBGR(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert YUV422 to Bitmap </summary>
            <param name="Dst">Bitmap Format24bppRgb</param>
            <param name="Src">YUV422 Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.YUV444ToBGR(System.Drawing.Bitmap,System.Byte[])">
            <summary>convert YUV422 to Bitmap </summary>
            <param name="Dst">Bitmap <see cref="!:System::Windows::Media::PixelFormat::Format24bppRgb" /></param>
            <param name="Src">YUV444 Source as Byte Array</param>
            <remarks>Src Size and Dst size have to match exactly otherwise an uniTransformException will be thrown</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransform.GetVersion">
            <summary>get version of the used UniTransform "C"-API </summary>
            <returns>version of the underlying API [Major][Minor][Subminor][Build]</returns>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniColorCorrection.Dispose">
            <summary> Dispose destructor for color correction </summary>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniColorCorrection.#ctor(System.Single[],AlliedVisionTech.UniTransformNet.UniColorCorrection.enUniColorCorrectionType)">
            <summary>Constructor from color correction matrix and type </summary>
            <param name="cc_matrix"> float array with color correction matrix</param>
            <param name="type"> type of color correction to use</param>
            <seealso cref="T:AlliedVisionTech.UniTransformNet.UniColorCorrection.enUniColorCorrectionType" />
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.TechInfo.#ctor">
            <summary>default Constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.UniTransform">
            <summary> provides access to the UniTransform "C" API for Net components.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.UniTransportFormatInfo">
            <summary>This class contains information used for unified processing of 16-bit mono/raw images from Firewire and GigE-Vision cameras.</summary>
        </member>
        <member name="F:AlliedVisionTech.UniTransformNet.TechInfo.m_Processor">
            <summary> supported by Hardware.</summary>
        </member>
        <member name="F:AlliedVisionTech.UniTransformNet.TechInfo.m_OperationSystem">
            <summary> supported by OS. </summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.TechInfo">
            <summary> multimedia technology support.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.AMD3DNow">
            <summary> AMD3DNow is supported on the system.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.IntelSSSE3">
            <summary> Intel SSSE3 is supported on the system.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.IntelSSE3">
            <summary> Intel SSE3 is supported on the system.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.IntelSSE2">
            <summary> Intel SSE2 is supported on the system.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.IntelSSE">
            <summary> Intel SSE is supported on the system.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.MultimediaTech.IntelMMX">
            <summary> Intel MMX extension is supported on the system.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.MultimediaTech">
            <summary> Property holder for supported multimedia extensions.</summary>
        </member>
        <member name="F:AlliedVisionTech.UniTransformNet.UniColorCorrection.enUniColorCorrectionType.MatrixInt16">
            <summary> calculation will be done with 16bit integers</summary>
        </member>
        <member name="F:AlliedVisionTech.UniTransformNet.UniColorCorrection.enUniColorCorrectionType.MatrixInt8">
            <summary> calculation will be done with 8bit integers</summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.UniColorCorrection.enUniColorCorrectionType">
            <summary> color depth of the source image for the correction.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.UniColorCorrection">
            <summary> 3x3 transformation matrix-based color correction.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniTransformNet.UniTransformException.ReturnCode">
            <summary> Read access to UniAPI return code.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransformException.#ctor(System.UInt32)">
            <summary> constructor from a UniReturnCode.</summary>
            <param name="error"> The return code used for initialization</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransformException.#ctor(System.String)">
            <summary> constructor from String which can be accessed by Exception::Message.</summary>
            <param name="s"> The string reference used for initialization</param>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.UniTransformException.#ctor">
            <summary> Default constructor.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniTransformNet.UniTransformException">
            <summary> Inherits ::System::Exception, thrown by all algorithms in UniTransform.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniTransformNet.MapErrorInfo(System.UInt32)">
            <summary> convert error code to readable text.</summary>
            <param name="error"> error to translate</param>
            <returns> error description</returns>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enFlipMode">
            <summary> Mode used by methods flipping an image </summary>
        </member>
        <member name="F:Both">
            <summary> flip horizontal and vertical </summary>
        </member>
        <member name="F:TopDown">
            <summary> flip top to down, the same as vertical </summary>
        </member>
        <member name="F:Vertical">
            <summary> flip along the vertical axis </summary>
        </member>
        <member name="F:LeftRight">
            <summary> left to right same as horizontal  </summary>
        </member>
        <member name="F:Horizontal">
            <summary> flip along the horizontal axis </summary>
        </member>
        <member name="F:None">
            <summary> do not flip </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniDefinitions">
            <summary> UniControlNet value definition</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AllCameras">
            <summary> Defines that a notification is global, for all cameras</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniReturn">
            <summary> UniAPI return values </summary>
        </member>
        <member name="M:UIT_Y12ImageTransform(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert a packed 12 bit monochrome format to variable image format.
* \param[out]   pDst                a pointer to the destination image  (according to eImageFormat)
* \param[in]    pSrc                a pointer to the binned interlaced image
* \param[in]    nPixelCount         number of pixels of the image
* \param[in]    eImageFormat        destination \ref E_UNI_SIMPLE_IMAGE_FORMAT

</member>
        <member name="M:UIT_Raw12ImageTransform(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert AVT RAW12 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    AVT RAW12 image source
* \param[in]    nXSize                  width of the image
* \param[in]    nYSize                  height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    BayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_IRaw8CYGMImageTransform_3x3(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert interlaced RAW8 CYGM to variable image format with 3x3 local filtering.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    interlaced RAW8 CYGM image source
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_IRaw8CYGMImageTransform(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert interlaced RAW8 CYGM to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    interlaced RAW8 image source
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height off the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_IRaw8CYGMImageTransform_Bin(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert interlaced RAW8 binning format to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    interlaced RAW8 image source
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw8ImageTransform_IntLCAAV(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert RAW8 to variable image format  with local color anti aliasing vertical interpolation.
* \param[out]       pDst                variable image format destination (according to eImageFormat)
* \param[in,out]    pSrc                RAW8 image, will be altered in processing
* \param[in]        XSize               width of image
* \param[in]        YSize               height of image
* \param[in]        eImageFormat        destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns          UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw8ImageTransform_IntLCAA(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert RAW8 to variable image format with local color anti aliasing.
* \param[out]       pDst                variable image format destination (according to eImageFormat)
* \param[in,out]    pSrc                RAW8 source image, will be altered while processing
* \param[in]        XSize               width of the image
* \param[in]        YSize               height of the image
* \param[in]        eImageFormat        destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns          UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw8ImageTransform_IntYUV422(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert RAW8 to variable image format with U/V interpolation in the YUV color space.
* \param[out]       pDst                variable image format destination (according to eImageFormat)
* \param[in,out]    pSrc                RAW8 image, will be altered in processing
* \param[in]        XSize               width of the image
* \param[in]        YSize               height of the image
* \param[in]        eImageFormat        destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns          UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw8ImageTransform_3x3(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert RAW8 to variable image format with 3x3 local filtering.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    RAW8 source image
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw16ImageTransformEx(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,S_UNI_TRANSPORT_FORMAT_INFO!System.Runtime.CompilerServices.IsConst*)">
Convert several Raw16 format variations to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    IIDC RAW16 image
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination image format (see \ref E_UNI_SIMPLE_IMAGE_FORMAT)
* \param[in]    psFormatInfo            detailed input format specification
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw16ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert IIDC RAW16 to variable image format.
* \note 
* This function is provided for backwards compatibility. When using GigE-Vision cameras,
* raw images without any compatibility adjustments will be converted incorrectly.  
* It is recommended to use ::UIT_Raw16ImageTransformEx for all-purpose image conversions
* from Y16 formats. 
*
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    IIDC RAW16 image
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Raw8ImageTransform(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Convert RAW8 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    RAW8 source image
* \param[in]    XSize                   width of the image
* \param[in]    YSize                   height of the image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    bayerPattern            bayer pattern ::E_UNI_BAYER_PATTERN
* \note         also supports ::E_SIF_UYVY output
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Y16ImageTransformEx(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,S_UNI_TRANSPORT_FORMAT_INFO!System.Runtime.CompilerServices.IsConst*)">
Convert several Mono16 format variations to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    IIDC Mono16 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \param[in]    psFormatInfo            detailed input format specification - IIDC peculiarities are assumed when NULL
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Y16ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert IIDC Mono16 to variable image format.
* \note 
* This function is provided for backwards compatibility. When using GigE-Vision cameras,
* mono images without any compatibility adjustments will be converted incorrectly.  
* It is recommended to use ::UIT_Y16ImageTransformEx for all-purpose image conversions
* from Y16 formats. 
* 
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    IIDC Mono16 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Y8ImageTransform(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert Mono8 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    Mono8 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \note         also supports E_SIF_UYVY_MONO output
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_YUV411ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert YUV 4:1:1 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    YUV411 source image (byte order UYYVYY)
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_YUV422ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert YUV 4:2:2 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    YUV422 source image (byte order UYVY)
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE 

</member>
        <member name="M:UIT_YUV444ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert YUV 4:4:4 to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    YUV444 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE 

</member>
        <member name="M:UIT_RGBA8ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert RGBA format with 8 bytes per pixel to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    RGBA8 / RGBA32 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_RGB8ImageTransform(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
@name Format conversions to variable image formats
Format conversions from IIDC transfer formats to BGR24, BGRA32 or YUV422 UYVY


Convert RGB format with 8 bytes per pixel to variable image format.
* \param[out]   pDst                    variable image format destination (according to eImageFormat)
* \param[in]    pSrc                    IIDC RGB8/ RGB24 source image
* \param[in]    nPixelCount             number of pixels per image
* \param[in]    eImageFormat            destination \ref E_UNI_SIMPLE_IMAGE_FORMAT
* \returns      UNI_RETURN_TYPE

</member>
        <member name="M:UIT_Flip(System.Byte*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
Generic flip of image.
    \param[in,out] pImage       valid pointer to source/destination image
    \param[in]  nXSize          image width
    \param[in]  nYSize          Image height
    \param[in]  eFlipMode       Use valid values from ::E_UNI_FLIP_MODE
    \param[in]  eccColorCode    Use ::E_UNI_COLOR_CODE to allow for correct image flipping in horizontal and both modes
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_FlipVertical(System.Byte*,System.UInt32,System.UInt32)">
 Flip image vertically.
 Create a top-down version of the provided image (in place). This method is made for all kinds of images since the pixel format is not relevant if
 the correct number of overall bytes per line is submitted.
    \param[in,out]  pImage          Valid pointer to source/destination image
    \param[in]      nBytesPerLine   The number of bytes (not pixels!) per line
    \param[in]      nLines          The number of lines
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_ResampleScanlinesEx(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.UInt32,System.UInt32)">
 Do resampling along lines.
 This method is made for Mono8 images as well as for Raw8 images.
Extended version.
    \param[out] pDst             valid pointer to destination image
    \param[in]  pSrc             valid const pointer to source image
    \param[in]  nDstXSize        destination image width
    \param[in]  nDstPitch        distance between consecutive lines in the destination image
    \param[in]  nSrcXSize        source image width
    \param[in]  nSrcPitch        distance between consecutive line in the source image
    \param[in]  nSrcYSize        source image height
    \param[in]  nDstPixelSize    destination image pixel width
    \param[in]  nSrcPixelSize    source image pixel width
    \param[in]  nBytesPerPixel   The number of bytes per pixel (e.g. 1 for mono, 3 for RGB)
    \param[in]  nRopMode         use ::E_UNI_ROPMODE
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_ResampleScanlines(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.Int32,System.Int32,System.Int32,System.Single,System.Single,System.UInt32,System.UInt32)">
 Do resampling along lines.
    \param[out] pDst             valid pointer to destination image
    \param[in]  pSrc             valid const pointer to source image
    \param[in]  nDstXSize        destination image width
    \param[in]  nSrcXSize        source image width
    \param[in]  nSrcYSize        source image height
    \param[in]  nDstPixelSize    destination image pixel width
    \param[in]  nSrcPixelSize    source image pixel width
    \param[in]  nBytesPerPixel   the number of bytes per pixel (e.g. 1 for mono, 3 for RGB)
    \param[in]  nRopMode         use ::E_UNI_ROPMODE
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_GetResampledScanlineLength(System.Int32*,System.Int32,System.Single,System.Single)">
@name Image transformation.
Methods for transforming images without image type change. Logically, ::UIT_RGB8ToBGR
and ::UIT_InterlacedToProgressive would also fall into this category, but since
they are already listed in different categories, they do not appear here.


 Calculate the needed destination image width for images with non-square pixels.
 This method is made for Mono8 images as well as for Raw8 images.

    \param[out]     pDstXSize       pointer, returns the needed destination image width
    \param[in]      nSrcXSize       source image width
    \param[in]      nDstPixelSize   destination image pixel size
    \param[in]      nSrcPixelSize   source image pixel size
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_RGB8ToRGBYPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image from RGB format to R,G,B and additional Y planes

 \param[out] pR        A pointer to the red plane
 \param[out] pG        A pointer to the green plane 
 \param[out] pB        A pointer to the blue plane
 \param[out] pY        A pointer to the intensity plane
 \param[in] pSrc       A pointer to the source image
 \param[in] nXSize     The width of the image in columns
 \param[in] nYSize     The height of the image in rows
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV411ToRGBYPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image from YUV411 format to R,G,B and additional Y planes

 \param[out] pR        A pointer to the red plane
 \param[out] pG        A pointer to the green plane 
 \param[out] pB        A pointer to the blue plane
 \param[out] pY        A pointer to the intensity plane
 \param[in] pSrc       A pointer to the source image
 \param[in] nXSize     The width of the image in columns
 \param[in] nYSize     The height of the image in rows
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV422ToRGBYPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image from YUV422 format to R,G,B and additional Y planes

 \param[out] pR        A pointer to the red plane
 \param[out] pG        A pointer to the green plane 
 \param[out] pB        A pointer to the blue plane
 \param[out] pY        A pointer to the intensity plane
 \param[in]  pSrc      A pointer to the source image
 \param[in]  nXSize    The width of the image in columns
 \param[in]  nYSize    The height of the image in rows
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV444ToRGBYPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image from YUV444 format to R,G,B and additional Y planes

 \param[out] pR        A pointer to the red plane
 \param[out] pG        A pointer to the green plane 
 \param[out] pB        A pointer to the blue plane
 \param[out] pY        A pointer to the intensity plane
 \param[in]  pSrc      A pointer to the source image
 \param[in]  nXSize    The width of the image in columns
 \param[in]  nYSize    The height of the image in rows
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV411ToRGBPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image in YUV411 format to R,G and B planes

 \param[out]	pR		A pointer to the red plane
 \param[out]	pG		A pointer to the green plane 
 \param[out]	pB		A pointer to the blue plane
 \param[in]	pSrc	A pointer to the source image
 \param[in]	nXSize	The width of the image in columns
 \param[in]	nYSize	The height of the image in rows
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV422ToRGBPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
 Convert an image in YUV422 format to R,G and B planes

 \param[out]	pR		A pointer to the red plane
 \param[out]	pG		A pointer to the green plane 
 \param[out]	pB		A pointer to the blue plane
 \param[in]	pSrc	A pointer to the source image
 \param[in]	nXSize	The width of the image in columns n&gt;1
 \param[in]	nYSize	The height of the image in rows   n&gt;1 
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV444ToRGBPlanes(System.Byte*,System.Byte*,System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
@name Format conversions to image planes
Format conversions from IIDC transfer formats to image planes


 Convert an image in YUV444 format to R,G and B planes

 \param[out]	pR		A pointer to the red plane
 \param[out]	pG		A pointer to the green plane 
 \param[out]	pB		A pointer to the blue plane
 \param[in]	pSrc	A pointer to the source image
 \param[in]	nXSize	The width of the image in columns n&gt;1
 \param[in]	nYSize	The height of the image in rows   n&gt;1 
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_IRaw8CYGMToMono(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a raw interlaced CYGM binning format into progressive RGB format with R=G=B.
  Only images that start
  with the second half-image are supported, like those that are resulting
  from the Guppy GF-038.

 \param[out]  pDst             A pointer to the destination image (B G R B ...)
 \param[in]   pSrc             A pointer to the binned interlaced image
 \param[in]   nXSize           The width of the image in columns (must be a multiple of 2) 
 \param[in]   nYSize           The height of the image in rows   (must be a multiple of 4) 
 \param[in]   nBayerPattern    The code of the start pixel -- UNUSED so far ( otherwise, see ::E_UNI_BAYER_PATTERN)
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_IRaw8CYGMToBGR_Bin(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a raw interlaced CYGM binning format into progressive RGB format.
  Only images that start
  with the second half-image are supported, like those that are resulting
  from the Guppy GF-038.

 \param[out]  pDst             A pointer to the destination image (B G R B ...)
 \param[in]   pSrc             A pointer to the binned interlaced image
 \param[in]   nXSize           The width of the image in columns (must be a multiple of 2) 
 \param[in]   nYSize           The height of the image in rows   (must be a multiple of 4) 
 \param[in]   nBayerPattern    The code of the start pixel -- UNUSED so far ( otherwise, see ::E_UNI_BAYER_PATTERN)
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_IRaw8CYGMToBGR_3x3(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a raw interlaced CYGM format like raw or mono to a progressive RGB format with 3x3 matrix interpolation.
  Only images that start
  with the second half-image are supported, like those that are resulting
  from the Guppy GF-038.

 \param[out]  pDst            A pointer to the destination image ( [B,G,R] ...)
 \param[in]   pSrc            A pointer to the raw source image
 \param[in]   nXSize           The width of the image in columns
 \param[in]   nYSize           The height of the image in rows  (must be a multiple of 4)
 \param[in]   nBayerPattern    The code of the start pixel -- UNUSED so far ( otherwise, see ::E_UNI_BAYER_PATTERN)
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_IRaw8CYGMToBGR(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a raw interlaced CYGM format like raw or mono to a progressive RGB format.
  Only images that start
  with the second half-image are supported, like those that are resulting
  from the Guppy GF-038.

 \param[out]  pDst             A pointer to the destination image (B G R B ...)
 \param[in]   pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]   nXSize           The width of the image in columns (must be a multiple of 2) 
 \param[in]   nYSize           The height of the image in rows   (must be a multiple of 4) 
 \param[in]   nBayerPattern    The code of the start pixel use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_InterlacedToProgressive(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
@name Methods for interlaced cameras
Methods for use with interlaced cameras which also have a CYGM sensor.

 Convert an interlaced format to a progressive format.
  This method may be used to convert raw or mono images consisting of
  two half-images to progressive-data images. Only images that start
  with the second half-image are supported, like those that are resulting
  from the Guppy GF-0xx NIR.

 \param[out]  pDst            A pointer to the destination image
 \param[in]   pSrc            A pointer to the source image
 \param[in]   nXSize           The width of the image in columns
 \param[in]   nYSize           The height of the image in rows
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_DestroyColorCorrection(System.Void*)">
* Destroy the color correction object identified by the given handle.
*  After using this operation, the handle may not be used any more.
*
* \param   hCC   The handle of the conversion object to be destroyed
* \return  \li S_OK if successful, \li UNI_RESULT_PARAMETER_INVALID_1 if the handle is NULL
\li UNI_RESULT_COMMAND_ABORTED if the object could not be destroyed

</member>
        <member name="M:UIT_CreateColorCorrectionMatrix_Int16(System.Single!System.Runtime.CompilerServices.IsConst*,System.Void**)">
 Create a color conversion object and return the handle to it. 
  This handle may then be used in some 16 bit debayering methods.
  This color conversion is then used as the last operation on the
  image data (in RGB space).
  Every RGB pixel is virtually multiplied with a RGB2RGB conversion matrix
  and a range check is performed. The
  actual multiplications are implemented as integer multiplications.

 \param   fArrayCorr    A float vector with nine elements containing the color conversion matrix (3 by 3) 
 \param   phCC           The pointer to the conversion object handle
 \return  Always S_OK

</member>
        <member name="M:UIT_CreateColorCorrectionMatrix_Int8(System.Single!System.Runtime.CompilerServices.IsConst*,System.Void**)">
@name Methods for controlling color conversion
Methods for use with format conversion methods ending with "_CC"
(::UIT_Raw8ToBGR_CC, ::UIT_Raw8ToY8_CC, ::UIT_Raw16ToBGR16_CC, ::UIT_Raw16ToY16_CC).

 Create a color conversion object and return the handle to it. 
  This handle may then be used in some 8 bit debayering methods.
  This color conversion is then used as the last operation on the
  image data (in RGB space). 
  Every RGB pixel is virtually multiplied with a RGB2RGB conversion matrix
  and a range check is performed. The
  actual multiplications are implemented as integer multiplications.

 \param   *fArrayCorr    A float vector with nine elements containing the color conversion matrix (3 by 3) 
 \param   *phCC       The pointer to the conversion object handle
 \return  Always S_OK

</member>
        <member name="M:UIT_Raw12ToBGR16_CC(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.Void*)">
Pike 12Bit Packed to windows 16 bit three channel BGR with color conversion.
    \param[out] pDst            destination 16bit BGR image buffer
    \param[in]  pSrc            12bit packed source image
    \param[in]  nXSize          image width
    \param[in]  nYSize          image height
    \param[in]  BayerPattern    BayerPattern to debayer the image, use ::E_UNI_BAYER_PATTERN
    \param[in]  ColCor          ColorCorrection used while debayering
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw12ToBGR16(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
Pike 12Bit Packed to windows 16 BGR.
    \param[out] pDst            destination 16bit BGR image buffer
    \param[in]  pSrc            12bit packed source image
    \param[in]  nXSize          image width
    \param[in]  nYSize          image height
    \param[in]  BayerPattern    BayerPattern to debayer the image, use ::E_UNI_BAYER_PATTERN
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw12ToBGR_CC(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.Void*)">
12Bit packed RAW mode to Windows BGR with color conversion.
    \param[out] pDst            destination windows BGR image buffer
    \param[in]  pSrc            12bit packed source image
    \param[in]  nXSize          image width
    \param[in]  nYSize          image height
    \param[in]  BayerPattern    BayerPattern to debayer the image, use ::E_UNI_BAYER_PATTERN
    \param[in]  ColCor          ColorCorrection used while debayering
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw12ToBGR(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
12Bit packed RAW mode to Windows BGR.
    \param[out] pDst            destination windows BGR image buffer
    \param[in]  pSrc            12bit packed source image
    \param[in]  nXSize          image width
    \param[in]  nYSize          image height
    \param[in]  BayerPattern    BayerPattern to debayer the image, use ::E_UNI_BAYER_PATTERN
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Packed12BitTo16Bit(System.UInt16*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
Convert a packed 12 bit format to 16 bit data.
    This method may be used to convert RAW12 to RAW16 or MONO12 to MONO16, for example
* \param[out]  pDst             A pointer to the destination image (see ::E
* \param[in]   pSrc             A pointer to the binned interlaced image
* \param[in]   nXSize           The width of the image in columns (must be a multiple of 2) 
* \param[in]   nYSize           The height of the image in rows   (must be a multiple of 4) 
    \note   the 12bit packed data will be output as continuous 16 bit data stream without most significat 4 bit padding
            [p1_8bit][p1_4bit|p2_4bit][p2_8bit]...[p(n)_8bit][p(n)_4bit|p(n+1)_4bit][p(n+1)_8bit]
* \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Packed12BitTo8Bit(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32)">
@name 12 bit format conversions/depacking to 8 or 16 bit, with support of 2x2 debayering, color conversion
Format conversions between 12 bit formats with support of 2x2 debayering, color conversion

Convert a packed 12 bit format to 8 bit data.
    This method may be used to convert RAW12 to RAW8 or MONO12 to MONO8, for example
* \param[out]  pDst             A pointer to the destination image 
* \param[in]   pSrc             A pointer to the binned interlaced image
* \param[in]   nXSize           The width of the image in columns (must be a multiple of 2) 
* \param[in]   nYSize           The height of the image in rows   (must be a multiple of 4) 

</member>
        <member name="M:UIT_Raw8ToY8_CC(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.Void*)">
* Convert Raw8 to a monochrome 8BPP image with additional color conversion
*
* \param[out]  pDst             A pointer to the destination image (Y1 Y2 Y3 ...)
* \param[in]   pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
* \param[in]   nXSize           The width of the image in columns
* \param[in]   nYSize           The height of the image in rows n&gt;0
* \param[in]   nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
* \param[in]   ColCor           A handle to the color conversion object, e.g. created 
                               by ::UIT_CreateColorCorrectionMatrix_Int8. If zero, no
                               color correction is performed (faster).
* \return S_OK if the operation was successful 
* \note    Source and Destination can point to the same memory locations

</member>
        <member name="M:UIT_Raw8ToY8(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert Raw8 to a monochrome 8BPP image.

 \param[out]  pDst             A pointer to the destination image (Y1 Y2 Y3 ...)
 \param[in]   pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]   nXSize           The width of the image in columns
 \param[in]   nYSize           The height of the image in rows n&gt;0
 \param[in]   nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note    Source and Destination can point to the same memory locations
 \see     \link debmethods Debayering methods\endlink 

</member>
        <member name="M:UIT_Y16ToY8_MMX(System.Byte*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
@name Format conversions
Format conversions to the Windows Mono8 format with and without color conversion.

 Convert big endian MONO16 to a monochrome 8BPP image.
 \note 
 This function is provided for backwards compatibility. When using GigE-Vision cameras,
 mono images without any compatibility adjustments will be converted incorrectly.  
 It is recommended to use ::UIT_Y16ImageTransformEx for all-purpose image conversions
 from Y16 formats. 

 \param[out]   pDst        A pointer to the destination image (Y1 Y2 Y3 Y4 ...)
 \param[in]    pSrc        A pointer to the source image (big endian: YH YL YH YL ...)
 \param[in]    nPixelCount The number of pixels to be converted
 \return S_OK if the operation was successful \see GetTechnoInfo

</member>
        <member name="M:UIT_Y16ToY8Ex(System.Byte*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 Convert MONO16 to a monochrome 8BPP image.

 \param[out]   pDst        A pointer to the destination image (Y1 Y2 Y3 Y4 ...)
 \param[in]    pSrc        A pointer to the source image
 \param[in]    nPixelCount The number of pixels to be converted
 \param[in]    nEndianness The endianness of the image data, use ::E_UNI_ENDIANNESS (big endian: YH YL YH YL ..., little endian: YL YH YL YH ...)
 \param[in]    nAlignment  The alignment of the image data, use ::E_UNI_ALIGNMENT (MSB: pppp pppp pppp ...., LSB: .... pppp pppp pppp)
 \param[in]    nPixelDepth Number of bits per pixel
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Y16ToY8(System.Byte*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
 Convert big endian MONO16 to a monochrome 8BPP image.
 \note 
 This function is provided for backwards compatibility. When using GigE-Vision cameras,
 mono images without any compatibility adjustments will be converted incorrectly.  
 It is recommended to use ::UIT_Y16ImageTransformEx for all-purpose image conversions
 from Y16 formats. 

 \param[out]   pDst        A pointer to the destination image (Y1 Y2 Y3 Y4 ...)
 \param[in]    pSrc        A pointer to the source image (big endian: YH YL YH YL ...)
 \param[in]    nPixelCount The number of pixels to be converted
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_RGB8ToY8(System.Byte*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
@name Format conversions
Format conversions to the Windows Mono8 format with and without color conversion.

 Convert RGB8 data to a monochrome 8BPP image.
 

 \param[out]   pDst        A pointer to the destination image (Y Y Y ...)
 \param[in]    pSrc        A pointer to the source image (R G B R G B ...)
 \param[in]    nPixelCount The number of pixels (not: bytes) to be converted
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw8ToBGR_CC(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.Void*)">
@name Format conversions to BGR format with color conversion
Format conversions to the Windows RGB with simple 2x2 debayering.

* Convert a RAW8 image to the Windows RGB format
*
* \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
* \param[in]    pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
* \param[in]    nXSize           The width of the image in columns (must be a multiple of 2)
* \param[in]    nYSize           The height of the image in rows n&gt;0
* \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
* \param[in]    ColCor           A handle to the color conversion object, e.g created 
                                 by ::UIT_CreateColorCorrectionMatrix_Int8. If NULL, no
                                color correction is performed (faster).
* \return S_OK if the operation was successful 
* \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_Raw8ToBGR_IntLCAAV(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a RAW8 image to the Windows RGB format via an interpolated
  YUV422 format.
  As the U resp. V value, the average of the U/V values of a four by two
  pixel window is taken (indexes -1, 0 1 and 2 of each row)

 \param[out]  pDst             A pointer to the destination image (B G R B G R...)
 \param[in]   pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]   nXSize           The width of the image in columns n&gt;1
 \param[in]   nYSize           The height of the image in rows   n&gt;1
 \param[in]   nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note    Source and Destination have to point to different memory locations
 \see     \link debmethods Debayering methods\endlink 

</member>
        <member name="M:UIT_Raw8ToBGR_IntLCAA(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a RAW8 image to the Windows RGB format via a stronglier interpolated YUV422 format.
  As the U resp. V value, the average of the U/V values of four
  consecutive pixels is taken (indexes -1, 0 1 and 2)

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]    nXSize           The width of the image in columns n&gt;1
 \param[in]    nYSize           The height of the image in rows   n&gt;1
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note    Source and Destination have to point to different memory locations
 \see     \link debmethods Debayering methods\endlink 

</member>
        <member name="M:UIT_Raw8ToBGR_IntYUV422(System.Void*,System.Byte*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a RAW8 image to the Windows RGB format via an interpolated YUV422 format.
  As the U resp. V value, the average of the U/V values of
  consecutive pixels is taken

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]    nXSize           The width of the image in columns n&gt;1
 \param[in]    nYSize           The height of the image in rows   n&gt;1
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note    Source and Destination have to point to different memory locations
 \see     \link debmethods Debayering methods \endlink

</member>
        <member name="M:UIT_Raw8ToBGR_3x3(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
@name Format conversions to BGR format with more sophisticated debayering w/o color conversion
Format conversions from raw color images resulting from a RGGB sensor to the Windows RGB format.

 Convert Raw8 to the Windows RGB format using a 3x3 interpolation.

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]    nXSize           The width of the image in columns (must be a multiple of 2)
 \param[in]    nYSize           The height of the image in rows   n&gt;1
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 
 \note    Source and Destination have to point to different memory locations
 \see     \link debmethods Debayering methods\endlink 

</member>
        <member name="M:UIT_Raw8ToBGR_mono(System.Byte*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert Raw8 to the Windows RGB format with R=G=B

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]    nXSize           The width of the image in columns
 \param[in]    nYSize           The height of the image in rows
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return   S_OK if the operation was successful 

</member>
        <member name="M:UIT_Y16ToBGREx(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 Convert big endian MONO16 to the Windows RGB format. 
  The resulting mostly monochrome image format (the R,G and B values are
  more or less equal to one another) lets you extract at least a 10 bit
  monochrome image by applying the following formula:
   I   = 4*G + 2*(R-G) + (B-G)  = 2*R + G + B

 \param[out]   pDst        A pointer to the destination image  ( B G R B G R ...)
 \param[in]    pSrc        A pointer to the source image
 \param[in]    nPixelCount The number of pixels to be converted
 \param[in]    nEndianness The endianness of the image data, use ::E_UNI_ENDIANNESS (big endian: YH YL YH YL ..., little endian: YL YH YL YH ...)
 \param[in]    nAlignment  The alignment of the image data, use ::E_UNI_ALIGNMENT (MSB: pppp pppp pppp ...., LSB: .... pppp pppp pppp)
 \param[in]    nPixelDepth Number of bits per pixel
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Y16ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
 Convert big endian MONO16 to the Windows RGB format. 
  The resulting mostly monochrome image format (the R,G and B values are
  more or less equal to one another) lets you extract at least a 10 bit
  monochrome image by applying the following formula:
   I   = 4*G + 2*(R-G) + (B-G)  = 2*R + G + B
 \note 
 This function is provided for backwards compatibility. When using GigE-Vision cameras,
 mono images without any compatibility adjustments will be converted incorrectly.  
 It is recommended to use ::UIT_Y16ImageTransformEx for all-purpose image conversions
 from Y16 formats. 

 \param[out]   pDst        A pointer to the destination image  ( B G R B G R ...)
 \param[in]    pSrc        A pointer to the source image (big endian: YH YL YH YL ...)
 \param[in]    nPixelCount The number of pixels to be converted
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Y8ToBGR(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
Convert MONO8 to the Windows RGB format. 
The Mono8 value is copied to each RGB pixel
\param[out]   pDst        A pointer to the destination image  ( B G R B G R ...)
\param[in]    pSrc        A pointer to the source image 
\param[in]    nPixelCount The number of pixels to be converted
\return S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw16ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert Raw16 (YH YL) to the Windows RGB format.
 \note 
 This function is provided for backwards compatibility. When using GigE-Vision cameras,
 raw images without any compatibility adjustments will be converted incorrectly.  
 It is recommended to use ::UIT_Raw16ImageTransformEx for all-purpose image conversions
 from Raw16 formats.  

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the raw source image (big endian:RH RL GH GL ...)
 \param[in]    nXSize           The width of the image in columns
 \param[in]    nYSize           The height of the image in rows
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return   S_OK if the operation was successful 

</member>
        <member name="M:UIT_Raw8ToBGR(System.Void*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
 Convert a RAW8 image to the Windows RGB format

 \param[out]   pDst             A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc             A pointer to the raw source image (start pixel depends on parameter "nBayerPattern" )
 \param[in]    nXSize           The width of the image in columns
 \param[in]    nYSize           The height of the image in rows
 \param[in]    nBayerPattern    The code of the start pixel , use ::E_UNI_BAYER_PATTERN
 \return S_OK if the operation was successful 

</member>
        <member name="M:UIT_YUV411ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
 Convert YUV 4:1:1 data to the Windows RGB format.

 \param[out]   pDst        A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc        A pointer to the source image (U Y Y V Y Y U Y Y V Y Y ...)
 \param[in]    nPixelCount The number of pixels to be converted n&gt;1
 \return S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_YUV422ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
 Convert YUV 4:2:2 data to the Windows RGB format.

 \param[out]   pDst        A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc        A pointer to the source image (U Y V Y U Y V Y ...)
 \param[in]    nPixelCount The number of pixels to be converted n&gt;1
 \return   S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_YUV444ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
@name Format conversions to BGR with simple 2x2 debayering w/o color conversion
Simple format conversions from IIDC transfer formats to the Windows RGB format.

 Convert YUV 4:4:4 data to the Windows RGB format.

 \param[out]   pDst        A pointer to the destination image (B G R B G R ...)
 \param[in]    pSrc        A pointer to the source image (U Y V U Y V ...)
 \param[in]    nPixelCount The number of pixels to be converted (n&gt;1)
 \return   S_OK if the operation was successful 
 \note      Source and Destination have to point to different memory locations

</member>
        <member name="M:UIT_GetErrorInfo(System.Int32!System.Runtime.CompilerServices.IsLong,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte*,System.UInt32)">
Translate ::UNI_RETURN_TYPE into an human readable description.
\param[in]      nErrCode        return code to translate
\param[in,out]  pszInfoString   pointer to string for output
\param[in]      nMaxStrLen        length of the string

</member>
        <member name="M:UIT_GetBitsPerPixel(System.UInt32,System.UInt32*)">
 Get the bits per pixel for the transport image with the given color coding.

 \param[in]    ColorCode     The color coding ::E_UNI_COLOR_CODE (0..255)
 \param[out]   BitsPerPixel  The number of bits (8,12,16,24 or 48)
 \return <dl><dt>S_OK</dt><dd>if a known color coding was submitted</dd></dl></member>
        <member name="M:UIT_GetSIS(S_SIS_DATA*,System.Byte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.Int32,System.UInt32)">
Extract the Secure Image Signature from a transport image.
\param[in,out]  pSISData    pointer to S_SIS_DATA struct to be filled
\param[in]      pImage      pointer to transport image with embedded SIS
\param[in]      nXSize      width of the image in pixel
\param[in]      nYSize      height of the image in pixel
\param[in]      eColorCode  IIDC color code for transport format, use ::E_UNI_COLOR_CODE
\param[in]      nSISPos     Line number where the SIS is embedded into the image
\param[in]      eSISType    Type of SIS, use ::E_UNI_SIS_TYPE

</member>
        <member name="M:UIT_GetTechnoInfo(S_TECHNO_INFO*)">
Get information about processor supported features.
* \param[out] pTechnoInfo returns the supported SIMD technologies
* \note This should be called before using any SIMD (MMX,SSE) optimmized functions

</member>
        <member name="M:UIT_GetVersion(System.UInt32*)">
@name Miscellaneous methods

  Get library version.
* \param[out] pnValue  pointer, returns the library version (Major,Minor,SubMinor,Build)
* \return will always return S_OK

</member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniSimpleImageFormat">
stream type enum.

</member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.UserValue">
            <summary>  User defined value</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.SerialNumber">
            <summary>  Camera serial number</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.ColorCode">
            <summary>  IIDC color code</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.SequenceIndex">
            <summary>  Sequence position index</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.InputState(System.UInt32)">
            <summary>  Indexer for input states</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.OutputState(System.UInt32)">
            <summary>  Indexer for output states</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.Gain">
            <summary>  Gain value for image </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.Shutter">
            <summary>  Shutter value for image</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.AOIHeight">
            <summary>  AOI Height</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.AOIWidth">
            <summary>  AOI Width</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.AOITop">
            <summary>  AOI Top corner</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.AOILeft">
            <summary>  AOI Left corner</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.TriggerCounter">
            <summary>  Number of received trigger signals</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.FrameCounter">
            <summary>  Number of frames since start</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.SISData.CycleCounter">
            <summary>  FireWire CycleCount when image was grabbed</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.SISData.Dispose">
            <summary> Cleaning unmanaged resources</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.SISData.#ctor">
            <summary> Default constructor </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.SISData.m_sis_data">
            <summary> unmanaged pointer to S_SIS_DATA from UniCApi</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.SISData">
            <summary> Secure Image Signature </summary>
            <remarks> from technical manuals:\n
   Secure image signature (SIS) is the synonym for data, which is inserted into
   an image to improve or check image integrity.\n
   All Pike models starting with firmware 3.x and all Stingray models can insert
<list type="bullet"><item> Time stamp (1394 bus cycle time at the beginning of integration)</item><item> Trigger counter (external trigger seen only)</item><item> Frame counter (frames read out of the sensor)</item><item> AOI (x, y, width, height)</item><item> Exposure (shutter) and gain</item><item> Input and output state on exposure start</item><item> Index of sequence mode</item><item> Serial number</item><item> User value </item></list>        
   Starting with firmware V3.03, all CCD Marlin models can insert
<list><item> Time stamp (1394 bus cycle time at the beginning of integration)</item><item> Trigger counter (external trigger seen only)</item><item> Frame counter (frames read out of the sensor)</item></list>
   into a selectable line position within the image. Furthermore the trigger
   counter and the frame counter are available as advanced registers to be read
   out directly.</remarks>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniAlignment">
            <summary> Enumeration for the alignment </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniAlignment.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniAlignment.LSB">
            <summary> Least significant byte alignment </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniAlignment.MSB">
            <summary> Most significant bit alignment </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniEndianness">
            <summary> Enumeration for the endianness </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniEndianness.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniEndianness.Big">
            <summary> Big endian data format </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniEndianness.Little">
            <summary> Little endian data format </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniStopOption">
            <summary> option for AlliedVisionTech::UniControlNet::Camera::GrabStop() </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniStopOption.Immediately">
            <summary> stop and discard the pending image </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniStopOption.DeliverAllPending">
            <summary> stop and continue the delivering of the pending images </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniSISType">
            <summary> type of the Secure image Stamp format </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniSISType.Pike">
            <summary> Pike SIS format </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniSISType.Marlin">
            <summary> Marlin SIS format </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniSISType.NotPresent">
            <summary> camera does not support SIS </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniFlipMode">
            <summary> Enumeration for the direction for flipping images </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.Both">
            <summary> flip in both directions </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.TopDown">
            <summary> same as Vertical </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.Vertical">
            <summary> flip in vertical direction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.LeftRight">
            <summary> same as Horizontal </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.Horizontal">
            <summary> flip in horizontal direction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFlipMode.None">
            <summary> no raster operation </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniProcessingMode">
            <summary> Enums for the published UniTransform Functions. </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw12ToBGR16_CC">
            <summary> conversion mode camera raw 12bit packet data  to 16bit Windows BGR with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw12ToBGR16">
            <summary> conversion mode camera raw 12bit packet data  to 16bit Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw12ToBGR_CC">
            <summary> conversion mode camera raw 12bit packet data  to 8bit Windows BGR with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw12ToBGR">
            <summary> conversion mode camera raw 12bit packet data  to 8bit Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Packed12BitTo16Bit">
            <summary> conversion mode unpack 12bit packet transport format to 16bit </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Packed12BitTo8Bit">
            <summary> conversion mode unpack 12bit packed transport format to 8bit  </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.IRaw8CYGMToMono">
            <summary> conversion mode CyYeMaGr interlaced sensor data from Guppy mode1 to mono 8 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.IRaw8CYGMToBGR_Bin">
            <summary> conversion mode CyYeMaGr interlaced sensor data from Guppy mode0 to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.IRaw8CYGMToBGR_3x3">
            <summary> conversion mode CyYeMaGr interlaced sensor data from Guppy mode1 with smoothing to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.IRaw8CYGMToBGR">
            <summary> conversion mode CyYeMaGr interlaced sensor data from Guppy mode1 to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.InterlacedToProgressive">
            <summary> conversion mode interlaced data to progressive data </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw16ToY16_CC">
            <summary> conversion mode camera raw 16bit to mono 16bit with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw16ToY16">
            <summary> conversion mode camera raw 16bit to mono 16bit </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw16ToBGR16_CC">
            <summary> conversion mode camera raw 16bit to Windows BGR 16bit with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw16ToBGR16">
            <summary> conversion mode camera raw 16bit to Windows BGR 16bit </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw16ToBGR">
            <summary> conversion mode camera raw 16bit to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Y16ToY8_MMX">
            <summary> conversion mode camera raw 16bit to mono 8 with mmx optimization </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Y16ToY8">
            <summary> conversion mode camera mono 16bit to mono 8 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Y16ToBGR">
            <summary> conversion mode camera mono 16bit  to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToY8_CC">
            <summary> conversion mode camera raw 8bit  to mono 8bit with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToY8">
            <summary> conversion mode camera raw 8bit  to mono 8bit </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_CC">
            <summary> conversion mode camera raw 8bit to Windows BGR with color correction </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_IntLCAAV">
            <summary> conversion mode camera raw 8bit with vertical and local color anti aliasing to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_IntLCAA">
            <summary> conversion mode camera raw 8bit with local color anti aliasing to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_IntYUV422">
            <summary> conversion mode camera raw 8bit with anti aliasing to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_3x3">
            <summary> conversion mode camera raw 8bit to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR_mono">
            <summary> conversion mode camera raw 8bit to Windows BGR with mono in all components </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.Raw8ToBGR">
            <summary> conversion mode camera raw 8bit to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.RGB8ToBGR">
            <summary> conversion mode RGB 8bit to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.YUV422ToBGR">
            <summary> conversion mode YUV422 to Windows BGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniProcessingMode.YUV411ToBGR">
            <summary> conversion mode YUV411 to Windows BGR </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniBayerPattern">
            <summary> Enumeration for the Bayer pattern </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.LAST">
            <summary> Last </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.MGCY">
            <summary> MGCY pattern, magenta pixel comes first in the first row, cyan in the second row (of the sensor) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.CYMG">
            <summary> CYMG pattern, cyan pixel comes first in the first row, magenta in the second row (of the sensor) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.GMCY">
            <summary> GMCY pattern, green pixel comes first in the first row, cyan in the second row (of the sensor) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.CYGM">
            <summary> CYGM pattern, cyan pixel comes first in the first row, green in the second row (of the sensor) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.BGGR">
            <summary> BGGR pattern, blue pixel comes first </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.GRBG">
            <summary>GRBG pattern, green pixel of red row comes first </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.GBRG">
            <summary> GBRG pattern, green pixel of blue row comes first </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.RGGB">
            <summary> RGGB pattern, red pixel comes first </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniBayerPattern.Auto">
            <summary> Set pattern to automatic mode (may not be used in conversion methods!!!)</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniColorCode">
            <summary> IIDC Color Code </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.End">
            <summary> same as Last </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RGBA8">
            <summary> four bytes per pixel, byte order is RGBARGBA, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RAW12">
            <summary> two pixels in three bytes, see ::S_12BIT_2PACKED </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.SRGB12">
            <summary> same format as RGB12 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.SMono12">
            <summary> same format as Mono12 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RGB12">
            <summary> 12 Bit RGB for future use </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.Mono12">
            <summary> two pixels in three bytes, see ::S_12BIT_2PACKED </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.MonoB">
            <summary> see ::E_CC_MONO8 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.MonoG">
            <summary> see ::E_CC_MONO8 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.MonoR">
            <summary> see ::E_CC_MONO8 </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RAW16">
            <summary> same as RAW8, but each value: high_byte,low_byte </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RAW8">
            <summary> one byte per pixel, byte order is RGGB, GBRG, GRBG or BGGR </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.SRGB16">
            <summary> six bytes per pixel, byte order is Rh,Rl,Gh,Gl,Bh,Bl, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.SMono16">
            <summary> two bytes per pixel, byte order is high_byte,low_byte </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RGB16">
            <summary> six bytes per pixel, byte order is Rh,Rl,Gh,Gl,Bh,Bl, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.Mono16">
            <summary> two bytes per pixel, byte order is high_byte,low_byte </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.RGB8">
            <summary> three bytes per pixel, byte order is RGBRGB, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.YUV444">
            <summary> three bytes per pixel, byte order is UYVUYV, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.YUV422">
            <summary> two pixels in four bytes, byte order is UYVYUYVY, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.YUV411">
            <summary> four pixels in six bytes, byte order is UYYVYYUYYVYY, </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniColorCode.Mono8">
            <summary> one byte per pixel, sequential byte order</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniRopMode">
            <summary>Enumeration for raster operation (used by re sampling methods) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniRopMode.LinInterpol">
            <summary> interpolating raster operation method </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniRopMode.SrcCpy">
            <summary> nearest neighbor raster operation method </summary>
        </member>
        <member name="T:E_UNI_IP_CONFIGURATION_MODE">
IP configuration modes.
*
* \note This enumeration is for GigE only.

</member>
        <member name="T:E_UNI_SETTINGS_SELECT_MODE">
Mode for selecting the camera entry from a settings file.

</member>
        <member name="T:E_UNI_LICENSE_TYPE">
License modes of Firegrab.

</member>
        <member name="T:E_TRIGGER_MODES">
Trigger modes.

</member>
        <member name="T:E_OUTPUTPIN_MODES">
Output Pin Modes.

</member>
        <member name="T:E_INPUTPIN_MODES">
Input Pin Modes.

</member>
        <member name="T:S_IIDC_VIDEOINFO_FORMAT">
IIDC video formats.

</member>
        <member name="T:S_IIDC_VIDEOINFO_MODE">
IIDC video modes.

</member>
        <member name="T:S_IIDC_VIDEOINFO_FPS">
Frame rates for IIDC video modes.

</member>
        <member name="T:S_UNI_TRANSPORT_FORMAT_INFO">
Structure providing an exact description of the image transfer format. 
* This structure is used for unified processing of 16-bit mono/raw images from Firewire and GigE-Vision cameras.

</member>
        <member name="T:E_UNI_SIMPLE_IMAGE_FORMAT">
Simple image type enum. 
*   Enumeration for simple image types with one image plane consisting of consecutive pixel (starting at the top of the image).

</member>
        <member name="T:S_YUV444">
Structure for accessing data in the YUV 4:4:4 format (YUV)
    prosilica component order

</member>
        <member name="T:S_BGRA8">
Structure for accessing Windows RGBA data.
</member>
        <member name="T:S_RGBA8">
Structure for accessing non-Windows RGB data 
</member>
        <member name="T:S_BGR8">
Structure for accessing Windows RGB data 
</member>
        <member name="T:S_RGB8">
Structure for accessing Windows RGB data 
</member>
        <member name="T:E_UNI_IMAGE_FORMAT">
Enumeration for output image formats supported by ::UCC_GetImage, ::UCC_GetImageEx, ::UCC_GrabImage and ::UCC_GrabImageEx.
* \note Image formats ::E_IF_MONO8 and ::E_IF_BGR have been removed due to accuracy issues. Instead, ::E_IF_MONO_8BPP
* and ::E_IF_BGR_24BPP should be used. Unlike their outdated counterparts, ::E_IF_MONO_8BPP and ::E_IF_BGR_24BPP
* consistently result in the expected image format. As a consequence, existing applications may require additional
* changes when the new formats are adopted. Alternatively, the macro USE_LEGACY_IMAGE_FORMATS can be defined for 
* existing projects. This activates compatibility definitions, allowing recompilation of existing applications 
* without any changes.

</member>
        <member name="T:S_SIS_DATA">
SIS data structure
</member>
        <member name="T:S_SIS_ENABLE.S_SIS_ENABLE_FIELDS">
states of the SIS data
</member>
        <member name="T:S_SIS_ENABLE">
information about state of the fields of the ::S_SIS_DATA struct
</member>
        <member name="T:E_UNI_SIS_TYPE">
enum for the secure image signatures
</member>
        <member name="T:S_TECHNO_INFO">
states of the support for different multimedia technologies
</member>
        <member name="T:S_SUPPORT_STATE">
states of the multi media technology support for operating system and processor.

</member>
        <member name="T:E_UNI_COLOR_CODE">
Enumeration for color codes / transfer formats.
These codes have their origin in standard IIDC color codes and AVT-specific color codes
used in format 7, but they are used for cameras on both interfaces.

</member>
        <member name="T:E_UNI_ROPMODE">
\endcond
Enumeration for raster operation (used by re-sampling methods).
</member>
        <member name="D:__1394GUID">
\cond UNI_CONTROL 
</member>
        <member name="D:HRESULT">
@name Special types.
Types for special purposes.

</member>
        <member name="D:UNICODE_CHAR">
@name Character types.
Types for character handling.

</member>
        <member name="D:INT8_TYPE">
@name Portable types.
Use these types.

</member>
        <member name="D:int16_t">
\file UniDefines.Net.cpp
* \brief This file implements the available AVT Universal API .Net Transform methods.
*
* All the AVT Universal API methods for controlling cameras that are publicly available are described
*  in this part of the help.
*
* \version 1.0
*
* \date 09-08-2007
*
* \author Philipp Beyer, Holger Eddelbuettel, Matthias Heidenreich, Olaf Reuter
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.

\file UniDefines.Net.h
* \brief This file describes data structures and enumerations used in the AVT Universal API .Net Transform module.
*
* All the AVT Universal API data structures and enumeration are described
*  in this part of the help.
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file uni_defines.h
 * \brief Definitions for the Universal API methods.
 *
 * This file describes all necessary definitions for using the
 *  Universal API methods.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

\file UNI_types.h
 * \brief Definitions for the types used in the Universal API methods.
 *
 * This file describes all necessary type definitions for using
 * methods of AVT's Universal API. These type definitions are designed to be
 * portable in the sense of Microsoft, means: also usable in VB and C#.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

@name Basic types used to define the types really used
Don't use these typedefs directly!


\file UniTransform.Net.cpp
* \brief This file describes all the available AVT Universal API .Net Transform methods.
*
* All the AVT Universal API methods for controlling cameras that are publicly available are described
*  in this part of the help.
*
* \version 1.0
*
* \date 09-08-2007
*
* \author Philipp Beyer, Holger Eddelbuettel, Matthias Heidenreich, Olaf Reuter
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file UniTransform.Net.h
* \brief This file describes all the available AVT Universal API .Net Transform methods.
*
* All the AVT Universal API methods for controlling cameras that are publicly available are described
*  in this part of the help.
* \note     Converting data to 16-bit formats is supported by the UniTransform module, 
*           but it is not fully supported by the NET 2.0 Bitmap. So saving and displaying 
*           16-bit data may fail with an exception.\n
*           Every function may throw an \ref AlliedVisionTech::UniTransformNet::UniTransformException "UniTransformException" or a ::System::Exception.
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file UniTransform.h
* \brief The available AVT Universal API Image Transformation methods.
*
* All the AVT Universal API methods for image transformation publicly available are described in this part of
*  the help. For a description see the section "\link intro Introduction
*  \endlink " of this help.
*
* \version 2.0.1
*
* \date 30-Oct-2009
*
* \author Holger Eddelbuettel, Matthias Heidenreich, Olaf Reuter
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file uni_defines.h
 * \brief Definitions for the Universal API methods.
 *
 * This file describes all necessary definitions for using the
 *  Universal API methods.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

\file UNI_types.h
 * \brief Definitions for the types used in the Universal API methods.
 *
 * This file describes all necessary type definitions for using
 * methods of AVT's Universal API. These type definitions are designed to be
 * portable in the sense of Microsoft, means: also usable in VB and C#.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

@name Basic types used to define the types really used
Don't use these typedefs directly!

</member>
        <!-- Discarding badly formed XML document comment for member 'T:S_UNI_IMAGE'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UIT_RGB8ToBGR(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UIT_Raw16ToBGR16(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UIT_Raw16ToBGR16_CC(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.Void*)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UIT_Raw16ToY16(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UIT_Raw16ToY16_CC(System.Void*,System.Void!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.Void*)'. -->
    </members>
</doc>