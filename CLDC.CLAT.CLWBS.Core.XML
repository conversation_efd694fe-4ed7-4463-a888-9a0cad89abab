<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.Core</name>
    </assembly>
    <members>
        <member name="P:CLDC.CLAT.CLWBS.Core.AssemblyManager`1.Sessions">
            <summary>
            反射类型集合
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.AssemblyManager`1.#ctor(System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.String)">
            <summary>
            构造方法
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.AssemblyManager`1.#ctor(System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String)">
            <summary>
            构造方法2
            </summary>
            <param name="assemblyString"></param>
            <param name="classNames"></param>
            <param name="appendSpace"></param>
            <param name="path"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase">
            <summary>
            功能描述：  设备通讯协议管理类
            作    者：
            编写时间：
            修改记录：
                        修改时间    修改人    修改内容
                        2014-2-10   谢恒      加密机升级，增加加密机函数ParameterUpdate,ParameterElseUpdate,ParameterUpdate1,
                                                  ParameterUpdate2,Meter_Formal_IdentityAuthentication,Meter_Formal_UserControl,
                                                  Meter_Formal_ParameterUpdate,Meter_Formal_ParameterElseUpdate,
                                                  Meter_Formal_ParameterUpdate1,Meter_Formal_ParameterUpdate2,Meter_Formal_InintPurse,
                                                  Meter_Formal_DataClear1,Meter_Formal_DataClear2,Meter_Formal_InfraredAuth,
                                                  Meter_Formal_MacCheck,Meter_Formal_KeyUpdateV2
                       
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Index">
            <summary>
            城市索引  用于地市加密机
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.IsSpecialEsam">
            <summary>
            是否为特殊Esam（用于存量部分 ESAM 未发行二次使用）0x00 – 标准 ESAM（已发行第二遍）0x01 – 特殊 ESAM（未发行第二遍）
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.equipSignalFlag">
            <summary>
            源控制信号量
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.LogReadWrite">
            <summary>
            log
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.TransitEquip">
            <summary>
            中转设备
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase._Stopwatch">
            <summary>
            计时器
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EquipMentPort">
            <summary>
            通讯通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Proctocol">
            <summary>
            协议
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.OutTimes">
            <summary>
            通讯超时时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EquipMentId">
            <summary>
            设备收信地址或表位编号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EquipManageMeterId">
            <summary>
            设备管控表位号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EquipMentClassName">
            <summary>
            通讯设备类名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReConnNum">
            <summary>
            重复通讯次数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.DebugMsgToFile(System.Int32,System.String)">
            <summary>
            发送日志到文件
            </summary>
            <param name="Msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.DebugMsgToFile(System.Int32,System.String,System.Int32)">
            <summary>
            发送日志到文件
            </summary>
            <param name="Msg">日志信息</param>
             <param name="id">设备Id</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendMsgToFile(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            发送日志到文件
            </summary>
            <param name="Msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendExMsg(System.Exception)">
            <summary>
            发送异常到文件
            </summary>
            <param name="ex">异常信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendMsg(System.String)">
            <summary>
            打印异常日志到TXT文件
            </summary>
            <param name="ex"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckCLT(System.Byte[])">
            <summary>
            检查是否符合CLT协议
            </summary>
            <param name="RevData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckDLT(System.Byte[])">
            <summary>
            检查是否符合DLT协议
            </summary>
            <param name="RevData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckTerminalFranme(System.Byte[])">
            <summary>
            校验终端南方电网协议
            </summary>
            <param name="RevData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckTerminalProtocol376(System.Byte[])">
            <summary>
            校验国网、云网终端协议
            </summary>
            <param name="RevData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartRevDataEvent">
            <summary>
            开始接收事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopRevDataEvent">
            <summary>
            停止接收事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CtRelayControl(System.Boolean)">
            <summary>
            顺德CT切换继电器切换蓝牙通道
            </summary>
            <param name="actTag">动作类型[0-开始 1-停止]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode,System.Int32)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCurrentGear(System.Single)">
            <summary>
            设置电流源定档
            </summary>
            <param name="current"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.PowerOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Boolean)">
            <summary>
            关源
            </summary>
            <param name="sourceParam">关源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.AllowPowerOn">
            <summary>
            释放关源后不能升源的信号量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetVotFalloff(System.Int32)">
            <summary>
            电压特殊输出
            </summary>
            <param name="intType">电压操作类型，0=电压跌落和短时中断，1=电压逐渐变化，2=逐渐关机和启动</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ClearAlarm">
            <summary>
            清除报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.QueryAlarmInfo(System.String@)">
            <summary>
            查询报警状态信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetEncryptionCode(System.Byte[])">
            <summary>
            获取标准表序列号
            </summary>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetEncryptionCode(System.Byte[],System.Byte[]@)">
            <summary>
            获取标准表序列号
            </summary>
            <param name="constant"></param>
            <param name="constant"></param>
            <returns></returns> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetYuanEncryptionCode(System.Byte[])">
            <summary>
            获取源序列号
            </summary>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetYuanEncryptionCode(System.Byte[],System.Byte[]@)">
            <summary>
            获取源序列号
            </summary>
            <param name="constant"></param>
            <param name="constant"></param>
            <returns></returns> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetStdMeterConst(System.UInt32@)">
            <summary>
            获取标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetStdMeterConst(System.UInt32)">
            <summary>
            设置标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            获取标准表监视数据
            </summary>
            <param name="monitorData">标准表监视数据</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetStdMeterEnergy(System.Int64@,System.UInt64@)">
            <summary>
            获取标准表累加电量和脉冲
            </summary>
            <param name="Energy">标准表电量</param>
            <param name="Pulses">标准表脉冲</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetStdMeterEnergy(System.Single@)">
            <summary>
            获取标准表累加电量
            </summary>
            <param name="Energy">标准表电量</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetStdMeterEnergyType(System.Byte,System.Byte,System.Byte)">
            <summary>
            设置标准表电能指示
            </summary>
            <param name="powerType">功率类型(0.有功 1.无功 2.视在)</param>
            <param name="phaseType">相别(0.总1.A相2.B相3.C相)</param>
            <param name="energyType">电能类型(0.总电能1.基波电能2.谐波总电能3.谐波分次电能)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ChangeStdMeterLineMode(CLDC.Framework.DataModel.Enum.EmLineMode,CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置标准表接线模式
            </summary>
            <param name="lMode">接线模式(3P3L,3P4L)</param>
            <param name="sMode">档位类型(auto-自动,manual-手动)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetStdMeterStartType(System.Byte)">
            <summary>
            设置标准表启动类型
            </summary>
            <param name="startType">启动类型(0.停止1.电能误差计算2.电能走字)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetStdMeterStall(System.Single,System.Single)">
            <summary>
            设置标准表档位
            </summary>
            <param name="vol">电压</param>
            <param name="cur">电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetUAndIStall(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            设置电压和电流档位
            </summary>
            <param name="ua">A相电压幅值</param>
            <param name="ub">B相电压幅值</param>
            <param name="uc">C相电压幅值</param>
            <param name="ia">A相电流幅值</param>
            <param name="ib">B相电流幅值</param>
            <param name="ic">C相电流幅值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCheckModeAndLine(CLDC.CLAT.CLWBS.DataModel.Enum.EmStaMtrCheckMode,CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置SRS400.3测量模式档位
            </summary>
            <param name="mode">测量模式</param>
            <param name="sMode">档位</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetStdMtrFrency(System.Single@)">
            <summary>
            读取频率
            </summary>
            <param name="Frency">频率</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetEnergyModel">
            <summary>
            设置标准表走字模式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterCheckParams(System.UInt32,System.UInt32)">
            <summary>
            设置标准表检定参数
            </summary>
            <param name="meterConst">被检标准表常数</param>
            <param name="pulseCount">校验圈数(脉冲个数)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMeterErr(System.Single@)">
            <summary>
            获取被检标准表误差
            </summary>
            <param name="meterErr">误差值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartTeleControlInfoState(System.Int32,System.Int32,System.Int32,System.Int32[])">
            <summary>
            启动或停止遥信输出
            </summary>
            <param name="StartOrStop">1：启动，0：停止</param>
            <param name="YXCount">路数</param>
            <param name="OUtWay">输出方式 0：电平，1：脉冲</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.AbortAllFunction(System.Int32[],System.Byte,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>{}
            停止或者开始误差板相应功能
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="actTag">动作类型[0-开始计算 1-停止计算]</param>
            <param name="chkType">检定类型</param>
            <param name="channel">通道号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetErrPanelParam(System.Int32[],System.UInt32,System.Single,System.UInt32,System.UInt32,System.Byte,System.Int32)">
            <summary>
            设置误差检定参数
            </summary>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetClockParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置日计时误差参数
            </summary>
            <param name="sClkFreq">标准时钟频率(单位: KHZ; 500KHZ)</param>
            <param name="cClkFreq">被检时钟频率(单位: HZ; 100HZ)</param>
            <param name="pClkCount">被检脉冲个数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetDemandParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置需量误差检定参数（CL188L）
            </summary>
            <param name="sClkFreq">标准时钟频率（单位：Hz）</param>
            <param name="cClkFreq">时间周期（单位：ms）</param>
            <param name="pClkCount">被检脉冲个数</param>
            <param name="channel">通道号</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPulseChannel(System.Int32[],CLDC.Framework.DataModel.Enum.EmPulseChannel,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置误差板脉冲通道
            </summary>
            <param name="plsChannel">被检脉冲通道[0-P+,1-P-,2-Q+,3-Q-,4-日计时,5-需量脉冲]</param>
            <param name="plsType">脉冲输入类型[0-电子式脉冲输入 1-感应式脉冲输入]</param>
            <param name="polar">共阴或共阳(0共阴,1共阳)</param>
            <param name="chkType">检定类型[0-电能误差 1-走字计数 2-预付费功能检定 3-对标 4-日计时误差 5-需量误差]</param>
            <param name="pulseSpeendType">脉冲类型[0-低速脉冲（普通电能表输出） 1-高速脉冲（标准电能表输出)]</param>
            <param name="countType">计数类型[0-脉冲计数 1-脉冲间隔时间]</param>
            <param name="photoelectricity">光电类型[0-电脉冲 1-光脉冲]</param>
            <param name="channel">通道号</param>
            <param name="divisionFactor">分频系数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCommMode(System.Int32,System.Int32[])">
            <summary>
            设置通讯模式
            </summary>
            <param name="mode">
            通讯模式[0-一对一模式485通讯 1-奇数表位485通讯 2-偶数表位485通讯 3-一对一模式红外通讯 
                     4-奇数表位红外通讯  5-偶数表位红外通讯 6-切换到485总线] 
            </param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ClearAllFaultState(System.Int32[],System.Byte)">
            <summary>
            清除所有表位故障状态
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="faultType">故障类型[1-接线故障状态 2-预付费跳闸状态 3-报警信号状态]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckErrMeterState(System.Int32[],System.Byte,System.Collections.Generic.List{System.Int32}@)">
            <summary>
            查询误差板当前误差及当前状态指令
            </summary>
            <param name="meterIds"></param>
            <param name="faultType">误差类型包括：电能误差（00）、需量周期误差（01）、日计时误差（02）、脉冲个数（03）、对标状态（04）</param>
            <param name="listErrAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InsulateFaultMeters(System.Int32[],System.Int32)">
            <summary>
            隔离故障表位电压电流
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="ctlType">控制类型[0-隔离解除 1-隔离]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCTtypeMeters(System.Int32[],System.Int32)">
            <summary>
            通过误差板控制设置CT档位
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="ctType">档位类型[档位类型0x01=100A 0x02=2A]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetEquiqMode(System.Int32[],System.Byte,System.Byte,System.Byte)">
            <summary>
            设误差板台体设备选择
            </summary>
            <param name="meterIds">表位号</param>
            <param name="MeterType">电能表类型 0表示为国网电能表，1表示南网电能表</param>
            <param name="MeterMode">0表示单项电能表,1表示三相电能表</param>
            <param name="CtType">0x00表示不带CT，0x01表示2030-3C,0x02表示2030-3B, 0x03表示2030-3A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetUAndIOutputLoop(System.Byte,System.Byte,System.Int32[])">
            <summary>
            选择电压和电流输出回路（CL188L）
            </summary>
            <param name="uLoop">电压回路[0-直接接入式 1-互感器接入式 2-表位无电表接入]</param>
            <param name="iLoop">电流回路[0-第一回路 1-第二回路]</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCommDoor(System.Byte,System.Byte,System.Int32[])">
            <summary>
            设置通讯通道选择
            </summary>
            <param name="oneDoorType">1路485[0-485 1-蓝牙通讯 2-232通讯]</param>
            <param name="twoDoorType">2路485[0-485 1-蓝牙通讯 2-232通讯]</param>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.QueryTenErrorPower(System.Collections.Generic.List{System.Int32},System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            获取误差板功耗
            </summary> 
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMorMeterTenErrValues(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@,System.Int32@,System.Int32,System.Int32,System.Int32)">
            <summary>
            获取多块表10次误差值
            </summary>
            <param name="MeterIds"></param>
            <param name="chkType"></param>
            <param name="DitpanelInfo"></param>
            <param name="DitLstData"></param>
            <param name="readType">“清远期间核查”误差读取类型 >=1表示读取误差板获取的脉冲数 </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMeterMoreErrValues(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@,System.Int32@,System.Int32,System.Int32)">
            <summary>
            获取多次误差数据
            </summary>
            <param name="MeterIds"></param>
            <param name="chkType"></param>
            <param name="DitpanelInfo"></param>
            <param name="DitLstData"></param>
            <param name="normalizingPulse"></param>
            <param name="channel"></param>
            <param name="meterSort"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMorMeterTempratureValues(System.Collections.Generic.List{System.Int32},System.Int32,System.Int32,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@)">
            <summary>
            获取多块表接线柱温度数据
            </summary>
            <param name="MeterIds">表位号</param>
            <param name="MeterType">表类型</param>
            <param name="AccessType">接入类型</param>
            <param name="DitpanelInfo"></param>
            <param name="DitLstData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckErrorPulse(System.Int32[])">
            <summary>
            查询电能误差检定时脉冲参数命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckClockFreAndAmountTime(System.Int32[])">
            <summary>
            查询日计时检定时钟频率及需量周期检定时间(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetRelayOverLoadTime(System.Int32[],System.Byte)">
            <summary>
            设置隔离继电器过载动作可靠性检测时间(CL188M)
            </summary>
            <param name="meterIds"></param>
            <param name="byMin">检测时间(s)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetRelayOverLoadTime(System.Int32[])">
            <summary>
            读取隔离继电器过载动作可靠性检测时间(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetErrorCurrent(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@)">
            <summary>
            扩展查询误差板当前误差命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetReadPrintInfo(System.Int32[])">
            <summary>
            读取打印信息命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetCurrentVersion(System.Int32[])">
            <summary>
            查询当前版本命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetVoltageCurr(System.Int32[])">
            <summary>
            查询表位电压电流隔离命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetQueryLoop(System.Int32[])">
            <summary>
            查询双回路命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetQueryPulseComm(System.Int32[])">
            <summary>
            查询被检脉冲通道及检定类型命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetDeviceInfoRead(System.Int32[])">
            <summary>
            检定装置信息读取命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetQueryRelayStatus(System.Int32[],System.Byte)">
            <summary>
            查询继电器状态命令(CL188M)
            </summary>
            <param name="meterIds"></param>
            <param name="relayID">继电器Id</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckErrMeterState(System.Int32[],System.Byte,System.Collections.Generic.Dictionary{System.Int32,System.Int32}@)">
            <summary>
            查询误差板当前误差及当前状态指令
            </summary>
            <param name="meterIds"></param>
            <param name="faultType">误差类型包括：电能误差（00）、需量周期误差（01）、日计时误差（02）、脉冲个数（03）、对标状态（04）</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterMode(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            模式切换
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>        
            <param name="OutEndata1">密文1</param>        
            <param name="OutModeNo">费控模式状态字</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterInfraredStatusAticPKey(System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            红外安全认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata1">密文1</param>        
            <param name="Data">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterInfraredStatusAticPKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            红外安全认证（串口服务器转换板）
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata1">密文1</param>        
            <param name="Data">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMetersAddress(System.String@)">
            <summary>
            获取电能表的通讯地址
            </summary>
            <param name="meterIDs">表位数组</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMetersAddress(System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            获取电能表的通讯地址
            </summary>
            <param name="meterIDs">表位数组</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadMetersEnergy(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            读取电能表各费率和总电能(单位: kWh)
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="meterAddrs">通讯地址数组</param>
            <param name="powerType">(1=正向有功2=反向有功3=正向无功4=反向无功)</param>
            <param name="workType">工作类型(0-操作,1-载波,2-红外)</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterTime(System.String,System.String,System.String,System.DateTime)">
            <summary>
            设置多块表的时间
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dateTime">时间</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterDate(System.String,System.String,System.String,System.DateTime)">
            <summary>
            设置多块表的日期
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dateTime">时间</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterPulseType(System.String,System.String,System.String,System.String)">
            <summary>
            设置多块表的多功能端子
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="pulseType">脉冲类型：00-时钟秒脉冲，01-需量周期，02-时段投切</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMacAdress(System.String,System.String,System.String,System.String)">
            <summary>
            设置蓝牙Mac地址
            </summary>
            <param name="commAddrs">地址 默认全A</param>
            <param name="localMacAddr">本地Mac</param>
            <param name="salaveMacAddr">从机Mac</param>
            <param name="power">功率</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMacAdress(System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.String)">
            <summary>
            设置蓝牙Mac地址（串口服务器转换板）
            </summary>
            <param name="commAddrs">地址 默认全A</param>
            <param name="localMacAddr">本地Mac</param>
            <param name="salaveMacAddr">从机Mac</param>
            <param name="power">功率</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterModeSwicth(System.String,System.Int32,System.String,System.String)">
            <summary>
            校表模式切换
            </summary>
            <param name="commAddrs">地址 默认全A</param>
            <param name="meterId">表号</param>
            <param name="channel">通道号  低4位有效（有功、无功）</param>
            <param name="power"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlMorMeterClear(System.String,System.String,System.String)">
            <summary>
            清电表电量
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="medType">通讯媒介</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlMaxDemandClear(System.String,System.String,System.String)">
            <summary>
            清电表需量
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="medType">通讯媒介</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadSinMeterData(System.String,System.String,System.String,System.String,System.String@,System.Int32,System.Boolean)">
            <summary>
            读表任意数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadSinMeterData(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String}@,System.Int32,System.Boolean)">
            <summary>
            读表任意数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterFullDemand(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            读电表各费率需量及总需量
            </summary>
            <param name="commAddr">表地址</param>
            <param name="wattType">做功方向</param>
            <returns>需量集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterSpareMoney(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表剩余金额
            </summary>
            <param name="commAddr">表地址</param>
            <returns>金额</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterJtdj(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表阶梯电价
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>电价</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterSpareEng(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表中剩余电量
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterRunState(System.String,System.String,System.String,CLDC.CLAT.CLWBS.DataModel.Struct.StMeterRunState@)">
            <summary>
            获取电表运行状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>返回电能表运行状态</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterRunState(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StMeterRunState}@)">
            <summary>
            获取电表运行状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>返回电能表运行状态</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSinMeterRunState(System.String,System.String,System.String,CLDC.CLAT.CLWBS.DataModel.Struct.StRunState@)">
            <summary>
            获取电表运行状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>返回电能表运行状态</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetEsamFileData(System.String,System.String,System.String,System.Byte,CLDC.CLAT.CLWBS.DataModel.Struct.StEsamRunFile@,System.String@)">
            <summary>
            抄表ESAM中数据
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="fileType">文件类型[0-密钥文件 2-参数信息文件 5-密钥信息文件 6-运行信息文件 7-控制命令文件]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterWriteData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            写电表任意数据
            </summary>
            <param name="commAddr"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="dataCode"></param>
            <param name="strWriteData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlSinMeterState(System.String,System.String,System.String,System.String)">
            <summary>
            控制电能表状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="Endata">控制数据，拉合闸报警保电密文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ChangeSinMeterPassWord(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            密码修改
            </summary>
            <param name="commAddr">表地址</param>
            <param name="dataCode">数据标识</param>
            <param name="handleCode">操作者代码</param>
            <param name="PA">密码等级</param>
            <param name="OldPassWord">旧密码</param>
            <param name="NewPassWord">新密码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterPullSwitch(System.String,System.String,System.String,System.Byte,System.Byte)">
            <summary>
            设置电表跳闸延时时间
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="ctlType">控制类型[0-跳闸 1-合闸 2-报警 3-报警解除 4-保电 5-保电解除]</param>
            <param name="times">有效截止时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterStatusAticPKey(System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutRand1">随机数1</param>
            <param name="OutEndata1">密文1</param>
            <param name="PutDiv">分散因子</param>
            <param name="OutRand2">返回随机数2</param>
            <param name="OutESAMNo">返回ESAM序列号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterStatusAticPKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String}@,System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            安全认证（串口服务器转换板）
            </summary>
            <param name="Meteraddress"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="OutRand1"></param>
            <param name="OutEndata1"></param>
            <param name="PutDiv"></param>
            <param name="OutRand2"></param>
            <param name="OutESAMNo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterExternalStatusAticPKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            外部安全认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata2">密文2</param>
            <param name="PutDiv">分散因子</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterBroadcastData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            写电表广播数据
            </summary>
            <param name="commAddr"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="dataCode"></param>
            <param name="strWriteData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterKeyUpdata_Main(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            主控密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo1">密钥信息</param>
            <param name="OutKeyInfo1">MAC</param>
            <param name="OutKey1">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterKeyUpdata_Control(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            控制命令密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo2">密钥信息</param>
            <param name="OutKeyInfo2">MAC</param>
            <param name="OutKey2">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterKeyUpdata_Params(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            参数更新，密钥
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo3">密钥信息</param>
            <param name="OutKeyInfo3">MAC</param>
            <param name="OutKey3">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSinMeterKeyUpdata_Identity(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            身份认证密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo4">密钥信息</param>
            <param name="OutKeyInfo4">MAC</param>
            <param name="OutKey4">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ClearSinMeterKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            密钥信息清零
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="macAddr">MAC</param>
            <param name="keyData">密钥信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetErrorCalibration(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            校准日计时误差
            </summary>
            <param name="dataCode">功能码</param>
             <param name="ClockError">日计时误差 第一传空</param>
            <param name="passWord">操作密码</param>
            <param name="handleCode">操作者代码</param> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptClearMeter(System.String,System.String,System.String,System.String)">
            <summary>
            加密方式电能表清零
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，N1～Nm为密文。N1～Nm解密后的明文数据为R1～R8，其中R1=1AH，R2保留，默认为00H，R3～R8代表命令有效截止时间，数据格式为YYMMDDhhmmss。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptInintPurse(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            加密方式电能表钱包初始化
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，数据格式为  分，四个字节。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptReadMeterData(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            加密方式读数据
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata">密文数据</param>
            <param name="handlCode">操作者代码</param>
            <param name="RevData">收到的数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptWriteMeterData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加密方式写数据
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata">写入的数据，98H级密码权限代表通过密文+MAC的方式进行数据传输，不需要密码验证也不需要按编程键配合。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptClearDemand(System.String,System.String,System.String,System.String)">
            <summary>
            加密方式最大需量清零
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，N1～Nm为密文。N1～Nm解密后的明文数据为R1～R8，其中R1=19H，R2保留，默认为00H，R3～R8代表命令有效截止时间，数据格式为YYMMDDhhmmss。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptUpdateKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            2013新的方式密钥下装，一次4条密钥指令下发，共20条密钥。
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutData">加密机返回的密钥信息，4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.EncryptUpdateKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            2013新的方式密钥下装，一次4条密钥指令下发，共20条密钥。
            </summary>
            <param name="dicCommAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="dicOutData">加密机返回的密钥信息，4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetLightColor(CLDC.Framework.DataModel.Enum.EmLightColor)">
            <summary>
            设置指示灯颜色
            </summary>
            <param name="color">指示灯颜色</param>
            <returns>true-成功,false-失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.DLRelayControl(System.Byte)">
            <summary>
            切换大电流
            </summary>
            <param name="linkType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.DYRelayControl(System.Byte)">
            <summary>
            CT电源控制，直接接入式接通15V电源。互感切断CT电源切换电压
            </summary>
            <param name="linkType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarrierRelayControl(System.Byte)">
            <summary>
            载波时，切换2041与功率源和表同回路
            </summary>
            <param name="linkType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.RelayControl(System.Boolean)">
            <summary>
            开市电继电器
            </summary>
            <param name="isOpen">开关状态[True-闭合 False-断开]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ChangUbRelayControl(System.Byte)">
            <summary>
            单相表切换B相电压
            </summary>
            <param name="linkType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.RelayControl(System.Byte)">
            <summary>
            开市电继电器
            </summary>
            <param name="ControlType">控制方式0x00=,全开,0x01=第1路闭合,0x02=第2路闭合,0x03=1,2路同时闭合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ModifyMapped(System.Byte)">
            <summary>
            修改卡槽和替代卡映射关系
            </summary>
            <param name="CardNum">卡槽号：0x00,0x01,0x02,0x03</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMapped(System.Byte@,System.Byte@)">
            <summary>
            获取卡槽对应关系
            </summary>
            <param name="CardNum">卡槽号</param>
            <param name="CardNo">替代卡号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSampleFreq(System.Byte,System.Single@)">
            <summary>
            获取频率
            </summary>
            <param name="s8Freq">频率（单位：Hz）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetAllMeterIValue(System.Byte,System.Single[]@)">
            <summary>
            获取全部表位电流值
            </summary>
            <param name="values">电流值数组（单位：mA，L=承载数）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetAllMeterState(System.Byte,System.Byte[]@)">
            <summary>
            获取全部表位状态
            </summary>
            <param name="states">表位状态数组[0-正常 1-故障]</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetILimenValue(System.Byte,System.Single)">
            <summary>
            设置电流阈值
            </summary>
            <param name="s8Limen">电流阈值（单位：mA；范围：0.1mA-12.0mA）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlWork(System.Byte,System.Int32,System.Byte[])">
            <summary>
            控制表位工作
            </summary>
            <param name="actTag">动作类型[0-耐压准备 1-放电]</param>
            <param name="ctlFigures">控制字数组[0-不操作 1-操作]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlWork(System.Byte,System.Byte)">
            <summary>
            控制表位工作
            </summary>
            <param name="actTag">动作类型[0-复位 1-电压端子短接对地打耐压 2-三相电流短接对地打耐压 3-电压短接对电流短接打耐压 
            4-A相电流对B相电流打耐压 5-A相电流对C相电流打耐压 6-B相电流对C相电流打耐压 7-放电]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ResetAllMeterState(System.Byte)">
            <summary>
            重置全部表位状态
            </summary>
            <param name="cellID">单元编号</param>
            <returns>True-重置成功；False-重置失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSampleFreq(System.Single@)">
            <summary>
            获取频率
            </summary>
            <param name="s8Freq">频率（单位：Hz）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetAllMeterIValue(System.Single[]@)">
            <summary>
            获取全部表位电流值
            </summary>
            <param name="values">电流值数组（单位：mA，L=承载数）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetAllMeterState(System.Byte[]@)">
            <summary>
            获取全部表位状态
            </summary>
            <param name="states">表位状态数组[0-正常 1-故障]</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetILimenValue(System.Single)">
            <summary>
            设置电流阈值
            </summary>
            <param name="s8Limen">电流阈值（单位：mA；范围：0.1mA-12.0mA）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlWork(System.Int32,System.Byte[])">
            <summary>
            控制表位工作
            </summary>
            <param name="actTag">动作类型[0-耐压准备 1-放电]</param>
            <param name="ctlFigures">控制字数组[0-不操作 1-操作]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlWork(System.Byte)">
            <summary>
            控制表位工作
            </summary>
            <param name="actTag">动作类型[0-复位 1-电压端子短接对地打耐压 2-三相电流短接对地打耐压 3-电压短接对电流短接打耐压 
            4-A相电流对B相电流打耐压 5-A相电流对C相电流打耐压 6-B相电流对C相电流打耐压 7-放电]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ResetAllMeterState">
            <summary>
            重置全部表位状态
            </summary>
            <param name="cellID">单元编号</param>
            <returns>True-重置成功；False-重置失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SelectOverLoaded(System.Byte@)">
            <summary>
            查询信号是否过载
            </summary>
            <param name="type">0x00代表没过载，0x01代表过载</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCtType(System.Byte)">
            <summary>
            控制CT档位
            </summary>
            <param name="type">0x00代表100A，0x01代表2A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SelectCtType(System.Byte@)">
            <summary>
            查询CT档位
            </summary>
            <param name="type">0x64代表100A，0x02代表2A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlCT(System.Byte[])">
            <summary>
            CT继电器控制
            </summary>
            <param name="controls">CT继电器选择断开和闭合，
            长度为8,
            第一位为0代表A相断开，为1代表A相闭合，
            第二位为0代表B相断开，为1代表B相闭合，
            第三位为0代表C相断开，为1代表C相闭合，
            第五位为0代表A相不被控制，为1代表控制A相，
            第六位为0代表B相不被控制，为1代表控制B相，
            第七位为0代表C相不被控制，为1代表控制C相，</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ControlCTWireMode(System.Byte)">
            <summary>
            CT继电器控制
            </summary>
            <param name="type">0x33代表三相三线,0x34代表三线四线</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetLostPower(System.Int32,System.Byte,System.Single@,System.Single@,System.Single@)">
            <summary>
            读功耗
            </summary>
            <param name="type">相位，1=电压A相，2=电流A相,3=电压B相，4=电流B相，5=电压C相，6=电流C相</param>
            <param name="Rxid">功耗板地址</param>
            <param name="flt_P">有功</param>
            <param name="flt_Q">无功</param>
            <param name="flt_S">视在</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetPressFreq(System.Single@)">
            <summary>
            获取频率
            </summary>
            <param name="sfFreq">频率（单位：Hz）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetPressIValue(System.Single@)">
            <summary>
            获取电流值
            </summary>
            <param name="sfIValue">电流值（单位：mA）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetPressUValue(System.Single@)">
            <summary>
            获取电压值
            </summary>
            <param name="sfUValue">电压值（单位：V）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetPressState(System.Byte@)">
            <summary>
            获取耐压状态
            </summary>
            <param name="state">状态[0-正常 1-超过阈值]</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPressILimen(System.Single)">
            <summary>
            设置电流阈值
            </summary>
            <param name="sfLimen">阈值（单位：mA；范围：1.0mA-120.0mA）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPressUValue(System.Single)">
            <summary>
            设置电压值
            </summary>
            <param name="sfUValue">电压值（单位：V；范围：220.0V-6000.0V）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetWorkTime(System.Int32)">
            <summary>
            设置工作时间
            </summary>
            <param name="workTime">工作时间（单位：s；范围：1s-999s）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CL2038_ControlWork(System.Byte)">
            <summary>
            控制耐压工作
            </summary>
            <param name="actTag">动作类型[0-关源 1-升源]</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ResetPressState">
            <summary>
            重置工作状态
            </summary>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CheckAddress">
            <summary>
            查询CL2046地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.OpenLED">
            <summary>
            开LED灯
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CloseLED">
            <summary>
            关LED灯
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.LoginServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密机服务器
            </summary>
            <param name="ip">加密机IP</param>
            <param name="port">加密机端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">密码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.LgoutServer">
            <summary>
            断开与服务器的连接
            </summary>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ClseUsbkey">
            <summary>
            释放服务器登录权限
            </summary>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Create_Rand(System.String@)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1">8字节随机数</param>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SI_IdentityAuthentication(System.String,System.String@,System.String@,System.String@)">
            <summary>
            私钥认证
            </summary>
            <param name="MeterAddress"></param>
            <param name="OutRand"></param>
            <param name="OutEndata"></param>
            <param name="PutDiv"></param>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_ExternalAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            外部认证
            </summary>
            <param name="Flag">密钥状态</param>
            <param name="PutRand">电表随机数</param>
            <param name="PutDiv">输出的分散因子</param>
            <param name="OutEndata">输出的密文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.UserControl(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            控制命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            远程二类参数设置加密函数,用于形成二类参数设置645命令 
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(需要特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            清除密钥信息
            </summary>
            <param name="flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="rand">输入的随机数2(字符型,长度8)</param>
            <param name="div">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="EsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyInfo">输入的远程控制密钥信息明文(字符型)</param>
            <param name="Key1">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="KeyInfo1">输出的远程控制密钥信息(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.KeyUpdate(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥下载密文信息
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>       
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SwichChargeMode(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            费控模式切换
            </summary>
            <param name="Flag">电表秘钥状态：1</param>        
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入参数明文，包含费控模式状态字+购电金额+购电次数</param>        
            <param name="OutData">输出：费控模式状态字+4字节MAC1+购电金额+购电次数+MAC2</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadCPResult(System.String,System.String,System.String,System.Collections.Generic.List{System.String}@,System.String@)">
            <summary>
            充电桩充电
            </summary>
            <param name="meterInfo">指令</param>
            <param name="lstResult">返回参数</param>
            <param name="errorStr">错误</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTeleControlInfoState(System.Byte,System.Byte)">
            <summary>
            遥信状态（1200H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTeleControlState(System.Byte,System.Byte,System.Byte)">
            <summary>
            遥控状态（1201H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="type"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTransformerState(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            互感器状态（1202H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="stateA"></param>
            <param name="stateB"></param>
            <param name="stateC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalRS485State(System.Byte,System.Byte)">
            <summary>
            终端RS485接入状态（1203H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetOutputPulse(System.Byte,System.Int16,System.String,System.Byte,System.String)">
            <summary>
            设置输出频率（1204H~120aH）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="frequence"></param>
            <param name="dutyRadio"></param>
            <param name="pulseNum"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterConstant(System.Byte,System.String)">
            <summary>
            设置标准表常数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCheckedMeterConstant(System.Byte,System.Int16,System.String)">
            <summary>
            第一路被检表常数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCheckedMeterCircleNum(System.Byte,System.Int16,System.String)">
            <summary>
            第一路被检表校验圈数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="circle"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterFrequence(System.Byte,System.String)">
            <summary>
            设置标准表时钟频率（1005H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="frequence"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCheckedMeterFrequence(System.Byte,System.Int16,System.String)">
            <summary>
            设置被检表时钟频率（1006H，1012H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="frequence"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadClockErr(System.Byte,System.Int16,System.Int16@,System.String@)">
            <summary>
            读日计时误差（1015H，1017H）  
            Byte4Byte5:次数
             (Byte0-Byte3):误差植
             Byte0: 最高位bit7=0正，1=负;
             Bit3-Bit0小数点位数（从后算起）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="dayErrValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadBaseErr(System.Byte,System.Int16,System.Int16@,System.String@)">
            <summary>
            读基本误差（1016H，1018H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="baseErrNum"></param>
            <param name="baseErrValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadTeleControlState(System.Byte,System.Byte@,System.Byte@)">
            <summary>
            读遥控状态（1201H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="type"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartDayClock(System.Byte,System.Byte)">
            <summary>
            启动读日计时误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartBaseError(System.Byte,System.Byte)">
            <summary>
            启动读基本误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartOutputPulse(System.Byte,System.Byte)">
            <summary>
            启动脉冲输出功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopDayClock(System.Byte,System.Byte)">
            <summary>
            停止读日计时误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopBaseError(System.Byte,System.Byte)">
            <summary>
            停止读基本误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopOutputPulse(System.Byte,System.Byte)">
            <summary>
            停止脉冲输出功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarrierEquipInit">
            <summary>
            硬件初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCommunicationMode(System.Int32)">
            <summary>
            设置载波通讯方式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarrierParamInit">
            <summary>
            载波参数区初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarrierDataInit">
            <summary>
            载波数据区初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadCarrierAddress">
            <summary>
            读取载波主节点地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPrimaryNode(System.String)">
            <summary>
            设置主节点地址
            </summary>
            <param name="address"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCarrierRoute(System.String,System.Int32)">
            <summary>
            载波路由设置
            </summary>
            <param name="MeterAddress"></param>
            <param name="commType">通讯方式：1为集中式窄带，2为分布式窄带，3为宽带</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCarrierRoute(System.Collections.Generic.List{System.String})">
            <summary>
            表地址集合
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCarrierRoute(System.String[],System.Int32)">
            <summary>
            载波路由设置
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendDataRoute(System.Byte[],System.Byte[]@)">
            <summary>
            路由转发数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendDataDirect(System.Byte[],System.Byte[]@)">
            <summary>
            直接转发数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMeterEng(System.String)">
            <summary>
            载波抄电量组帧
            </summary>
            <param name="MeterAddress">表地址</param>
            <returns>组帧</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendCarrierFrameDirect(System.Byte[],System.Byte[]@)">
            <summary>
            发送读取载波参数数据帧
            </summary>
            <param name="sendMsgBuff"></param>
            <param name="revDataBuff"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetReadCarrierFrame(System.String,System.Collections.Generic.List{System.Byte},System.String)">
            <summary>
            读取载波参数指令
            </summary>
            <param name="MeterAddress"></param>
            <param name="IDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParseReadCarrierFrame(System.Byte[],System.Collections.Generic.List{System.Byte},System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            解析读取载波参数报文
            </summary>
            <param name="RevData"></param>
            <param name="IDs">元素ID集合</param>
            <param name="data">key：元素ID，value：元素ID对应的值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParseMeterEng(System.Byte[],System.Collections.Generic.List{System.Single}@)">
            <summary>
            解析载波抄电量
            </summary>
            <param name="RevData"></param>
            <param name="lstEng"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMeterData(System.String,System.String)">
             <summary>
             抄电表数据组帧
             </summary>
             <param name="MeterAddress">表地址</param>
            <param name="oad">数据标识</param>
             <returns>组帧</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParseMeterData(System.Byte[],System.String@)">
            <summary>
            解析载波抄表数据
            </summary>
            <param name="RevData"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadCoreID(System.Int32,System.Int32)">
            <summary>
            读取ID信息
            </summary>
            <param name="count"></param>
            <param name="StartNum"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadNodeCount">
            <summary>
            读取节点数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParseCoreData(System.Byte[],System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            解析
            </summary>
            <param name="RevData"></param>
            <param name="dicCoreInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadNetworking(System.Int32,System.Int32)">
            <summary>
             读取组网信息
            </summary>
            <param name="count">节点数量</param>
            <param name="startNum">开始节点</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetCL2041Channel(System.Int32)">
            <summary>
            切换CL2041通道
            </summary>
            <param name="type">通道</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SelectCtnPulse(System.Byte)">
            <summary>
            191B设置脉冲类型
            </summary>
            <param name="pulseType">脉冲类型[0-标准电能脉冲 1-标准时钟脉冲]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SwitchRelay(System.Byte,System.Byte)">
            <summary>
            继电器控制
            </summary>
            <param name="relayId">继电器ID</param>
            <param name="actTag">动作类型[0-断开 1-闭合]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InfraredRayGetMeterEnergy(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            红外方式读取电量
            </summary>
            <param name="meterAddrs">电表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="powerType">做功类型</param>
            <param name="LstEng">返回电量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadMeterData(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            红外方式读取任意数据
            </summary>
            <param name="meterAddrs">电表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="dataCode">操作者</param>
            <param name="ReadData">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InfraredRayReadTime376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            南方电网
            </summary>
            <param name="terminalInfo"></param>
            <param name="terminalTime"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InfraredRayReadTime_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            国网、云网协议
            </summary>
            <param name="terminalInfo"></param>
            <param name="terminalTime"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetTemperature(System.Collections.Generic.Dictionary{System.Int32,System.Single}@,System.Collections.Generic.List{System.Int32},System.Byte,System.Byte)">
            <summary>
            读取温度监视器
            </summary>
            <param name="temperature">返回各2151上对应各表位的温度</param>
            <param name="lstMeterIDs">各2151对应的受信地址</param>
            <param name="startChannelNum">起始通道</param>
            <param name="askChannelAmount">通道数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetTemperature(System.Boolean,System.Single@)">
            <summary>
            中鼎温湿度仪设备读取温度
            </summary>
            <param name="TypeValue">温度:True;湿度:False</param>
            <param name="Temperature"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ScanCode(System.Int32,System.String@,System.String@)">
            <summary>
            条码扫描
            </summary>
            <param name="BarCodeLen">条码长度</param>
            <param name="BarCode">输出的条码数据</param>
            <param name="ErrCode">输出的错误代码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartPicProcess(System.Collections.Generic.List{System.String},System.Byte,System.String@)">
            <summary>
            开始处理图像
            </summary>
            <param name="lstCodes">表条码集合</param>
            <param name="bThick">高表还是矮表(0x01:高表, 0x00:矮表)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetPicResult(System.Collections.Generic.List{System.Boolean}@,System.String@)">
            <summary>
            读取检测结果(0x11)	
            </summary>
            <param name="lstResults">检测结果集合</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetIRTextResult(System.String@,System.Single@)">
            <summary>
            获取绝缘电阻测试结果
            </summary>
            <param name="ResultStatus">结论状态字</param>
            <param name="TextValue">绝缘电阻测试值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartIRText">
            <summary>
            启动绝缘电阻测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopIRText">
            <summary>
            停止绝缘电阻测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetIRTextMode">
            <summary>
            设置绝缘仪IR测试方式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetIRTextPRM(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置绝缘仪IR测试参数
            </summary>
            <param name="TextVoltage">测试电压</param>
            <param name="MinLimit">电阻下限</param>
            <param name="MaxLimit">电阻上限</param>
            <param name="TestTime">测试时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadIRTextPRM(System.Int32@,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            读取绝缘仪IR测试参数
            </summary>
            <param name="TextVoltage">测试电压</param>
            <param name="MinLimit">电阻下限</param>
            <param name="MaxLimit">电阻上限</param>
            <param name="TestTime">测试时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.VoltageTextControl(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32@)">
            <summary>
            冲击电压控制
            </summary>
            <param name="VoltageValue">电压控制赋值[0-手调状态下的程控输出 1-12对应控制冲击电压为1KV-12KV,13停止试验]</param>
            <param name="Num">冲击次数</param>
            <param name="SpaceTime">间隔时间</param>
            <param name="WorkMode">工作模式[0-正极性测试 1-负极性测试]</param>
            <param name="BreakDownNum">击穿次数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendDataControll(System.String,System.Collections.Generic.List{System.Single}@)">
            <summary>
            操控器发送数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadTerminalInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            南方电网读取终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="str">返回信息，多个信息以|分隔开</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalIPInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端IP信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置IP信息（通讯模式|TCP端口|终端IP|子网掩码|网关地址|IP获取的设置方式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalMasterIP376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端主站信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的主站信息（主站IP|端口号|通信通道模式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartRevTerinalLinkThread">
            <summary>
            南方电网开启一个线程处理预链接（登录和心跳）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopTerinalLinkThread">
            <summary>
            通用登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadTerminalInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            国网、云网读取终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="str">返回信息，多个信息以|分隔开</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalIPInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端IP信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置IP信息（通讯模式|TCP端口|终端IP|子网掩码|网关地址|IP获取的设置方式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalMasterIP_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端主站信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的主站信息（主站IP|端口号|通信通道模式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StartRevTerinalLinkThread_376">
            <summary>
            国网、云网开启一个线程处理预链接（登录和心跳）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.StopTerinalLinkThread_376">
            <summary>
            国网、云网登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadTerminalInfoYN376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            读取终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="str">返回信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetTerminalInfoYN376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            设置终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Connect(System.String,System.String,System.String)">
            <summary>
            连接加密机
            </summary>
            <param name="ip"></param>
            <param name="port"></param>
            <param name="outTime"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CreateRand(System.String@)">
            <summary>
            创建随机数
            </summary>
            <param name="OutRand1"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetInitSessionData(System.Int32,System.String,System.String,System.String@,System.String@,System.String@)">
            <summary>
            获取会话协商数据
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Asctr"></param>
            <param name="outRand"></param>
            <param name="outSessionData"></param>
            <param name="outMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetInitSessionDataJL(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
            <summary>
            获取会话协商数据
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Asctr"></param>
            <param name="outRand"></param>
            <param name="outSessionData"></param>
            <param name="outMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetVerifySessionData(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            会话协商验证
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Rand"></param>
            <param name="SessionData"></param>
            <param name="Sign"></param>
            <param name="SessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetVerifySessionDataJL(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            会话协商验证
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Rand"></param>
            <param name="SessionData"></param>
            <param name="Sign"></param>
            <param name="SessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetSessionData(System.Int32,System.String,System.String,System.Int32,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            获取下发参数数据：设置，操作
            </summary>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="TaskType"></param>
            <param name="TaskData"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.VerifyMeterData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            电能表回复帧进行数据验证和解密。
            </summary>
            <param name="KeyState"></param>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="Data"></param>
            <param name="Mac"></param>
            <param name="outData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.KeyUpdate(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥更新
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InitKey(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥初始化
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.PurseOperation(System.Int32,System.String,System.String,System.Int32,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            钱包操作
            </summary>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="TaskType">任务序编号，9 钱包初始化；10，钱包充值；11，钱包退费</param>
            <param name="TaskData"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetESAMData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            Esam参数（用于设置表号、当前套电价文件、备用套电价文件、ESAM 存储标）
            </summary>
            <param name="KeyState"></param>
            <param name="OperateMode"></param>
            <param name="EsamId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="ESAMRand"></param>
            <param name="Data"></param>
            <param name="OutSID"></param>
            <param name="OutAddData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SatartRevLinkFrameThread(System.String)">
            <summary>
            开启一个线程处理预链接（登录和心跳）
            </summary>
            <param name="Address"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadWirelessSpectrum">
            <summary>
            读取无线通信频段
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetWirelessSpectrum(System.Int32,System.Int32)">
            <summary>
            设置无线通信频段
            </summary>
            <param name="modulationMethod">无线调制方式</param>
            <param name="channelNumber">无线信道编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadNetworkType">
            <summary>
            读取双模组网方式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetNetworkType(CLDC.CLAT.CLWBS.DataModel.Enum.EmNetworkType)">
            <summary>
            设置双模组网方式
            </summary>
            <param name="networkType"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetReadMeterData(System.String,System.String)">
            <summary>
            读表数据
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ParseMeterDatas(System.Byte[],System.Byte[]@)">
            <summary>
            解析读取表数据
            </summary>
            <param name="RevData">收到数据</param>
            <param name="DataRegion">数据域</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetBuildBluetoothInstruct(System.String,CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel)">
            <summary>
            发送国网蓝牙指令
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="bluprm">蓝牙参数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetBuildBluetoothInstruct(System.Collections.Generic.Dictionary{System.Int32,System.String},CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel)">
            <summary>
            发送国网蓝牙指令（串口服务板）
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="bluprm">蓝牙参数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetErrorCalibrations(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            日计时误差校准
            </summary>
            <param name="dataCode">校准代码</param>
            <param name="passWord">操作密码</param>
            <param name="handleCode">操作者代码</param> 
            <param name="ClockError">日计时误差</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.WriteData(System.String,System.String,System.String,System.String,System.String,System.Int32,System.Byte[]@,System.Byte[]@,System.Boolean)">
            <summary>
            写表信息
            </summary>
            <param name="Addrs">地址表s</param>
            <param name="dataCode">功能代码</param>
            <param name="passWord">操作密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="ClockError">下发参数</param>
            <param name="outTime">超时时间</param>
            <param name="isOldProtocol">是否为老协议（DLT 645-1997）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.MeterEventCleal(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            事件清零
            </summary>
            <param name="Addrs"></param>
            <param name="dataCode"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.RestorationBaudRate(System.String,System.Byte,System.Byte@)">
            <summary>
            复位波特率
            </summary> 
            <param name="commAddr">通信地址</param> 
            <param name="taggedWord"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.Identity_AuthenticationTime(System.String,System.String,System.String)">
            <summary>
            身份认证有效时间设置
            </summary>
            <param name="commAdd"></param>
            <param name="code"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetMeterPressureValue(System.Int32,System.Int32,CLDC.CLAT.CLWBS.DataModel.Pressure.PressureModel@)">
            <summary>
            获取压力值
            </summary>
            <param name="MeterId">表位号</param>
            <param name="communicationID">表位号</param>
            <param name="outData">表位号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetZeroValue(System.Int32,System.Int32)">
            <summary>
            手动置零
            </summary>
            <param name="MeterId">表位ID</param>
            <param name="communicationID">通讯地址</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetAddressValue(System.Int32,System.Int32,System.Int32)">
            <summary>
            设置通讯地址
            </summary>
            <param name="MeterID"></param>
            <param name="communicationID"></param>
            <param name="newAddres"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CloseOrOpenSystemConfigure(System.Int32,System.Int32,System.String)">
            <summary>
            解锁/锁定系统配置
            </summary>
            <param name="MeterId">表位号</param>
            <param name="communicationID">通讯地址</param>
            <param name="value">值：5AA5解锁系统配置、其他任意值锁定系统配置</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarveInitTemplate(System.String)">
            <summary>
            初始化
            </summary>
            <param name="templateName">模板名称</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarveSenData(System.String)">
            <summary>
            数据传输
            </summary>
            <param name="data">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.CarveMarkStart">
            <summary>
            启动打标
            </summary>
            <param name="data">打标标识</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetFlipScreenResult(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StCCDResult}@,System.String@)">
            <summary>
            获取翻屏结果(CCD)
            </summary>
            <param name="dicResult">key：表位号、value：翻屏结果</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.InteractiveRadioAutoSetAddr(System.Int32[])">
            <summary>
            交互广播自动设置地址
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.UpgradeEnabled(System.Int32[])">
            <summary>
            升级使能
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SoftwareReset(System.Int32[])">
            <summary>
            软件重启
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.WriteUpgradeData(System.Int32[],System.Byte[],System.Int32)">
            <summary>
            写升级数据
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="writeData">写入数据</param>
            <param name="dataIndex">写入序号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPortParams(System.Int32[],System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            设置串口参数
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="dicPortParam">串口参数字典[key-串口编号 value-波特率,例：2400,e,8,1]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPort})">
            <summary>
            设置同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPorts">端口数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetNoSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin})">
            <summary>
            设置不同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPinPulseCapture(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Int32)">
            <summary>
            设置引脚脉冲捕获
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="actionType">捕获动作0.停止捕获 1.开始捕获</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadNoSamePortPinState(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StPinConfigurationParam}}@)">
            <summary>
            查询不同端口引脚状态
            </summary>
            <param name="MeterIds">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="DicClServicePanelPortPin">不同端口引脚状态</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadPinPulseCapture(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Int32}}@)">
            <summary>
            查询引脚脉冲捕获
            </summary>
            <param name="MeterIds">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="DicClServicePulseCaptures">端口引脚脉冲</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SendTakingDownDeviceData(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Byte[]},System.Int32,System.Collections.Generic.Dictionary{System.Int32,System.Byte[]}@)">
            <summary>
            下发从站设备数据
            </summary>
            <param name="meterId"></param>
            <param name="data"></param>
            <param name="takingDownDeviceId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetCurrentVersions(System.Int32)">
            <summary>
            查询当前软件本号
            </summary>
            <param name="meterId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadHumAndTemp(System.Single@,System.Single@,System.Int32@)">
            <summary>
            读取（环境）温湿度
            </summary>
            <param name="Temp">温度</param>
            <param name="Hum">湿度</param>
            <param name="controlId">控制器ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterInspectType(System.String,System.Byte,System.Int32)">
            <summary>
            设置核查表模式
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="dataCode">功能码</param>
            <param name="setType">设置模式类型 0 设置进入核查模式， 1启动物理核查</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetConstantPattern(System.String,System.Int32)">
            <summary>
            设置台体脉冲常数模式
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="Constant">被检脉冲常数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetImpulseCount(System.String,System.String,System.Int32)">
            <summary>
            设置台体脉冲数
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="impulseNum">脉冲编号</param>
            <param name="impulseCount">脉冲数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.ReadMeterErrorValue(System.String,System.String[]@)">
            <summary>
            读取误差
            </summary> 
            <param name="commAddr">表接收地址</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMeterLineMode(System.String,System.Int32,System.Int32)">
            <summary>
            设置表接线模式
            </summary>
            <param name="commAddr">表接收地址</param>
            <param name="lineMode">接线模式</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.GetCurrentPluseCount(System.Int32@,System.Int32@)">
            <summary>
            获取当前脉冲计数
            </summary>
            <param name="state">状态[0-计数未完成 1到达计数]</param>
            <param name="currentPluse">当前脉冲计数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetMagneticHoldingRelayPins(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            设置磁保持继电器引脚
            </summary>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.MagneticHoldingRelayControl(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            磁保持继电器控制
            </summary>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.EquipMentBase.SetPulseCount(System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            设置脉冲计数
            </summary>
            <param name="pulseCount">脉冲数</param>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.DteRevBuffData">
            <summary>
            发送数据委托
            </summary>
            <param name="RevBuff">缓存数据</param>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.SendBuffDataEvent">
            <summary>
            发送接收数据事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.AddBuffData(System.Byte[])">
            <summary>
            发送数据方法
            </summary>
            <param name="BuffData">缓存数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.SendErrMsg(System.Exception)">
            <summary>
            发送错误日志
            </summary>
            LogMessage第二个参数为通讯报文，系统消息，错误消息
            <param name="strErrMsg"></param>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.PortType">
            <summary>
            端口类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.LogReadWrite">
            <summary>
            日志对象
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.PortParams">
            <summary>
            端口通讯参数9600,n,8,1
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.EquiqMentClassName">
            <summary>
            端口所属设备类名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.Ipaddress">
            <summary>
            远端Ip地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.LocalPort">
            <summary>
            本地网络端口号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.RemotePort">
            <summary>
            远端网络端口号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.SendData(System.Byte[])">
            <summary>
            发送数据方法
            </summary>
            <param name="SendBuff"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.Open">
            <summary>
            打开端口方法
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.BaseClass.PortBase.Close">
            <summary>
            关闭端口方法
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Core.ISimulation">
            <summary>
            模拟器接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.Initialize(System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.Core.BaseClass.PortBase})">
            <summary>
            初始化模拟器
            </summary>
            <param name="ports">端口枚举数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.InitializePorts">
            <summary>
            初始化端口数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.SetProtocolType(CLDC.CLAT.CLWBS.DataModel.Data._698.ProtocolType)">
            <summary>
            设置协议类型
            </summary>
            <param name="protocolType"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.ChangeMeterTime(System.DateTime)">
            <summary>
            设置模拟表的时间
            </summary>
            <param name="dateTime"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.GetMeterTime">
            <summary>
            获取模拟表时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.ChangeDataItemValue(System.String,System.Int32,System.Object)">
            <summary>
            改变数据项值
            </summary>
            <param name="id">数据项标识</param>
            <param name="index">数据项索引</param>
            <param name="value">值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.ChangeDataItemValue(System.String,System.Collections.Generic.IList{System.Object})">
            <summary>
            改变数据项值
            </summary>
            <param name="id">数据项标识</param>
            <param name="valueList">数据项值列表</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.ChangeMuxDataItemValues(System.String,System.Collections.Generic.List{System.Collections.Generic.List{System.Object}})">
            <summary>
            改变复杂类型的数据项的值
            </summary>
            <param name="id">数据项标识</param>		
            <param name="valueLst">复杂类型的值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.SetEnableAllMeter(System.Boolean)">
            <summary>
            设置是否启用模拟表
            </summary>
            <param name="isEnableMeter">true:启用 false:停用</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Core.ISimulation.GetSimulationDataById(System.String,System.String@)">
            <summary>
            根据数据标识获取模拟表内的数据
            </summary>
            <param name="id"></param>
            <param name="idName"></param>
            <returns></returns>
        </member>
    </members>
</doc>
