<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.BaseClass</name>
    </assembly>
    <members>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.TrialDataUploadEvent">
            <summary>
            实验数据上传事件
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CommSendMsg">
            <summary>
            发送数据委托
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendBuffDataEvent">
            <summary>
            发送接收数据事件
            </summary>
        </member>
        <member name="E:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SaveAndUploadData">
            <summary>
            保存上传数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SaveAndUploadDataFun">
            <summary>
            保存上传数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendMsgType(CLDC.CLAT.CLWBS.DataModel.Enum.EmCommType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.SculptureInfo})">
            <summary>
            发送数据方法
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.LogReadWrite">
            <summary>
            Log
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase._systemXmlInfo">
            <summary>
            系统配制文件
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.WorkThreadIsStart">
            <summary>
            工作线程是否开启
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.WorkThread">
            <summary>
            工作线程
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CameraId">
            <summary>
            相机位置ID
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.IsPause">
            <summary>
            暂停试验
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CurrentWorkThread">
            <summary>
            当前工作线程
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CurrentWorkItem">
            <summary>
            当前工作试验项目
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CurrentWorkItemId">
            <summary>
            当前缓存检定项目，
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.TepMeterInfo">
            <summary>
            临时缓存表信息
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.LstMeter_Info">
            <summary>
            电能表基本信息集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.LstSchema_Info">
            <summary>
            方案基本信息集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.ParameterCheck_Info">
            <summary>
            参数验证项目基本信息集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.LstMachineInfo">
            <summary>
            专机信息
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.LstProtocol_Info">
            <summary>
            协议
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.InitComm">
            <summary>
            初始化对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.InitOPCComm">
            <summary>
            初始化PLC连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CloseComm">
            <summary>
            关闭对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.GetMachineInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo}@,System.String@)">
            <summary>
            获取专机信息
            </summary>
            <param name="lstMachineInfo">专机信息集合</param>
            <param name="strErrInfo">错误信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendUpLoadEquipEleLabel(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.UploadEquipCarveReqArg})">
            <summary>
            雕刻认证数据上传
            </summary>
            <param name="uploadEquipCarveReqArgs">上传数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.Send_AlarmInfo(System.String,System.String,System.Int32,System.String)">
             <summary>
            外发警告信息
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.DealToShutDown">
            <summary>
            关闭窗体更新图标
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.GetMeterInfo(System.String,System.Int32)">
            <summary>
            手工获取表信息
            </summary>
            <param name="barCode">表条码</param>
            <param name="meterId">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.GetSchemeInfo(System.String,CLDC.CLAT.CLWBS.DataModel.Enum.EmLocalStationType)">
            <summary>
            手工获取方案信息
            </summary>
            <param name="taskId">任务编号</param>
            <param name="stationType">工位类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.AddMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            添加表信息
            </summary>
            <param name="LstMeter">表信息问包</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CheckMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.String@)">
            <summary>
            检验主控发过来的表信息中必须要有的数据
            </summary>
            <param name="LstMeter"></param>
            <param name="strErrMsg"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.CalculateMeterId(System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}})">
            <summary>
            计算表位ID排序
            </summary>
            <param name="dicMeterInfo">表信息:托盘号trayNo-List表信息ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.AddMeter(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            潍坊添加表信息
            </summary>
            <param name="LstMeter"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendLogMsg(System.String)">
            <summary>
            写Debug日志
            </summary>
            <param name="msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendLogCodeMsg(System.String)">
            <summary>
            写码值同步日志
            </summary>
            <param name="msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendMsgToUI(System.String)">
            <summary>
            发送到界面
            </summary>
            <param name="msg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendLogErrRunMsg(System.Int32,System.String,System.String,System.String)">
            <summary>
            写运行信息，错误，警告日志
            </summary>
            <param name="msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.SendExMsg(System.Exception)">
            <summary>
            写异常日志
            </summary>
            <param name="ex">异常日志</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.CommunicationBase.StartThreadRead(System.Threading.ThreadStart)">
            <summary>
            开始线程
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.Serialize(System.Object)">
            <summary>
            对象序列化为json字符串
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.SerializeXNode(System.Xml.Linq.XDocument)">
            <summary>
            将XDocument序列化成json字符串
            </summary>
            <param name="xml"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.Serialize(System.Object,System.Boolean,System.Boolean)">
            <summary>
            Json序列化
            </summary>
            <param name="obj"></param>
            <param name="flag"></param>
            <param name="QuoteName">获取或设置一个值，该值指示对象名称是否包引号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.Serialize(System.Object,Newtonsoft.Json.Serialization.IContractResolver,System.Boolean)">
            <summary>
            Json序列化
            </summary>
            <param name="obj"></param>
            <param name="resolver"></param>
            <param name="quoteName">获取或设置一个值，该值指示对象名称是否包引号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.Deserialize``1(System.String)">
            <summary>
            Json反序列化
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.DeserializeXNode(System.String)">
            <summary>
            将json反序列化成XDocument对象
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.JsonConverter.Compress(System.String)">
            <summary>
            压缩
            </summary>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CertificateYears">
            <summary>
            合格证年限
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TestPerson">
            <summary>
            检定人员
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckPerson">
            <summary>
            审验人员
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckHumidity">
            <summary>
            湿度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckTemperature">
            <summary>
            检定温度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TestLineNumber">
            <summary>
            线体编号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsStdMeterUseSCPI">
            <summary>
            标准表是否使用SCPI协议
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsPowerUseSCPI">
            <summary>
            功率源是否使用SCPI协议
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsCompatibleTest">
            <summary>
            是否为兼容检定台子（国网+南网）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.EncryptType">
            <summary>
            加密机类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsOpticalPulse">
            <summary>
            (误差)是否采用关脉冲方式 false-电脉冲
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsUseBluetooth">
            <summary>
            (电表)是否蓝牙通讯方式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsUseBluetoothGW">
            <summary>
            (电表)是否蓝牙通讯方式(国网)
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ReactivePower">
            <summary>
            第几次做无功实验
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsMultifunctionMeterOnEquipment">
            <summary>
            南网多功能表是否用加密机检测
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.MainBluetoothPower">
            <summary>
            主蓝牙模块功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.MeterBluetoothPower">
            <summary>
            电表模块功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSetBluetoothPower">
            <summary>
            是否设置蓝牙发射功率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.JjfType">
            <summary>
            检定规范类型0表示2012   1表示2020
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ReadTempInterval">
            <summary>
            是否采用关脉冲方式 false-电脉冲
            采集温度间隔
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ReadTempRature">
            <summary>
            是否读接线柱温度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.AlarmTempValue">
            <summary>
            温度报警值
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.PowerOffTempValue">
            <summary>
            接线柱温度超过上限，立即切断电源阈值
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ImageAngleCamera1">
            <summary>
            相机1拍照角度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ImageAngleCamera2">
            <summary>
            相机2拍照角度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ImageAngleCamera3">
            <summary>
            相机3拍照角度
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CameraBrand">
            <summary>
            相机品牌
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckNameplate">
             <summary>
            是否检测铭牌
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckBarCode">
             <summary>
            是否检测条码
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.SaveImageOrNo">
             <summary>
            是否保存图像
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TitleType">
            <summary>
            工位类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TitleName">
            <summary>
            工位名称
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LineType">
            <summary>
            单相或者三相线体
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ArrowDirection">
            <summary>
            表前进方向，箭头方向
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TrayTotalNum">
            <summary>
            满表托盘数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.StationID">
            <summary>
            工位ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LineNO">
            <summary>
            线体编号（几号线）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.SysNo">
            <summary>
            系统编号（江西项目）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.MachineNo">
            <summary>
            设备编号（江西项目）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.DetectUnitNo">
            <summary>
            单元编号（江西项目）
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CellMeterNum">
            <summary>
            单元可以挂表的数量
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ServerIpAddress">
            <summary>
            主控IP地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ServerPort">
            <summary>
            主控端口号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ServerTimeOut">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.OPCServerAddress">
            <summary>
            OPC服务地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.OPCServerAddressHead">
            <summary>
            OPC服务地址头内容
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.OPCNodeConfigPath">
            <summary>
            OPC配置地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSetBaudRate">
            <summary>
            是否检定中设置串口波特率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IpAddress">
            <summary>
            加密机IP地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.Port">
            <summary>
            加密机端口号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.Pwd">
            <summary>
            加密机密码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CarrierType">
            <summary>
            东软载波通讯方式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.DrCarrierInCL2041">
            <summary>
            东软载波在CL2041通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.DxCarrierInCL2041">
            <summary>
            鼎新载波在CL2041通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.XcCarrierInCL2041">
            <summary>
            晓程载波在CL2041通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.RscCarrierInCL2041">
            <summary>
            瑞斯康载波在CL2041通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.XrCarrierInCL2041">
            <summary>
            欣荣载波在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ZcCarrierInCL2041">
            <summary>
            中宸载波在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ZrhtCarrierInCL2041">
            <summary>
            中瑞昊天在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LhwCarrierInCL2041">
            <summary>
            力合微在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.KdCarrierInCL2041">
            <summary>
            中瑞昊天在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.WglInCL2041">
            <summary>
            中瑞昊天在2041上的通道
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.BuHeGeMax">
            <summary>
            不合格数量超过多少停止检定
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsCheckRs485">
            <summary>
            是否检查485接驳一个不合格报红灯停止实验
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsNewOrOld">
            <summary>
            是新台体还是旧台体
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ZhijieControlType">
            <summary>
            直接式配置选择
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.HuGanControlType">
            <summary>
            互感式配置选择
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ZhongDuanType">
            <summary>
            终端类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LinCheck">
            <summary>
            是否为临检台
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsConnectCentre">
            <summary>
            是否连接主控
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsCheckStdMeter">
            <summary>
            是否主表检副表
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.AppPulseCount">
            <summary>
             副标准表检主标准表时，被检表的脉冲圈数
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSrsStdMtr">
            <summary>
            是否高精度标准表
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.StdMeterErrLimit">
            <summary>
            检主标准表误差限值
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ErrorCount">
            <summary>
            误差小数点位数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.MeterProcotol">
             <summary>
            电表协议
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ErrorFloatNum">
            <summary>
            误差原始值个数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ErrTime">
            <summary>
            工控机和主控时间限差
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckTimeAgin">
            <summary>
            发广播指令校时前电能表和工控时间限差
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CheckTime">
            <summary>
            加密校时后电能表和工控时间限差
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LogReadWrite">
             <summary>
            log
             </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TcpLocalPorts">
            <summary>
            终端TCP通信本地起始端口
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TerminalIp">
            <summary>
            终端ip起始地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.FinshDataNum">
            <summary>
            默认放行数量
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.FinshIsCheckRs485">
            <summary>
            默认是否检查485接驳一个不合格报红灯停止实验
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSendBuHeGepicture">
            <summary>
            是否上传不合格图片
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ChargingPileIP">
            <summary>
            充电桩IP
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ChargingPileProt">
            <summary>
            充电桩端口号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.PulseType">
            <summary>
            当前蓝牙脉冲模式
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsBluetooth">
            <summary>
            当前是否已连接蓝牙
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.BluetoothParams">
            <summary>
            国网蓝牙连接参数  是否打开国网蓝牙1打开0关闭 | 是否打开蓝牙预检定1打开0关闭 | 发射档位 0代表4dBm、1代表0dBm、2代表-4dBm、3代表-8dBm、4代表最小档（-20 dBm）| 频段选择0- 全频段；1 - 带内频段；2 - 带外频段 | 通道生成方式 0-上位机输入 1-自动生成（盒子暂不支持自动）| 通道数 1-5 | 通道频点起始值 |通讯模式01
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ManageStationIDParams">
            <summary>
            温湿度仪参数配置信息，管辖工位ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.CollectionFrequenceParams">
            <summary>
            温湿度仪采集频率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempWarningDownValueParams">
            <summary>
            温度告警下限
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempWarningUpValueParams">
            <summary>
            温度告警上限
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.HumWarningDownValueParams">
            <summary>
            湿度告警下限
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.HumWarningUpValueParams">
            <summary>
            湿度告警上限
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempTheoryValueParams">
            <summary>
            温度理论值
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.HumTheoryValueParams">
            <summary>
            湿度理论值
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempHumEquipTypeParams">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempHumEquipSerialNumberParams">
            <summary>
            设备序列号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.TempHumControllerIDParams">
            <summary>
            控制器ID
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ExternalSysCommAddsParams">
            <summary>
            第三方服务地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ImagesCommAddsParams">
            <summary>
            外观专机 图像处理软件IP地址
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ExternalSysToken">
            <summary>
            第三方服务授权码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ExternalSysSecretKey">
            <summary>
            第三方服务SecretKey
            密钥通过RSA公钥加密后的字符
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LisSculptureInfos">
            <summary>
            MES获取的雕刻数据
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.LisreplyDATAs">
            <summary>
            芯片ID数据验证
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsLisreply">
            <summary>
            
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.StrSculptureInfoPLCData">
            <summary>
            PLC获取的雕刻数据
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.OPCMeterInfo">
            <summary>
            获取的表位数据
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.PLCDataPositionNo">
            <summary>
            检定单元需要上传表位状态数据的表位号
            工位软件从PLC读取
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSetServicePanlBaudRate">
            <summary>
            是否设置服务板下挂表位波特率
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsInProgress">
            <summary>
            当前软件是否正在检定
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ConnectDeviceTime">
            <summary>
            顺德连接加密机时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSubscriptionModel">
            <summary>
            江西OPC是否使用订阅模式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsContinueCheckNoPassMeter">
            <summary>
            不合格表是否继续检定
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.VirturalMeterCommType">
            <summary>
            模拟表通讯方式，默认2018串口服务器，
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.Instance(System.String)">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.ReadSystemConfig(System.String)">
            <summary>
            读取系统配置
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.GetStationType">
            <summary>
            获取工位类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsTerminalByMeterType(CLDC.Framework.DataModel.Enum.EmMeterType)">
            <summary>
            判断是否是为终端
            </summary>
            <param name="meterType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.BaseClass.SystemXmlInfo.IsSouthNetSingleMeter(CLDC.Framework.DataModel.Enum.EmMeterType)">
            <summary>
            是否是南网单相表
            </summary>
            <param name="meterType">表类型</param>
            <returns></returns>
        </member>
        <member name="T:TLE_WinFrom.SNTPTimeClient">
             <summary>
             SNTPTimeClient 的摘要说明。
             
             Public class members:
            
             LeapIndicator - Warns of an impending leap second to be inserted/deleted in the last
             minute of the current day. (See the _LeapIndicator enum)
             
             VersionNumber - Version number of the protocol (3 or 4).
             
             Mode - Returns mode. (See the _Mode enum)
             
             Stratum - Stratum of the clock. (See the _Stratum enum)
             
             PollInterval - Maximum interval between successive messages.
             
             Precision - Precision of the clock.
             
             RootDelay - Round trip time to the primary reference source.
             
             RootDispersion - Nominal error relative to the primary reference source.
             
             ReferenceID - Reference identifier (either a 4 character string or an IP address).
             
             ReferenceTimestamp - The time at which the clock was last set or corrected.
             
             OriginateTimestamp - The time at which the request departed the client for the server.
             
             ReceiveTimestamp - The time at which the request arrived at the server.
             
             Transmit Timestamp - The time at which the reply departed the server for client.
             
             RoundTripDelay - The time between the departure of request and arrival of reply.
             
             LocalClockOffset - The offset of the local clock relative to the primary reference
             source.
             
             Initialize - Sets up data structure and prepares for connection.
             
             Connect - Connects to the time server and populates the data structure.
             
             IsResponseValid - Returns true if received data is valid and if comes from
             a NTP-compliant time server.
             
             ToString - Returns a string representation of the object.
             
             -----------------------------------------------------------------------------
             Structure of the standard NTP header (as described in RFC 2030)
                                   1                   2                   3
               0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |LI | VN  |Mode |    Stratum    |     Poll      |   Precision   |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                          Root Delay                           |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                       Root Dispersion                         |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                     Reference Identifier                      |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                                                               |
              |                   Reference Timestamp (64)                    |
              |                                                               |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                                                               |
              |                   Originate Timestamp (64)                    |
              |                                                               |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                                                               |
              |                    Receive Timestamp (64)                     |
              |                                                               |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                                                               |
              |                    Transmit Timestamp (64)                    |
              |                                                               |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                 Key Identifier (optional) (32)                |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
              |                                                               |
              |                                                               |
              |                 Message Digest (optional) (128)               |
              |                                                               |
              |                                                               |
              +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
             
             -----------------------------------------------------------------------------
             
             NTP Timestamp Format (as described in RFC 2030)
                                     1                   2                   3
                 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
             +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
             |                           Seconds                             |
             +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
             |                  Seconds Fraction (0-padded)                  |
             +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
             
             </summary>
        </member>
    </members>
</doc>
