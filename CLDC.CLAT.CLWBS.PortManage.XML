<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.PortManage</name>
    </assembly>
    <members>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.GetParity(System.String)">
            <summary>
            字符串 ==> Parity 
            </summary>
            <param name="tag">校验位标记</param>
            <returns>Parity枚举值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.GetStopBit(System.String)">
            <summary>
            字符串 ==> StopBits 
            </summary>
            <param name="tag">停止位标记</param>
            <returns>StopBits枚举值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.GetStrParity(System.IO.Ports.Parity)">
            <summary>
            Parity ==> 字符串
            </summary>
            <param name="parity">校验位</param>
            <returns>Parity字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.GetStrStopBit(System.IO.Ports.StopBits)">
            <summary>
            StopBits ==> 字符串
            </summary>
            <param name="stopBits">停止位</param>
            <returns>StopBits字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.Open">
            <summary>
            打开串口
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.Close">
            <summary>
            关闭串口
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.SendData(System.Byte[])">
            <summary>
            串口发送数据
            </summary>
            <param name="SendBuff"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.SerialPortSend.ReceivetData(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
            <summary>
            串口接收数据的事件处理函数
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient">
            <summary>
            TCP客户端操作类
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.CLSocket">
            <summary>
            socket实例
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.RcvLen">
            <summary>
            收到的数据实际长度
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.RCVBUFLEN">
            <summary>
            接收缓冲区最大长度
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.m_softBuff">
            <summary>
            接收缓冲区
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.thdSoft">
            <summary>
            接收数据子线程
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.blConntOk">
            <summary>
            连接是否成功的标志
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.SendData(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="SendBuff"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.Open">
            <summary>
            打开连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.SoftListen">
            <summary>
            监听
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TCPClient.Close">
            <summary>
            关闭连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpClient_1.ReceiveData(System.IAsyncResult)">
            <summary>
            接受数据完成处理函数，异步的特性就体现在这个函数中
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpClient_1.SendDataEnd(System.IAsyncResult)">
            <summary>
            数据发送完成处理函数
            </summary>
            <param name="iar"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpClient_1.StartFun">
            <summary>
            断开后5分钟一直重连，超时不再连接。需手动连接或重启软件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.PortManage.PortType.TcpServer.MAX_CLIENT">
            <summary>
            默认的服  务器最大连接客户端端数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpServer.AcceptConn(System.IAsyncResult)">
            <summary>
            客户端连接处理函数
            </summary>
            <param name="iar">欲建立服务器连接的Socket对象</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpServer.ReceiveData(System.IAsyncResult)">
            <summary>
            接受数据完成处理函数，异步的特性就体现在这个函数中
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.TcpServer.SendDataEnd(System.IAsyncResult)">
            <summary>
            发送数据完成处理函数
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.PortManage.PortType.UdpSend_1.ReceiveData(System.IAsyncResult)">
            <summary>
            接受数据完成处理函数
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
    </members>
</doc>
