﻿<?xml version="1.0" encoding="utf-8" ?>
<items>
  <item  Id="1" Name="基本误差" Value="51" ClassName="BaseError"></item>
  <!--<item  Id="2" Name="标准偏差" Value="52" ClassName="BaseError"></item>-->
  <item  Id="3" Name="电能表常数" Value="53" ClassName="MeterConstComm"></item>
  <item  Id="4" Name="起动试验" Value="54" ClassName="MeterStart"></item>
  <item  Id="5" Name="潜动试验" Value="55" ClassName="MeterCreep"></item>
  <item  Id="6" Name="电能示值的组合误差" Value="56" ClassName="EnergyCombinErr"></item>
  <item  Id="8" Name="日计时误差" Value="58" ClassName="ClockError"></item>
  <item  Id="9" Name="剩余电量递减准确度" Value="60" ClassName="EnergyDegression"></item>
  <item  Id="10" Name="最大需量误差" Value="61" ClassName="EncryptMaxDemandError"></item>
  <!--<item  Id="11" Name="常温走字" Value="62" ClassName="EnergyMeasure"></item>-->
  <item  Id="14" Name="电能计量功能" Value="121" ClassName="EnergyMeasureMeter"></item>
  <!--<item  Id="15" Name="最大需量功能" Value="122" ClassName="EnergyMeasure"></item>-->
  <item  Id="16" Name="时段投切" Value="123" ClassName="EncryptPeriodSwitch"></item>
  <!--<item  Id="18" Name="脉冲输出功能" Value="125" ClassName="EnergyMeasure"></item>-->
  <item  Id="23" Name="需量清零" Value="134" ClassName="EncryptClearMaxDemand"></item>
  <item  Id="24" Name="广播校时" Value="135" ClassName="EncryptAdjustTime"></item>
  <!--<item  Id="25" Name="本地密钥更新" Value="578" ClassName="LocalKeyUpdate"></item>-->
  <!--<item  Id="27" Name="闰年自动转换" Value="136" ClassName="LeapYearTest"></item>-->
  <!--<item  Id="28" Name="本地表清零" Value="143" ClassName="LocalClear"></item>-->
  <!--<item  Id="29" Name="计时功能" Value="148" ClassName="TimingFunction"></item>-->
  <item  Id="31" Name="误差变差试验" Value="471" ClassName="ErrorChanging"></item>
  <item  Id="32" Name="误差一致性试验" Value="472" ClassName="ErrorConsistency"></item>
  <item  Id="33" Name="一次采集成功率试验" Value="521" ClassName="SuccessRateNew"></item>
  <!--<item  Id="34" Name="安全认证" Value="552" ClassName="StatusAtic"></item>-->
  <!--<item  Id="36" Name="远程数据回抄" Value="558" ClassName="ReadDat_Ver"></item>-->
  <!--<item  Id="37" Name="远程拉合闸" Value="559" ClassName="EnergyMeasure"></item>
  <item  Id="38" Name="远程报警" Value="569" ClassName="EnergyMeasure"></item>
  <item  Id="39" Name="远程保电" Value="570" ClassName="EnergyMeasure"></item>-->
  <!--<item  Id="40" Name="本地拉合闸" Value="576" ClassName="LocalPullSwitchs"></item>
  <item  Id="41" Name="预置金额" Value="854" ClassName="Preplace"></item>-->
  <!--<item  Id="42" Name="预热" Value="1" ClassName="Energization"></item>-->
  <item  Id="43" Name="参数验证" Value="852" ClassName="EncryptCheckParamted"></item>
  <item  Id="43" Name="参数设置" Value="557" ClassName="EncryptCheckParamted"></item>
  <item  Id="44" Name="负载电流升降变差" Value="473" ClassName="ErrorCurrUpDown"></item>
  <item  Id="47" Name="读标准表" Value="8888" ClassName="ReadStdMeter"></item>
  <item  Id="48" Name="485通讯测试" Value="3" ClassName="Rs485TestDanXiang"></item>
  <item  Id="52" Name="时钟偏差" Value="7" ClassName="ClockDeviation"></item>
 <item  Id="55" Name="温度测试" Value="9999" ClassName="ReadTemprature"></item>
 <item  Id="56" Name="远程清零" Value="130" ClassName="EncryptClearMeter"></item>
 <item  Id="57" Name="远程秘钥更新" Value="556" ClassName="EncryptKeyUpdate"></item>
 <item  Id="57" Name="密钥恢复" Value="580" ClassName="EncryptKeyUpdateRecove"></item>
  <item  Id="58" Name="远程身份认证" Value="1014001" ClassName="SouthChargeInterface"></item>
  <item  Id="59" Name="本地身份认证" Value="1015001" ClassName="SouthChargeInterface"></item>
  <item  Id="60" Name="远程密钥更新" Value="1007001" ClassName="SouthChargeInterface"></item>
  <item  Id="61" Name="远程密钥恢复" Value="1007002" ClassName="SouthChargeInterface"></item>
  <item  Id="62" Name="保电功能" Value="1009001" ClassName="SouthChargeInterface"></item>
  <item  Id="63" Name="直接跳闸" Value="1009002" ClassName="SouthChargeInterface"></item>
  <item  Id="64" Name="直接合闸" Value="1009003" ClassName="SouthChargeInterface"></item>
  <item  Id="65" Name="本地表清零" Value="1010001" ClassName="SouthChargeInterface"></item>
  <item  Id="66" Name="远程表清零" Value="1010002" ClassName="SouthChargeInterface"></item>
  <item  Id="67" Name="本地切换远程" Value="1011001" ClassName="LocalToRemote"></item>
  <item  Id="68" Name="远程切换本地" Value="1011002" ClassName="RemoteToLocal"></item>
  <item  Id="69" Name="阶梯电价结算" Value="1013001" ClassName="SouthChargeInterface"></item>
  <item  Id="70" Name="分时费率电价结算" Value="1013002" ClassName="SouthChargeInterface"></item>
  <item  Id="71" Name="透支功能" Value="1013004" ClassName="SouthChargeInterface"></item>
  <item  Id="72" Name="正式密钥下一类参数更新" Value="1006003" ClassName="SouthChargeInterface"></item>
  <item  Id="73" Name="合闸允许" Value="1009004" ClassName="SouthChargeInterface"></item>
  <item  Id="74" Name="远程报警及解除" Value="1009007" ClassName="SouthChargeInterface"></item>
  <item  Id="75" Name="控制命令有效时间合法性" Value="1009008" ClassName="SouthChargeInterface"></item>
  <item  Id="76" Name="参数备份" Value="1020001" ClassName="SouthChargeInterface"></item>
  <item  Id="77" Name="参数恢复" Value="1020002" ClassName="SouthChargeInterface"></item>
  <item  Id="78" Name="测试密钥下二类参数更新" Value="1006002" ClassName="SouthChargeInterface"></item>
  <item  Id="79" Name="正式密钥下二类参数更新" Value="1006004" ClassName="SouthChargeInterface"></item>
  <item  Id="80" Name="校时" Value="5" ClassName="EncryptAdjustTime"></item>
  <item  Id="81" Name="时钟示值误差" Value="66" ClassName="ClockDeviation"></item>
  <item  Id="87" Name="软件比对功能" Value="111015" ClassName="SoftWareCompare"></item>
 <item  Id="87" Name="过电压试验" Value="1009011" ClassName="OverVoltage"></item>
 <item  Id="88" Name="合元和分元试验" Value="67" ClassName="ErrorDValueNew"></item>
 <item  Id="89" Name="源控制" Value="5555" ClassName="ExternalPowerControl"></item>
</items>
