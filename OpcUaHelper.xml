<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpcUaHelper</name>
    </assembly>
    <members>
        <member name="T:OpcUaHelper.ClientUtils">
            <summary>
            Defines numerous re-useable utility functions.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.HandleException(System.String,System.Exception)">
            <summary>
            Handles an exception.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.GetAppIcon">
            <summary>
            Returns the application icon.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.GetAccessLevelDisplayText(System.Byte)">
            <summary>
            Gets the display text for the access level attribute.
            </summary>
            <param name="accessLevel">The access level.</param>
            <returns>The access level formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.GetEventNotifierDisplayText(System.Byte)">
            <summary>
            Gets the display text for the event notifier attribute.
            </summary>
            <param name="eventNotifier">The event notifier.</param>
            <returns>The event notifier formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.GetValueRankDisplayText(System.Int32)">
            <summary>
            Gets the display text for the value rank attribute.
            </summary>
            <param name="valueRank">The value rank.</param>
            <returns>The value rank formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.GetAttributeDisplayText(Opc.Ua.Client.Session,System.UInt32,Opc.Ua.Variant)">
            <summary>
            Gets the display text for the specified attribute.
            </summary>
            <param name="session">The currently active session.</param>
            <param name="attributeId">The id of the attribute.</param>
            <param name="value">The value of the attribute.</param>
            <returns>The attribute formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.BrowseDescriptionCollection,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
            <param name="session">The session.</param>
            <param name="nodesToBrowse">The set of browse operations to perform.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.ViewDescription,Opc.Ua.BrowseDescriptionCollection,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.BrowseDescription,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
            <param name="session">The session.</param>
            <param name="nodeToBrowse">The NodeId for the starting node.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.ViewDescription,Opc.Ua.BrowseDescription,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.BrowseSuperTypes(Opc.Ua.Client.Session,Opc.Ua.NodeId,System.Boolean)">
            <summary>
            Browses the address space and returns all of the supertypes of the specified type node.
            </summary>
            <param name="session">The session.</param>
            <param name="typeId">The NodeId for a type node in the address space.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.TranslateBrowsePaths(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.NamespaceTable,System.String[])">
            <summary>
            Returns the node ids for a set of relative paths.
            </summary>
            <param name="session">An open session with the server to use.</param>
            <param name="startNodeId">The starting node for the relative paths.</param>
            <param name="namespacesUris">The namespace URIs referenced by the relative paths.</param>
            <param name="relativePaths">The relative paths.</param>
            <returns>A collection of local nodes.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.FindEventType(Opc.Ua.Client.MonitoredItem,Opc.Ua.EventFieldList)">
            <summary>
            Finds the type of the event for the notification.
            </summary>
            <param name="monitoredItem">The monitored item.</param>
            <param name="notification">The notification.</param>
            <returns>The NodeId of the EventType.</returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.ConstructEvent(Opc.Ua.Client.Session,Opc.Ua.Client.MonitoredItem,Opc.Ua.EventFieldList,System.Collections.Generic.Dictionary{Opc.Ua.NodeId,System.Type},System.Collections.Generic.Dictionary{Opc.Ua.NodeId,Opc.Ua.NodeId})">
            <summary>
            Constructs an event object from a notification.
            </summary>
            <param name="session">The session.</param>
            <param name="monitoredItem">The monitored item that produced the notification.</param>
            <param name="notification">The notification.</param>
            <param name="knownEventTypes">The known event types.</param>
            <param name="eventTypeMappings">Mapping between event types and known event types.</param>
            <returns>
            The event object. Null if the notification is not a valid event type.
            </returns>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectInstanceDeclarationsForType(Opc.Ua.Client.Session,Opc.Ua.NodeId)">
            <summary>
            Collects the instance declarations for a type.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectInstanceDeclarationsForType(Opc.Ua.Client.Session,Opc.Ua.NodeId,System.Boolean)">
            <summary>
            Collects the instance declarations for a type.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectInstanceDeclarations(Opc.Ua.Client.Session,Opc.Ua.NodeId,OpcUaHelper.InstanceDeclaration,System.Collections.Generic.List{OpcUaHelper.InstanceDeclaration},System.Collections.Generic.IDictionary{System.String,OpcUaHelper.InstanceDeclaration})">
            <summary>
            Collects the fields for the instance node.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.FindTargetOfReference(Opc.Ua.Client.Session,System.Collections.Generic.List{Opc.Ua.NodeId},Opc.Ua.NodeId,System.Boolean)">
            <summary>
            Finds the targets for the specified reference.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.UpdateInstanceDescriptions(Opc.Ua.Client.Session,System.Collections.Generic.List{OpcUaHelper.InstanceDeclaration},System.Boolean)">
            <summary>
            Finds the targets for the specified reference.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectFieldsForType(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId})">
            <summary>
            Collects the fields for the type.
            </summary>
            <param name="session">The session.</param>
            <param name="typeId">The type id.</param>
            <param name="fields">The fields.</param>
            <param name="fieldNodeIds">The node id for the declaration of the field.</param>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectFieldsForInstance(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId})">
            <summary>
            Collects the fields for the instance.
            </summary>
            <param name="session">The session.</param>
            <param name="instanceId">The instance id.</param>
            <param name="fields">The fields.</param>
            <param name="fieldNodeIds">The node id for the declaration of the field.</param>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.CollectFields(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.QualifiedNameCollection,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId},System.Collections.Generic.Dictionary{Opc.Ua.NodeId,Opc.Ua.QualifiedNameCollection})">
            <summary>
            Collects the fields for the instance node.
            </summary>
            <param name="session">The session.</param>
            <param name="nodeId">The node id.</param>
            <param name="parentPath">The parent path.</param>
            <param name="fields">The event fields.</param>
            <param name="fieldNodeIds">The node id for the declaration of the field.</param>
            <param name="foundNodes">The table of found nodes.</param>
        </member>
        <member name="M:OpcUaHelper.ClientUtils.ContainsPath(Opc.Ua.SimpleAttributeOperandCollection,Opc.Ua.QualifiedNameCollection)">
            <summary>
            Determines whether the specified select clause contains the browse path.
            </summary>
            <param name="selectClause">The select clause.</param>
            <param name="browsePath">The browse path.</param>
            <returns>
            	<c>true</c> if the specified select clause contains path; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:OpcUaHelper.Forms.DiscoverServerDlg">
            <summary>
            Prompts the user to specify a host name and discovers the servers.
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.#ctor">
            <summary>
            Creates an empty form.
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.ShowDialog(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Shows the dialog.
            </summary>
            <param name="configuration">The client applicatio configuration.</param>
            <returns>The selected endpoint url</returns>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.ShowDialog(Opc.Ua.ApplicationConfiguration,System.String)">
            <summary>
            Shows the dialog.
            </summary>
            <param name="configuration">The client applicatio configuration.</param>
            <param name="hostName">The default host name.</param>
            <returns>The selected endpoint url</returns>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.GetEndpoints(System.String)">
            <summary>
            Gets the endpoints for the host.
            </summary>
            <param name="hostName">Name of the host.</param>
        </member>
        <member name="F:OpcUaHelper.Forms.DiscoverServerDlg.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:OpcUaHelper.Forms.DiscoverServerDlg.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:OpcUaHelper.Forms.FormBrowseServer">
            <summary>
            一个浏览OPC服务器节点的窗口类
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.#ctor">
            <summary>
            允许自己输入服务器地址的实例化
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.#ctor(System.String)">
            <summary>
            固定地址且不允许更改的实例化
            </summary>
        </member>
        <member name="F:OpcUaHelper.Forms.FormBrowseServer.m_OpcUaClient">
            <summary>
            Opc客户端的核心类
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.OpcUaClientInitialization">
            <summary>
            初始化
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.M_OpcUaClient_ConnectComplete(System.Object,System.EventArgs)">
            <summary>
            连接服务器结束后马上浏览根节点
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.M_OpcUaClient_OpcStatusChange1(System.Object,OpcUaHelper.OpcUaStatusEventArgs)">
            <summary>
            OPC 客户端的状态变化后的消息提醒
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.ReadOneNodeFiveAttributes(System.Collections.Generic.List{Opc.Ua.NodeId})">
            <summary>
            0:NodeClass  1:Value  2:AccessLevel  3:DisplayName  4:Description
            </summary>
            <param name="nodeIds"></param>
            <returns></returns>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.ReadNoteDataValueAttributes(Opc.Ua.NodeId,System.UInt32)">
            <summary>
            读取一个节点的指定属性
            </summary>
            <param name="nodeId"></param>
            <param name="attribute"></param>
            <returns></returns>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.PopulateBranch(Opc.Ua.NodeId,System.Windows.Forms.TreeNodeCollection)">
            <summary>
            Populates the branch in the tree view.
            </summary>
            <param name="sourceId">The NodeId of the Node to browse.</param>
            <param name="nodes">The node collect to populate.</param>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.label2_Click(System.Object,System.EventArgs)">
            <summary>
            点击了节点名称前的内容进行复制
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:OpcUaHelper.Forms.FormBrowseServer.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:OpcUaHelper.Forms.FormBrowseServer.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:OpcUaHelper.Forms.FormConnectSelect.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.FormConnectSelect.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:OpcUaHelper.Forms.FormConnectSelect.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:OpcUaHelper.Forms.TreeViewEx">
            <summary>
            无闪烁的树节点控件，结果还是没有实现
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.TreeViewEx.#ctor">
            <summary>
            实例化一个新的控件
            </summary>
        </member>
        <member name="M:OpcUaHelper.Forms.TreeViewEx.OnNotifyMessage(System.Windows.Forms.Message)">
            <summary>
            通知
            </summary>
            <param name="m"></param>
        </member>
        <member name="T:OpcUaHelper.ExceptionDlg">
            <summary>
            A dialog that displays an exception trace in an HTML page.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:OpcUaHelper.ExceptionDlg"/> class.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.ReplaceSpecialCharacters(System.String)">
            <summary>
            Replaces all special characters in the message.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.Show(System.String,System.Exception)">
            <summary>
            Displays the exception in a dialog.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.ShowDialog(System.String,System.Exception)">
            <summary>
            Display the exception in the dialog.
            </summary>
        </member>
        <member name="F:OpcUaHelper.ExceptionDlg.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:OpcUaHelper.ExceptionDlg.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:OpcUaHelper.TypeDeclaration">
            <summary>
            Stores a type declaration retrieved from a server.
            </summary>
        </member>
        <member name="F:OpcUaHelper.TypeDeclaration.NodeId">
            <summary>
            The node if for the type.
            </summary>
        </member>
        <member name="F:OpcUaHelper.TypeDeclaration.Declarations">
            <summary>
            The fully inhierited list of instance declarations for the type.
            </summary>
        </member>
        <member name="T:OpcUaHelper.InstanceDeclaration">
            <summary>
            Stores an instance declaration fetched from the server.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.RootTypeId">
            <summary>
            The type that the declaration belongs to.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.BrowsePath">
            <summary>
            The browse path to the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.BrowsePathDisplayText">
            <summary>
            The browse path to the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.DisplayPath">
            <summary>
            A localized path to the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.NodeId">
            <summary>
            The node id for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.NodeClass">
            <summary>
            The node class of the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.BrowseName">
            <summary>
            The browse name for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.DisplayName">
            <summary>
            The display name for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.Description">
            <summary>
            The description for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.ModellingRule">
            <summary>
            The modelling rule for the instance declaration (i.e. Mandatory or Optional).
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.DataType">
            <summary>
            The data type for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.ValueRank">
            <summary>
            The value rank for the instance declaration.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.BuiltInType">
            <summary>
            The built-in type parent for the data type.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.DataTypeDisplayText">
            <summary>
            A localized name for the data type.
            </summary>
        </member>
        <member name="F:OpcUaHelper.InstanceDeclaration.OverriddenDeclaration">
            <summary>
            An instance declaration that has been overridden by the current instance.
            </summary>
        </member>
        <member name="T:OpcUaHelper.FilterDeclarationField">
            <summary>
            A field in a filter declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclarationField.#ctor">
            <summary>
            Creates a new instance of a FilterDeclarationField.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclarationField.#ctor(OpcUaHelper.InstanceDeclaration)">
            <summary>
            Creates a new instance of a FilterDeclarationField.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclarationField.#ctor(OpcUaHelper.FilterDeclarationField)">
            <summary>
            Creates a new instance of a FilterDeclarationField.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.Selected">
            <summary>
            Whether the field is returned as part of the event notification.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.DisplayInList">
            <summary>
            Whether the field is displayed in the list view.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.FilterEnabled">
            <summary>
            Whether the filter is enabled.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.FilterOperator">
            <summary>
            The filter operator to use in the where clause.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.FilterValue">
            <summary>
            The filter value to use in the where clause.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclarationField.InstanceDeclaration">
            <summary>
            The instance declaration associated with the field.
            </summary>
        </member>
        <member name="T:OpcUaHelper.FilterDeclaration">
            <summary>
            A declararion of an event filter.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.#ctor">
            <summary>
            Creates a new instance of a FilterDeclaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.#ctor(OpcUaHelper.TypeDeclaration,OpcUaHelper.FilterDeclaration)">
            <summary>
            Creates a new instance of a FilterDeclaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.#ctor(OpcUaHelper.FilterDeclaration)">
            <summary>
            Creates a new instance of a FilterDeclaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.GetFilter">
            <summary>
            Returns the event filter defined by the filter declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.AddSimpleField(Opc.Ua.QualifiedName,Opc.Ua.BuiltInType,System.Boolean)">
            <summary>
            Adds a simple field to the declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.AddSimpleField(Opc.Ua.QualifiedName,Opc.Ua.BuiltInType,System.Int32,System.Boolean)">
            <summary>
            Adds a simple field to the declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.AddSimpleField(Opc.Ua.QualifiedName[],Opc.Ua.BuiltInType,System.Int32,System.Boolean)">
            <summary>
            Adds a simple field to the declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.AddSimpleField(Opc.Ua.QualifiedName[],Opc.Ua.NodeClass,Opc.Ua.BuiltInType,System.Int32,System.Boolean)">
            <summary>
            Adds a simple field to the declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.GetSelectClause">
            <summary>
            Returns the select clause defined by the filter declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.GetWhereClause">
            <summary>
            Returns the where clause defined by the filter declaration.
            </summary>
        </member>
        <member name="M:OpcUaHelper.FilterDeclaration.GetValue``1(Opc.Ua.QualifiedName,Opc.Ua.VariantCollection,``0)">
            <summary>
            Returns the value for the specified browse name.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclaration.EventTypeId">
            <summary>
            The type of event.
            </summary>
        </member>
        <member name="F:OpcUaHelper.FilterDeclaration.Fields">
            <summary>
            The list of declarations for the fields.
            </summary>
        </member>
        <member name="T:OpcUaHelper.OpcUaClient">
            <summary>
            一个二次封装了的OPC UA库，支持从opc ua服务器读写节点数据，批量读写，订阅，批量订阅，历史数据读取，方法调用操作。
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.#ctor">
            <summary>
            默认的构造函数，实例化一个新的OPC UA类
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ConnectServer(System.String)">
            <summary>
            connect to server
            </summary>
            <param name="serverUrl">remote url</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.Connect(System.String)">
            <summary>
            Creates a new session.
            </summary>
            <returns>The new session object.</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.Disconnect">
            <summary>
            Disconnects from the server.
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.UpdateStatus(System.Boolean,System.DateTime,System.String,System.Object[])">
            <summary>
            Report the client status
            </summary>
            <param name="error">Whether the status represents an error.</param>
            <param name="time">The time associated with the status.</param>
            <param name="status">The status message.</param>
            <param name="args">Arguments used to format the status message.</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.Session_KeepAlive(Opc.Ua.Client.Session,Opc.Ua.Client.KeepAliveEventArgs)">
            <summary>
            Handles a keep alive event from a session.
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.Server_ReconnectComplete(System.Object,System.EventArgs)">
            <summary>
            Handles a reconnect event complete from the reconnect handler.
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.SetLogPathName(System.String,System.Boolean)">
            <summary>
            设置OPC客户端的日志输出
            </summary>
            <param name="filePath">完整的文件路径</param>
            <param name="deleteExisting">是否删除原文件</param>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.OpcUaName">
            <summary>
            a name of application name show on server
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.UseSecurity">
            <summary>
            Whether to use security when connecting.
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.UserIdentity">
            <summary>
            The user identity to use when creating the session.
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.Session">
            <summary>
            The currently active session.
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.Connected">
            <summary>
            Indicate the connect status
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.ReconnectPeriod">
            <summary>
            The number of seconds between reconnect attempts (0 means reconnect is disabled).
            </summary>
        </member>
        <member name="E:OpcUaHelper.OpcUaClient.KeepAliveComplete">
            <summary>
            Raised when a good keep alive from the server arrives.
            </summary>
        </member>
        <member name="E:OpcUaHelper.OpcUaClient.ReconnectStarting">
            <summary>
            Raised when a reconnect operation starts.
            </summary>
        </member>
        <member name="E:OpcUaHelper.OpcUaClient.ReconnectComplete">
            <summary>
            Raised when a reconnect operation completes.
            </summary>
        </member>
        <member name="E:OpcUaHelper.OpcUaClient.ConnectComplete">
            <summary>
            Raised after successfully connecting to or disconnecing from a server.
            </summary>
        </member>
        <member name="E:OpcUaHelper.OpcUaClient.OpcStatusChange">
            <summary>
            Raised after the client status change
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaClient.AppConfig">
            <summary>
            配置信息
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNode(Opc.Ua.NodeId)">
            <summary>
            Read a value node from server
            </summary>
            <param name="nodeId">node id</param>
            <returns>DataValue</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNode``1(System.String)">
            <summary>
            Read a value node from server
            </summary>
            <typeparam name="T">type of value</typeparam>
            <param name="tag">node id</param>
            <returns>实际值</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNodeAsync``1(System.String)">
            <summary>
            Read a tag asynchronously
            </summary>
            <typeparam name="T">The type of tag to read</typeparam>
            <param name="tag">tag值</param>
            <returns>The value retrieved from the OPC</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNodes(Opc.Ua.NodeId[])">
            <summary>
            read several value nodes from server
            </summary>
            <param name="nodeIds">all NodeIds</param>
            <returns>all values</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNodesAsync(Opc.Ua.NodeId[])">
            <summary>
            read several value nodes from server
            </summary>
            <param name="nodeIds">all NodeIds</param>
            <returns>all values</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNodes``1(System.String[])">
            <summary>
            read several value nodes from server
            </summary>
            <param name="tags">所以的节点数组信息</param>
            <returns>all values</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNodesAsync``1(System.String[])">
            <summary>
            read several value nodes from server
            </summary>
            <param name="tags">all NodeIds</param>
            <returns>all values</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.WriteNode``1(System.String,``0)">
            <summary>
            write a note to server(you should use try catch)
            </summary>
            <typeparam name="T">The type of tag to write on</typeparam>
            <param name="tag">节点名称</param>
            <param name="value">值</param>
            <returns>if success True,otherwise False</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.WriteNodeAsync``1(System.String,``0)">
            <summary>
            Write a value on the specified opc tag asynchronously
            </summary>
            <typeparam name="T">The type of tag to write on</typeparam>
            <param name="tag">The fully-qualified identifier of the tag. You can specify a subfolder by using a comma delimited name. E.g: the tag `foo.bar` writes on the tag `bar` on the folder `foo`</param>
            <param name="value">The value for the item to write</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.WriteNodes(System.String[],System.Object[])">
            <summary>
            所有的节点都写入成功，返回<c>True</c>，否则返回<c>False</c>
            </summary>
            <param name="tags">节点名称数组</param>
            <param name="values">节点的值数据</param>
            <returns>所有的是否都写入成功</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.DeleteExsistNode(System.String)">
            <summary>
            删除一个节点的操作，除非服务器配置允许，否则引发异常，成功返回<c>True</c>，否则返回<c>False</c>
            </summary>
            <param name="tag">节点文本描述</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.AddNewNode(Opc.Ua.NodeId)">
            <summary>
            新增一个节点数据
            </summary>
            <param name="parent">父节点tag名称</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.AddSubscription(System.String,System.String,System.Action{System.String,Opc.Ua.Client.MonitoredItem,Opc.Ua.Client.MonitoredItemNotificationEventArgs})">
            <summary>
            新增一个订阅，需要指定订阅的关键字，订阅的tag名，以及回调方法
            </summary>
            <param name="key">关键字</param>
            <param name="tag">tag</param>
            <param name="callback">回调方法</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.AddSubscription(System.String,System.String[],System.Action{System.String,Opc.Ua.Client.MonitoredItem,Opc.Ua.Client.MonitoredItemNotificationEventArgs})">
            <summary>
            新增一批订阅，需要指定订阅的关键字，订阅的tag名数组，以及回调方法
            </summary>
            <param name="key">关键字</param>
            <param name="tags">节点名称数组</param>
            <param name="callback">回调方法</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.RemoveSubscription(System.String)">
            <summary>
            移除订阅消息，如果该订阅消息是批量的，也直接移除
            </summary>
            <param name="key">订阅关键值</param>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.RemoveAllSubscription">
            <summary>
            移除所有的订阅消息
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadHistoryRawDataValues(System.String,System.DateTime,System.DateTime,System.UInt32,System.Boolean)">
            <summary>
            read History data
            </summary>
            <param name="tag">节点的索引</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="count">读取的个数</param>
            <param name="containBound">是否包含边界</param>
            <returns>读取的数据列表</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadHistoryRawDataValues``1(System.String,System.DateTime,System.DateTime,System.UInt32,System.Boolean)">
            <summary>
            读取一连串的历史数据，并将其转化成指定的类型
            </summary>
            <param name="tag">节点的索引</param>
            <param name="start">开始时间</param>
            <param name="end">结束时间</param>
            <param name="count">读取的个数</param>
            <param name="containBound">是否包含边界</param>
            <returns>读取的数据列表</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.BrowseNodeReference(System.String)">
            <summary>
            浏览一个节点的引用
            </summary>
            <param name="tag">节点值</param>
            <returns>引用节点描述</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNoteAttributes(System.String)">
            <summary>
            读取一个节点的所有属性
            </summary>
            <param name="tag">节点信息</param>
            <returns>节点的特性值</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.ReadNoteDataValueAttributes(System.String)">
            <summary>
            读取一个节点的所有属性
            </summary>
            <param name="tag">节点值</param>
            <returns>所有的数据</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.CallMethodByNodeId(System.String,System.String,System.Object[])">
            <summary>
            call a server method
            </summary>
            <param name="tagParent">方法的父节点tag</param>
            <param name="tag">方法的节点tag</param>
            <param name="args">传递的参数</param>
            <returns>输出的结果值</returns>
        </member>
        <member name="M:OpcUaHelper.OpcUaClient.DoConnectComplete(System.Object)">
            <summary>
            Raises the connect complete event on the main GUI thread.
            </summary>
        </member>
        <member name="T:OpcUaHelper.OpcUaStatusEventArgs">
            <summary>
            OPC UA的状态更新消息
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaStatusEventArgs.Error">
            <summary>
            是否异常
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaStatusEventArgs.Time">
            <summary>
            时间
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcUaStatusEventArgs.Text">
            <summary>
            文本
            </summary>
        </member>
        <member name="M:OpcUaHelper.OpcUaStatusEventArgs.ToString">
            <summary>
            转化为字符串
            </summary>
            <returns></returns>
        </member>
        <member name="T:OpcUaHelper.OpcNodeAttribute">
            <summary>
            读取属性过程中用于描述的
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcNodeAttribute.Name">
            <summary>
            属性的名称
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcNodeAttribute.Type">
            <summary>
            属性的类型描述
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcNodeAttribute.StatusCode">
            <summary>
            操作结果状态描述
            </summary>
        </member>
        <member name="P:OpcUaHelper.OpcNodeAttribute.Value">
            <summary>
            属性的值，如果读取错误，返回文本描述
            </summary>
        </member>
        <member name="T:OpcUaHelper.FormUtils">
            <summary>
            辅助类
            </summary>
        </member>
        <member name="M:OpcUaHelper.FormUtils.GetAccessLevelDisplayText(System.Byte)">
            <summary>
            Gets the display text for the access level attribute.
            </summary>
            <param name="accessLevel">The access level.</param>
            <returns>The access level formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.GetEventNotifierDisplayText(System.Byte)">
            <summary>
            Gets the display text for the event notifier attribute.
            </summary>
            <param name="eventNotifier">The event notifier.</param>
            <returns>The event notifier formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.GetValueRankDisplayText(System.Int32)">
            <summary>
            Gets the display text for the value rank attribute.
            </summary>
            <param name="valueRank">The value rank.</param>
            <returns>The value rank formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.GetAttributeDisplayText(Opc.Ua.Client.Session,System.UInt32,Opc.Ua.Variant)">
            <summary>
            Gets the display text for the specified attribute.
            </summary>
            <param name="session">The currently active session.</param>
            <param name="attributeId">The id of the attribute.</param>
            <param name="value">The value of the attribute.</param>
            <returns>The attribute formatted as a string.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.DiscoverServers(Opc.Ua.ApplicationConfiguration)">
            <summary>
            Discovers the servers on the local machine.
            </summary>
            <param name="configuration">The configuration.</param>
            <returns>A list of server urls.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.SelectEndpoint(System.String,System.Boolean)">
            <summary>
            Finds the endpoint that best matches the current settings.
            </summary>
            <param name="discoveryUrl">The discovery URL.</param>
            <param name="useSecurity">if set to <c>true</c> select an endpoint that uses security.</param>
            <returns>The best available endpoint.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.BrowseDescriptionCollection,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
            <param name="session">The session.</param>
            <param name="nodesToBrowse">The set of browse operations to perform.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.FindEventType(Opc.Ua.Client.MonitoredItem,Opc.Ua.EventFieldList)">
            <summary>
            Finds the type of the event for the notification.
            </summary>
            <param name="monitoredItem">The monitored item.</param>
            <param name="notification">The notification.</param>
            <returns>The NodeId of the EventType.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.Browse(Opc.Ua.Client.Session,Opc.Ua.BrowseDescription,System.Boolean)">
            <summary>
            Browses the address space and returns the references found.
            </summary>
            <param name="session">The session.</param>
            <param name="nodeToBrowse">The NodeId for the starting node.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.BrowseSuperTypes(Opc.Ua.Client.Session,Opc.Ua.NodeId,System.Boolean)">
            <summary>
            Browses the address space and returns all of the supertypes of the specified type node.
            </summary>
            <param name="session">The session.</param>
            <param name="typeId">The NodeId for a type node in the address space.</param>
            <param name="throwOnError">if set to <c>true</c> a exception will be thrown on an error.</param>
            <returns>
            The references found. Null if an error occurred.
            </returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.ConstructEvent(Opc.Ua.Client.Session,Opc.Ua.Client.MonitoredItem,Opc.Ua.EventFieldList,System.Collections.Generic.Dictionary{Opc.Ua.NodeId,System.Type},System.Collections.Generic.Dictionary{Opc.Ua.NodeId,Opc.Ua.NodeId})">
            <summary>
            Constructs an event object from a notification.
            </summary>
            <param name="session">The session.</param>
            <param name="monitoredItem">The monitored item that produced the notification.</param>
            <param name="notification">The notification.</param>
            <param name="knownEventTypes">The known event types.</param>
            <param name="eventTypeMappings">Mapping between event types and known event types.</param>
            <returns>
            The event object. Null if the notification is not a valid event type.
            </returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.TranslateBrowsePaths(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.NamespaceTable,System.String[])">
            <summary>
            Returns the node ids for a set of relative paths.
            </summary>
            <param name="session">An open session with the server to use.</param>
            <param name="startNodeId">The starting node for the relative paths.</param>
            <param name="namespacesUris">The namespace URIs referenced by the relative paths.</param>
            <param name="relativePaths">The relative paths.</param>
            <returns>A collection of local nodes.</returns>
        </member>
        <member name="M:OpcUaHelper.FormUtils.CollectFieldsForType(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId})">
            <summary>
            Collects the fields for the type.
            </summary>
            <param name="session">The session.</param>
            <param name="fields">The fields.</param>
            <param name="fieldNodeIds">The node id for the declaration of the field.</param>
        </member>
        <member name="M:OpcUaHelper.FormUtils.CollectFieldsForInstance(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId})">
            <summary>
            Collects the fields for the instance.
            </summary>
            <param name="session">The session.</param>
            <param name="fields">The fields.</param>
            <param name="instanceId">The node id for the declaration of the field.</param>
        </member>
        <member name="M:OpcUaHelper.FormUtils.CollectFields(Opc.Ua.Client.Session,Opc.Ua.NodeId,Opc.Ua.QualifiedNameCollection,Opc.Ua.SimpleAttributeOperandCollection,System.Collections.Generic.List{Opc.Ua.NodeId},System.Collections.Generic.Dictionary{Opc.Ua.NodeId,Opc.Ua.QualifiedNameCollection})">
            <summary>
            Collects the fields for the instance node.
            </summary>
            <param name="session">The session.</param>
            <param name="nodeId">The node id.</param>
            <param name="parentPath">The parent path.</param>
            <param name="fields">The event fields.</param>
            <param name="fieldNodeIds">The node id for the declaration of the field.</param>
            <param name="foundNodes">The table of found nodes.</param>
        </member>
        <member name="M:OpcUaHelper.FormUtils.ContainsPath(Opc.Ua.SimpleAttributeOperandCollection,Opc.Ua.QualifiedNameCollection)">
            <summary>
            Determines whether the specified select clause contains the browse path.
            </summary>
            <param name="selectClause">The select clause.</param>
            <param name="browsePath">The browse path.</param>
            <returns>
            	<c>true</c> if the specified select clause contains path; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:OpcUaHelper.Properties.Resources">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Culture">
            <summary>
              重写当前线程的 CurrentUICulture 属性
              重写当前线程的 CurrentUICulture 属性。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Activity_16xLG">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.brackets_Square_16xMD">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Class_489">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.ClassIcon">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Cloud_16xLG">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Copy_6524">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Delegate_8339">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.docview_xaml_on_16x16">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Enum_582">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Event_594">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Event_594_exp">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.glasses_16xLG">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.grid_Data_16xLG">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.lightningBolt_16xLG">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.ListView_687">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.loading">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Method_636">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Module_648">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Structure_507">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Table_748">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.Tag_7213">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.usbcontroller">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:OpcUaHelper.Properties.Resources.VirtualMachine">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
    </members>
</doc>
