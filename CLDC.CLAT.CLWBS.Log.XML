<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.Log</name>
    </assembly>
    <members>
        <member name="P:CLDC.CLAT.CLWBS.Log.LogProcess.StationId">
            <summary>
            专机编号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.PrintExceptionMsg(System.String)">
            <summary>
            打印异常信息
            </summary>
            <param name="msg">异常信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.PrintExceptionMsg(System.Exception)">
            <summary>
            打印异常信息
            </summary>
            <param name="msg">异常信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.PrintDebugMsg(System.Int32,System.String)">
            <summary>
            打印调式信息
            </summary>
            <param name="DeviceCode">设备编码</param>
            <param name="msg">调试信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.PrintAndSendMsg(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            打印发送运行信息以及错误信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.GetLogInfo(System.Int32)">
            <summary>
            获取日志信息
            </summary>
            <param name="code">日志编码+等级编码</param>
            <returns>日志信息</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.GetSolutionInfo(System.Int32)">
            <summary>
            根据日志编码获取该异常的原因及解决方案
            </summary>
            <param name="code">日志+等级日志编码</param>
            <returns>知识库信息</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Log.LogProcess.GetSystemTypeCode">
            <summary>
            获取所属系统的配置信息
            </summary>
            <returns>所属系统ID，如果不存在该参数，则返回0</returns>
        </member>
    </members>
</doc>
