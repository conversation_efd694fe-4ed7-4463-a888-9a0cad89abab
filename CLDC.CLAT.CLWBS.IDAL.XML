<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.IDAL</name>
    </assembly>
    <members>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ICommonMethod">
            <summary>
            公共接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ICommonMethod.TransferDataToHisTable(System.String,System.String,System.String,System.Int32)">
            <summary>
            迁移实时表数据到历史表数据(整张表迁移)
            </summary>
            <param name="tableName">实时表名</param>
            <param name="strWhere">实时表查询条件：带 where 关键字</param>
            <param name="HisTableName">历史表名</param>
            <param name="dataType">迁移数据类型：1：试验数据；2、表信息(T_BD_METER_INFO)；3、检定表信息(T_BO_METER_CHECK)；4、踪合检定结论表(MT_TRIALDATA_RESULT)；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ICommonMethod.DeleteDatasFromTaBale(System.String,System.String)">
            <summary>
            从指定表中删除数据
            </summary>
            <param name="tableName"></param>
            <param name="strWhere"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.IConversionInfo">
            <summary>
            转换信息接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IConversionInfo.GetData">
            <summary>
            获取转换信息
            </summary>
            <returns>返回转换信息集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IConversionInfo.GetData(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.Decimal,System.Boolean)">
            <summary>
            获取转换信息
            </summary>
            <param name="typeCode">编码类别</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回转换信息集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IConversionInfo.SaveConversionInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.ConversionInfos})">
            <summary>
            保存码值信息
            </summary>
            <param name="lstMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IDictItem.GetItemName(System.String,System.String)">
            <summary>
            数据字典查询
            </summary>
            <param name="typeCode"></param>
            <param name="itemValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IDictItem.GetTypeCodeAndValueByItemName(System.String,System.String@,System.String@)">
            <summary>
            根据项名称，获取字典里的类型编号和项值
            </summary>
            <param name="itemName"></param>
            <param name="typeCode"></param>
            <param name="itemValue"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IDictItem.GetDicValuesByTypeCode(System.String)">
            <summary>
            根据编码类别查询基础数据
            </summary>
            <param name="TypeCode"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ILocalDbFactory">
            <summary>
            本地库工厂
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ILocalDbFactory.Create``1">
            <summary>
            创建对象
            </summary>
            <typeparam name="T">接口</typeparam>
            <returns>返回对象</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo">
            <summary>
            表信息接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.UpdateMeterCheck(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表检定信息
            </summary>
            <param name="isTomis">0未上传 1上传成功 2待重传</param>
            <param name="lstMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.GetMeterInfo(System.Boolean)">
            <summary>
            获取表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.GetMeterErrorInfo(System.String)">
            <summary>
            获取表误差详细信息
            </summary>
            <param name="where"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.GetOriginalErrorEntityList(CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity)">
            <summary>
            查询条件拼凑
            </summary>
            <param name="or"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.GetMeterInfo(System.String)">
            <summary>
            获取单个表详细信息
            </summary>
            <param name="guidId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.SaveMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            保存表信息
            </summary>
            <param name="lstMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.SaveMeterErrorInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity})">
            <summary>
            保存表原有误差信息
            </summary>
            <param name="lstMeterErrorInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.SaveMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存表信息
            </summary>
            <param name="meterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.SaveMeterErrorInfo(CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity)">
            <summary>
            保存表原有误差信息
            </summary>
            <param name="meterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.DeleteMeterInfo">
            <summary>
            删除表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.DeleteMeterErrorInfo(System.String)">
            <summary>
            删除表原有误差信息
            </summary>
            <param name="meterid"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.SaveMeterAddress(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存表地址
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.UpdateMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表信息的CHECK_RESULT状态
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterInfo.UpdateMeterInfoBaudRate(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            更新表信息波特率字段
            </summary>
            <param name="meterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterProtocol.GetProtocolInfo">
            <summary>
            获取本地数据库中表协议信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IMeterProtocol.SaveProtocolInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StProtocol})">
            <summary>
            保存表协议信息到本地数据库
            </summary>
            <param name="lstProtocols"></param>
            <returns></returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.Converter">
            <summary>
            数据转换者
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetData(System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,System.Int32}}@,System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            获取表信息
            </summary>
            <param name="taskId">任务号</param>
            <param name="outWarehouseNumber">出库编号</param>
            <param name="equipCateg">设备类型</param>
            <param name="dicPileMeterNum">返回垛分组表数量,key流水号，value垛分组表数量</param>
            <returns>返回表信息列表</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetDataCount(System.String,System.String)">
            <summary>
            获取表信息条数
            </summary>
            <param name="taskId"></param>
            <param name="outWarehouseNumber"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetDatas(System.String,System.String)">
            <summary>
            根据条码从中间库获取表结构
            </summary>
            <param name="DEVICE_NO">检定装置编号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.Update(System.Collections.Generic.List{System.String},CLDC.CLAT.CLWBS.DataModel.Enum.EmOutWarehouseHandleSate,System.String)">
            <summary>
            更新出库表状态
            </summary>
            <param name="barcodeList">条码列表</param>
            <param name="state">出库处理状态</param>
            <returns>成功，返回true</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.OutWarehouseNumber(System.String,System.String)">
            <summary>
            获取表信息流水号
            </summary>
            <param name="barcode">箱条码</param>
            <param name="taskNo">任务号</param>
            <returns>返回对应箱的流水号</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.OutWarehousePileCode(System.String,System.String)">
            <summary>
            获取出库垛号
            </summary>
            <param name="barcode">箱条码</param>
            <param name="taskNo">任务号</param>
            <returns>返回对应箱的流水号</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetCodeValueMid(System.String,System.String)">
            <summary>
            根据平台值获取平台编码
            </summary>
            <param name="name">平台值</param>
            <param name="code">编码类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetTaskIdByBarCode(System.String,CLDC.CLAT.CLWBS.DataModel.Enum.EmOutWarehouseHandleSate)">
            <summary>
            根据表条码获取任务号
            </summary>
            <param name="barCode">表条码</param>
            <param name="halfag">处理标记</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetData(System.String,System.String)">
            <summary>
            根据批次号与设备类别获取设备型号
            </summary>
            <param name="batchNo"></param>
            <param name="equipCateg"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IPlatformMeterInfo.GetData(System.String)">
            <summary>
            根据批次号获取设备型号
            </summary>
            <param name="equipCateg"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan">
            <summary>
            保存上传接口
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.HisTableName">
            <summary>
            历史表名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.SupportEquipmentType">
            <summary>
            支持设备类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.TrialTypeList">
            <summary>
            试验类型列表
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.Converter">
            <summary>
            数据转换者
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.SqlTextBuilder">
            <summary>
            SQL语句拼接对像
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.CleanSqlText(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Data.IDbTransaction)">
            <summary>
            删除数据的SQL语句
            </summary>
            <param name="dataInfo"></param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存（分项）数据到数据库的SQL语句集合
            </summary>
            <param name="lstTrialData">数据集合</param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存（综合）数据到数据库的SQL语句集合
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            上传（综合）数据到平台的sql
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUploadSiChuan.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData">实验数据</param>
            <param name="tableName">查询表名</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload">
            <summary>
            保存上传接口
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.HisTableName">
            <summary>
            历史表名
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.SupportEquipmentType">
            <summary>
            支持设备类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.TrialTypeList">
            <summary>
            试验类型列表
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.Converter">
            <summary>
            数据转换者
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.SqlTextBuilder">
            <summary>
            SQL语句拼接对像
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.CleanSqlText(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Data.IDbTransaction)">
            <summary>
            删除数据的SQL语句
            </summary>
            <param name="dataInfo"></param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存（分项）数据到数据库的SQL语句集合
            </summary>
            <param name="lstTrialData">数据集合</param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存（综合）数据到数据库的SQL语句集合
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            上传（综合）数据到平台的JSON字符串
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISaveUpload.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData">实验数据</param>
            <param name="tableName">查询表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISqlTextBuilder.GetDeleteSql(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            获取删除SQL语句
            </summary>
            <param name="tableName"></param>
            <param name="taskId"></param>
            <param name="meterBarcode"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISqlTextBuilder.GetInsertSql(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.DataTableColumnEntity})">
            <summary>
            获取插入SQL名句
            </summary>
            <param name="tableName"></param>
            <param name="fieldDictionary"></param>
            <param name="lstColumnDataType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ISqlTextBuilder.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData"></param>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITerminalIpAddress.GetTerminalIpInfo">
            <summary>
            获取终端IP信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITerminalIpAddress.SaveTerminalIpInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTerminalIp})">
            <summary>
            保存表信息
            </summary>
            <param name="lstTerminalInfo"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData">
            <summary>
            实验数据接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.SaveTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存试验数据(控制中心使用)
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.UpdateTrialTime(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local,System.Int32)">
            <summary>
            更新试验起始结束时间
            </summary>
            <param name="trialScheme"></param>
            <param name="Flag"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.SaveTrialData(System.Boolean,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存试验数据(工位软件使用)
            </summary>
            <param name="uploaded"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.SaveTrialData(System.Boolean,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            保存试验数据(工位软件使用)
            </summary>
            <param name="uploaded"></param>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.String,System.String)">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="tableName">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml},System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{System.String}})">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="tableName">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.UpdateIsuplodaedForXml(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml})">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialDataSum">
            <summary>
            获取所有实验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.String)">
            <summary>
            根据试验类型，源控参数，项目参数抓取试验数据
            </summary>
            <param name="trialType"></param>
            <param name="SOURCEPARAMS"></param>
            <param name="ITEMPARAMS"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取指定表位试验数据
            </summary>
            <param name="trialType"></param>
            <param name="MeterID"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32)">
            <summary>
            通过主键及试验类型获取试验数据
            </summary>
            <param name="trialType"></param>
            <param name="itemid"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(System.Int32,System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            抓取指定表位试验数据
            </summary>
            <param name="itemid"></param>
            <param name="MeterNO"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取指定表条码的表指定控源参数的试验数据
            </summary>
            <param name="trialType"></param>
            <param name="MeterCode"></param>
            <param name="SOURCEPARAMS"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取试验数据
            </summary>
            <param name="trialType"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData_ALL">
            <summary>
            获取所有实验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetOneTrialData_IsUploadedFalse(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            某个试验项是否有未上传成功的数据
            </summary>
            <param name="trialType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData_UploadedFalse(System.String,System.String,System.Boolean)">
            <summary>
            抓取没有成功上传到服务器的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetTrialData_UploadedTrue">
            <summary>
            抓取成功上传到服务器的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.DeleteTrialData">
            <summary>
            删除试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.DeleteTrialData(System.Boolean)">
            <summary>
            删除试验数据
            </summary>
            <param name="uploaded"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.QueryTrialData(System.String)">
            <summary>
            根据SQL语句查询所想要的东西(返回Datatable)
            </summary>
            <param name="strSQL"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.ListQueryTrialData(System.String)">
            <summary>
            根据SQL语句查询所想要的东西(返回List)
            </summary>
            <param name="strSQL"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.GetDataSearch(System.Int32,System.String,System.String,System.String)">
            <summary>
            数据查询页面接口
            </summary>
            <param name="ITEMID"></param>
            <param name="METERNO"></param>
            <param name="METERID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialData.SaveTrialResultDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存实验结果数据（综合结论）
            </summary>
            <param name="meterInfos"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme">
            <summary>
            实验方案接口
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.GetTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@)">
            <summary>
            获取DB中的试验方案
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.GetParameterCheckScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck}@)">
            <summary>
            获取DB中的参数验证PARAMTED试验方案
            </summary>
            <param name="ParameterCheck_Info"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveParamCheckSchemeItems(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck},System.Int32)">
            <summary>
            保存参数验证方案到DB数据库
            </summary>
            <param name="lstParamCheck"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveParamCheckScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck,System.Int32)">
            <summary>
            保存参数验证方案到DB
            </summary>
            <param name="ParamCheckItem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.DeleteParameterSchemes">
            <summary>
            删除参数验证表PARAMTED
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local})">
            <summary>
            保存试验方案到DB数据库
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveTrialSchemeMES(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Boolean)">
            <summary>
            保存试验方案到DB数据库（MES解析存储方案）
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{System.Int32})">
            <summary>
            保存方案,先删除需要保存的项目编号，再保存
            </summary>
            <param name="lstTrialScheme"></param>
            <param name="delItemIDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.DeleteTrialScheme(System.String)">
            <summary>
            根据项目编号 删除试验项目
            </summary>
            <param name="delItemIDsWhere"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SetENCRYPTOR_SERVER(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            插入是否使用加密机检测多功能表
            </summary>
            <param name="ArrSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.GetENCRYPTOR_SERVER">
            <summary>
            获取是否使用加密机检测多功能表
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.SaveTrialScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            保存试验方案到Access数据库
            </summary>
            <param name="ArrSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.DeleteTrialScheme">
            <summary>
            删除试验方案
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.ITrialScheme.DeleteTrialSchemeByItemID(System.Int32)">
            <summary>
            根据 ItemID 删除试验项目
            </summary>
            <param name="itemID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DALInterface.IOracleTrialData.SaveTrialData(System.Collections.Generic.IList{System.String})">
            <summary>
            保存试验数据(写中间库)
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.LocalDbFactory">
            <summary>
            本地库工厂
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ConversionInfo">
             <summary>
            平台与内部码值 转换接口
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToInternalCode(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            转换为内部编码
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="platformCode">外部编码</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回内部编码</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToInternalValue(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            转换为内部值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="platformCode">外部编码</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回内部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToInternalValueFromInternalCode(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String)">
            <summary>
            内部编码转换为内部值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="internalCode">内部编码</param>
            <returns>返回内部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToInternalValueFromPlatformValue(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            外部值转换为内部值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="platformValue">外部值</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回内部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformCode(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            转换为外部编码
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="internalCode">内部编码</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回外部编码</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformCodeFromInternalValue(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            转换为外部编码
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="internalValue">内部值</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回外部编码</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformValue(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            转换为外部值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="internalCode">内部编码</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns>返回外部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformValueFromPlatformCode(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            外部码转换为外部值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="platformCode">外部编码</param>
             <param name="externlSysCode">外部系统编号</param>
            <returns>返回外部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformCodeFromPlatformValue(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.Int32)">
            <summary>
            外部值转换为外部码
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="PlatformValue">外部编码</param>
             <param name="externlSysCode">外部系统编号</param>
            <returns>返回外部值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.ToPlatformValueFromInternalCodeAndPlatformCode(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.String,System.String,System.Int32)">
            <summary>
            通过内部码及平台码获取平台值
            </summary>
            <param name="codeClass">编码类别</param>
            <param name="internalCode">内部编码</param>
            <param name="platformCode">外部编码</param>
            <param name="externlSysCode">外部系统编号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.GetConversionInfo(CLDC.CLAT.CLWBS.DataModel.Enum.CodeClass,System.Decimal,System.Boolean)">
            <summary>
            获取码值信息
            </summary>
            <param name="codeClass">编码类型</param>
            <returns>码值信息</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter.GetREAD_TYPE_CODE(System.String)">
            <summary>
            获取示数类型
            </summary>
            <param name="powerFlag">功率方向</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert">
            <summary>
            类型转换类
            处理数据库获取字段为空的情况
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.ToInt32(System.Object)">
            <summary>
            读取数据库中字符串并转换成Int32
            为空时返回0
            </summary>
            <param name="obj">object类型的值</param>
            <returns>Int32类型</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.ToInt32(System.String)">
            <summary>
            读取数据库中字符串并转换成Int32
            为空时返回0
            </summary>
            <param name="str">string类型的值</param>
            <returns>Int32类型</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.IsInt(System.String)">
            <summary>
            判断一个字符串是否属于Int类型
            如果是的返回true，如果不是返回false
            </summary>
            <param name="str">string类型的值</param>
            <returns>true：是Int的字符串(即可以转换成Int类型)，false：不是Int类型的字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.ToString(System.Object)">
            <summary>
             读取数据库中字符串并转换成string
            </summary>
            <param name="obj">object类型的值</param>
            <returns>string类型</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.IsDouble(System.String)">
            <summary>
            判断一个字符串是否属于Double类型(包括负浮点型)
            如果是的返回true，如果不是返回false
            </summary>
            <param name="str">string类型的值</param>
            <returns>true：是Double的字符串(即可以转换成Double类型)，false：不是Double类型的字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.ToDouble(System.Object)">
            <summary>
            读取数据库中字符串并转换成Int32
            为空时返回0
            </summary>
            <param name="obj">object类型的值</param>
            <returns>Int32类型</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.ToDouble(System.String)">
            <summary>
            读取数据库中字符串并转换成Int32
            为空时返回0
            </summary>
            <param name="str">string类型的值</param>
            <returns>Int32类型</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.DBConvert.isDateTime(System.String)">
            <summary>
            判断时间格式是否是时间类型
            如23:10
            </summary>
            <param name="str">要判断的字符串</param>
            <returns>true：是时间类型的字符串(即可以转换成时间类型)，false：不是时间类型的字符串</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.parmCache">
            <summary>
            哈希表用来存储缓存的参数信息，哈希表可以存储任意类型的参数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteNonQuery(System.String,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过指定专用的连接字符串
            使用参数数组形式提供参数列表
            使用示例：int result = ExecuteNonQuery(connString,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，T-SQL 文本命令语句'Text'(默认)，等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="commandParamters">以数组形式提供OleDbCommand命令中用到的参数列表</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteNonQuery(System.String,System.Data.CommandType,System.Collections.Generic.List{System.String},System.Data.OleDb.OleDbParameter[])">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过指定专用的连接字符串
            使用参数数组形式提供参数列表
            使用示例：int result = ExecuteNonQuery(connString,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </summary>
            <param name="connectionString">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，T-SQL 文本命令语句'Text'(默认)，等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="commandParamters">以数组形式提供OleDbCommand命令中用到的参数列表</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteNonQuery(System.Data.OleDb.OleDbConnection,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过一个已经存在的数据库连接
            使用参数数组提供参数
            </summary>
            <remarks>
            使用示例：
            int result = ExecuteNonQuery(conn,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </remarks>
            <param name="connection">一个现有的数据库连接</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，
            T-SQL 文本命令语句'Text'(默认)， 等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="commandParameters">以数组形式提供OleDbCommand命令中用到的参数列表</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteNonQuery(System.Data.OleDb.OleDbTransaction,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过一个已经存在的数据库事务处理
            使用参数数组提供参数
            </summary>
            <remarks>
            使用示例：
            int result = ExecuteNonQuery(trans,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </remarks>
            <param name="trans">一个存在的OleDb事务处理</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，
            T-SQL 文本命令语句'Text'(默认)， 等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="commandParameters">以数组形式提供OleDbCommand命令中用到的参数列表</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteDataset(System.String,System.Data.CommandType,System.String,System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            返回DATASET
            </summary>
            <param name="connectionString">连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="dataname">返回的DataSet中DataTable的名字</param>
            <param name="commandParameters">参数列表</param>
            <returns>返回DataSet</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteDataTable(System.String,System.String)">
            <summary>
            输入一条 查询的SQL语句，返回一个DataTable的结果集
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdText"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteDataTable(System.String,System.Data.CommandType,System.String,System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            返回DATATABLE
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="dataname">返回的DataSet中DataTable的名字</param>
            <param name="commandParameters">参数列表</param>
            <returns>返回DataSet</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteReader(System.String,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])" -->
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteScalar(System.String,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])" -->
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.ExecuteScalar(System.Data.OleDb.OleDbConnection,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.CacheParameters(System.String,System.Data.OleDb.OleDbParameter[])">
            <summary>
            缓存参数数组
            </summary>
            <param name="cacheKey">参数缓存的键值</param>
            <param name="commandParameters">被缓存的参数列表</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.GetCachedParameters(System.String)">
            <summary>
            获取被缓存的参数
            </summary>
            <param name="cacheKey">用于查找参数的KEY值</param>
            <returns>返回缓存的参数数组</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.PrepareCommand(System.Data.OleDb.OleDbCommand,System.Data.OleDb.OleDbConnection,System.Data.OleDb.OleDbTransaction,System.Data.CommandType,System.String,System.Data.OleDb.OleDbParameter[])" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OleDbHelper.GetMeter_ParaList(System.String)">
            <summary>
            获取检定任务列表
            </summary>
            <param name="strSQL">SQL语句</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.BeginTransaction(System.String)">
            <summary>
            事务对像
            </summary>
            <param name="dataBaseConnStringValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteNonQuery(System.String,System.Data.CommandType,System.String,System.Data.SQLite.SQLiteParameter[])">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过指定专用的连接字符串
            使用参数数组形式提供参数列表
            使用示例：int result = ExecuteNonQuery(connString,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，T-SQL 文本命令语句'Text'(默认)，等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="commandParamters">以数组形式提供OleDbCommand命令中用到的参数列表</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteDataTable(System.String,System.String)">
            <summary>
            输入一条 查询的SQL语句，返回一个DataTable的结果集
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdText">SQL查询语句</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteNonQuery(System.String,System.Data.CommandType,System.Collections.Generic.List{System.String})">
            <summary>
            执行一个不需要返回值的OleDbCommand命令，通过指定专用的连接字符串
            使用参数数组形式提供参数列表
            使用示例：int result = ExecuteNonQuery(connString,CommandType.StoredProcedure,"PulishOrders",new OleDbParameter("@prodid",24));
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型(存储过程'StoredProcedure'，表的名称'TableDirect'，T-SQL 文本命令语句'Text'(默认)，等等)</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <returns>返回一个数值表示此OleDbCommand命令执行后影响的行数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteDataTable(System.String,System.Data.CommandType,System.String,System.String,System.Data.SQLite.SQLiteParameter[])">
            <summary>
            返回DATATABLE
            </summary>
            <param name="dataBaseConnStringValue">数据库连接字符串</param>
            <param name="cmdType">OleDbCommand命令类型</param>
            <param name="cmdText">存储过程的名字或者表的名字或者 T-SQL 文本命令语句</param>
            <param name="dataname">返回的DataSet中DataTable的名字</param>
            <param name="commandParameters">参数列表</param>
            <returns>返回DataSet</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteReader(System.String,System.Data.CommandType,System.String,System.Data.SQLite.SQLiteParameter[])" -->
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteScalar(System.String,System.Data.CommandType,System.String,System.Data.SQLite.SQLiteParameter[])" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteDataTable(System.String,System.Data.DataTable,System.String)">
            <summary>
            批量保存数据
            </summary>
            <param name="dataBaseConnStringValue">数据连接字符串</param>
            <param name="dt">保存的数据</param>
            <param name="strHisTableName">历史表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteNonQuery(System.Data.SQLite.SQLiteTransaction,System.String,System.Object[])">
            <summary>
            事务
            </summary>
            <param name="transaction"></param>
            <param name="commandText"></param>
            <param name="paramList"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.ExecuteNonQueryTransaction(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            通过事物批量执行增、删、改
            </summary>
            <param name="dataBaseConnStringValue">数据连接字符串</param>
            <param name="lstSql">sql语句集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.AttachParameters(System.Data.SQLite.SQLiteCommand,System.String,System.Object[])">
            <summary>
            参数赋值
            </summary>
            <param name="cmd"></param>
            <param name="commandText"></param>
            <param name="paramList"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.DBUtility.SQLiteHelper.PrepareCommand(System.Data.SQLite.SQLiteCommand,System.Data.SQLite.SQLiteConnection,System.Data.SQLite.SQLiteTransaction,System.Data.CommandType,System.String,System.Data.SQLite.SQLiteParameter[])" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper">
            <summary>
            Oracle数据库帮助类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteNonQuery(System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            执行sql语句操作返回影响行数
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteScalar(System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            执行语句 返回类型为object的数据
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteReader(System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            执行语句  返回DataReader类型数据
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteDataTable(System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            执行带参数的查询返回DataTable类型
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteDataTable(System.Data.OracleClient.OracleTransaction,System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            带事务查询参数的查询
            </summary>
            <param name="trans"></param>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteDataSet(System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            查下返回DataSet
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="commandParameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteDataSet(System.Data.OracleClient.OracleTransaction,System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            事务查询返回DataSet
            </summary>
            <param name="trans"></param>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="commandParameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.InsertData(System.Data.CommandType,System.String,System.String,System.Data.DataTable,System.Data.OracleClient.OracleParameter[])">
            <summary>
            数据库中的表与DataTable 映射
            </summary>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="sourceTableName"></param>
            <param name="dt"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.DBUtility.OracleHelper.ExecuteNonQuery(System.Data.OracleClient.OracleTransaction,System.Data.CommandType,System.String,System.Data.OracleClient.OracleParameter[])">
            <summary>
            事务返回影响行数
            </summary>
            <param name="trans"></param>
            <param name="cmdType"></param>
            <param name="cmdText"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.CommonMethodFactory.CreateInstance">
            <summary>
            数据迁移
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.ConversionInfoFactory.CreateInstance">
            <summary>
            数据转换
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.CreateInstance">
            <summary>
            数据保存上传管理
            </summary>
            <returns></returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.SaveUploadList">
            <summary>
            实体模型集合
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.SaveUploadSiChuans">
            <summary>
            实体模型集合  四川
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.#ctor(System.String)">
            <summary>
            构造方法
            </summary>
            <param name="assemblyName">实体模型所在的程序集</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.GetCurrentAssemblyUpload(System.String)">
            <summary>
            加载实体模型
            </summary>
            <param name="assemblyName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.DataSaveUploadManager.GetCurrentOracleUpload(System.String)">
            <summary>
            加载实体模型四川
            </summary>
            <param name="assemblyName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.DictItemFactory.CreateInstance">
            <summary>
            数据字典
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.MeterInfoFactory.CreateInstance">
            <summary>
            表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.MeterProtocolFactory.CreateInstance">
            <summary>
            表协议信息管理
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.OracleTrialDataFactory.CreateInstance">
            <summary>
            实验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.PlatformMeterInfoFactory.CreateInstance">
            <summary>
            表协议信息管理
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.TerminalIpAddressFactory.CreateInstance">
            <summary>
            终端IP地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.TrialDataFactory.CreateInstance">
            <summary>
            实验数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.Factory.TrialSchemeFactory">
            <summary>
            实验方案
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.Factory.TrialSchemeFactory.CreateInstance">
            <summary>
            实验数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.BacthInfoManage">
            <summary>
            批次数据管理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.BacthInfoManage.SaveBacthInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StBatchInfo)">
            <summary>
            保存批次信息到本地数据库(Access)
            </summary>
            <param name="bacthInfo">批次的结构体</param>
            <returns>true :成功  false: 失败</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal">
            <summary>
            Access数据接口层静态对象类
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.IsAccessDBType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.DataBasePath">
            <summary>
            数据库配置路径
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.DataBaseConnStringValue">
            <summary>
            数据库连接字符串
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.LocalDbConnectionString">
            <summary>
            Oracle数据库连接字符串
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.GetConnStringValue">
            <summary>
            读取配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.GetAccessConnectionValue(System.String)">
            <summary>
            Access数据库连接字符串
            </summary>
            <param name="configValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DbGlobal.GetConfigValue(System.String)">
            <summary>
            读取配置信息
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.DictItemManageData">
            <summary>
            数据字典管理类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DictItemManageData.GetItemName(System.String,System.String)">
            <summary>
            数据字典查询
            </summary>
            <param name="typeCode">类型编码</param>
            <param name="itemValue">项值</param>
            <returns>项名称</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DictItemManageData.GetTypeCodeAndValueByItemName(System.String,System.String@,System.String@)">
            <summary>
            根据项名称，获取字典里的类型编号和项值
            </summary>
            <param name="itemName">项名称</param>
            <param name="typeCode"></param>
            <param name="itemValue"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.DictItemManageData.GetDicValuesByTypeCode(System.String)">
            <summary>
            根据编码类别查询基础数据
            </summary>
            <param name="TypeCode"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.ErrorMessageManage">
            <summary>
            故障信息管理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.ErrorMessageManage.SaveErrorMessage(CLDC.CLAT.CLWBS.DataModel.Struct.StExceptionInfo)">
            <summary>
            保存故障信息
            </summary>
            <param name="errorMsg">故障信息类对象</param>
            <returns>bool类型，如果返回true，则正常回复；否则，失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.ErrorMessageManage.GetErrorMessage(System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StExceptionInfo@)">
            <summary>
            抓取故障信息
            </summary>
            <param name="errorCode">故障编码</param>
            <param name="errorMsg">故障信息类对象</param>
            <returns>bool类型，如果返回true，则正常回复；否则，失败</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage">
            <summary>
            表基本信息管理类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.UpdateMeterCheck(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表检定信息
            </summary>
            <param name="isTomis">0未上传 1上传成功 2待重传</param>
            <param name="lstMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetMeterInfo(System.Boolean)">
            <summary>
            获取表详细信息
            </summary>
            <returns>表信息对象列表</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetMeterErrorInfo(System.String)">
            <summary>
            获取表误差详细信息
            </summary>
            <returns>表信息对象列表</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetOriginalErrorEntityList(CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity)">
            <summary>
            查询条件拼凑
            </summary>
            <param name="or"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.UpdateMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表信息
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetMeterInfo(System.Collections.Generic.List{System.String})">
            <summary>
            获取表详细信息
            </summary>
            <param name="lstGuidId">表唯一标识列表</param>
            <returns>表信息对象列表</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetMeterInfo(System.String)">
            <summary>
            获取单个表详细信息
            </summary>
            <param name="guidId">表唯一标识</param>
            <returns>表信息对象</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.Comm_Meter">
            <summary>
            获取表通讯参数
            </summary>
            <returns>通讯参数</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            保存表信息
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveMeterErrorInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity})">
            <summary>
            保存表原有误差信息
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveOneMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存单条表信息
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存表信息
            </summary>
            <param name="meterInfo">表信息对象</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveMeterErrorInfo(CLDC.CLAT.CLWBS.DataModel.Class.OriginalErrorEntity)">
            <summary>
            保存表原有误差信息
            </summary>
            <param name="meterInfo">表信息对象</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeletMeterData">
            <summary>
            删除表数据库数据：防止硬盘存不下  2011-8-16 陈大伟 
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeleteMeterInfo">
            <summary>
            删除表信息
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeleteMeterErrorInfo(System.String)">
            <summary>
            删除表原有误差信息
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeleteMeterInfo(System.String)">
            <summary>
            删除表信息
            </summary>
            <param name="guidId">表唯一标识</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetGuidId">
            <summary>
            获取12位(METERID中的N_VALUE)GUID,然后再+1(METERID中的N_VALUE+1)
            </summary>
            <returns>当前的GUID</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.MoveMeterInfoDataToHisDB">
            <summary>
            表信息数据备份到历史表信息，ACCESS暂时不用
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveSchmem(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local})">
            <summary>
            保存多条方案记录,参数验证项目单独保存  wj
            </summary>
            <param name="lstSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.Parameter(System.String)">
            <summary>
             分解参数验证参数
            </summary>
            <param name="lstSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveParameter(System.String[])">
            <summary>
            存参数验证的参数
            </summary>
            <param name="ItemParams"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.SaveSchmem(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            保存单条方案记录
            </summary>
            <param name="ArrSchmem">单条方案结构体</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeleteScheme">
            <summary>
            删除方案数据：为下载方案准备
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.DeleteParamted">
            <summary>
            删除参数验证数据：为下载方案准备
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterInfoManage.GetDataSearch(System.String,System.String,System.String)">
            <summary>
            数据查询
            </summary>
            <param name="ITEMID">实验项目</param>
            <param name="METERNO">表条码</param>
            <param name="METERID">表位号</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterProtocolManage">
            <summary>
            表协议信息管理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterProtocolManage.GetProtocolInfo">
            <summary>
            获取本地数据库(Access)中表协议信息
            </summary>
            <returns>表协议信息集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.MeterProtocolManage.SaveProtocolInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StProtocol})">
            <summary>
            保存表协议信息到本地数据库
            </summary>
            <param name="lstProtocols">表协议信息对象集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.OperationLogManage">
            <summary>
            日志数据管理类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.OperationLogManage.SaveOperationLog(CLDC.CLAT.CLWBS.DataModel.Class.ClOperationLog)">
            <summary>
            保存操作日志(未完成)
            </summary>
            <param name="log">操作日志类对象</param>
            <returns>bool类型，如果返回true，则正常回复；否则，失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.OperationLogManage.GetOperationLog(System.Int32,CLDC.CLAT.CLWBS.DataModel.Class.ClOperationLog@)">
            <summary>
            抓取操作日志(未完成)
            </summary>
            <param name="cmdType">操作命令类型</param>
            <param name="ClOperationLog">操作日志类对象</param>
            <returns>bool类型，如果返回true，则正常回复；否则，失败</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage">
            <summary>
            试验方案、试验数据管理类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@)">
            <summary>
            获取Access中的试验方案
            </summary>
            <param name="lstTrialScheme">方案数据结构对象集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetParameterCheckScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck}@)">
            <summary>
            获取Access中的参数验证PARAMTED试验方案
            </summary>
            <param name="ParameterCheck_Info">参数验证方案数据结构对象集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialSchemeTemp}@)">
            <summary>
            获取Access中的试验方案
            </summary>
            <param name="lstTrialScheme">方案数据结构对象集合</param>
            <param name="endTime">试验开始时间</param>
            <param name="startTime">试验结束时间</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetItemParams(System.Collections.Generic.List{System.String[]}@)">
            <summary>
            查找参数验证的参数
            </summary>
            <param name="ItemParams"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialScheme(CLDC.Framework.DataModel.Enum.EmTrialType,CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local@)">
            <summary>
            获取Access中的试验方案
            </summary>
            <param name="trialType">试验类型</param>
            <param name="trialScheme">方案数据结构对象</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveParamCheckSchemeItems(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck},System.Int32)">
            <summary>
            保存参数验证方案到Access数据库
            </summary>
            <param name="lstTrialScheme">方案数据结构对象集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveParamCheckScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck,System.Int32)">
            <summary>
            保存参数验证方案到Access数据库
            </summary>
            <param name="ParamCheckItem">参数验证方案数据结构对象</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteParameterSchemes">
            <summary>
            删除参数验证表PARAMTED
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local})">
            <summary>
            保存试验方案到Access数据库
            </summary>
            <param name="lstTrialScheme">方案数据结构对象集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialSchemeMES(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Boolean)">
            <summary>
            江西项目MES解析保存方案
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{System.Int32})">
            <summary>
            保存方案
            先删除需要保存的项目编号，再保存
            </summary>
            <param name="lstTrialScheme"></param>
            <param name="delItemIDs">需要删除的项目编号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialScheme(System.String)">
            <summary>
            根据项目编号 删除试验项目
            </summary>
            <param name="delItemIDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SetENCRYPTOR_SERVER(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            插入是否使用加密机检测多功能表
            </summary>
            <param name="ArrSchmem">方案实体</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetENCRYPTOR_SERVER">
            <summary>
            获取是否使用加密机检测多功能表
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            保存试验方案到Access数据库
            </summary>
            <param name="trialScheme">方案数据结构对象</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.CheckTrialSchemeByItemID(System.Int32)">
            <summary>
            检查项目ID是否已存在
            </summary>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialScheme">
            <summary>
            删除试验方案
            </summary>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialSchemeByItemID(System.Int32)">
            <summary>
            根据 ItemID 删除试验项目
            </summary>
            <param name="itemID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialScheme(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            删除试验方案
            </summary>
            <param name="trialType">试验类型</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存试验数据(控制中心使用)
            </summary>
            <param name="lstTrialData">试验数据集合</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.UpdateTrialTime(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local,System.Int32)">
            <summary>
            更新试验起始结束时间
            </summary>
            <param name="trialScheme">试验方案</param>
            <param name="Flag">1代表更新起始时间，2代表结束时间</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialData(System.Boolean,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存试验数据(工位软件使用)
            </summary>
            <param name="uploaded">试验数据上传控制中心是否成功</param>
            <param name="lstTrialData">试验数据集合</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.SaveTrialData(System.Boolean,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            保存试验数据(工位软件使用)
            </summary>
            <param name="uploaded">试验数据上传控制中心是否成功(true-成功)</param>
            <param name="trialData">试验数据对象</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.String,System.String)">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="tableName">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.UpdateIsuplodaedForXml(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml})">
            <summary>
            更新上传成功标志,wangjuan add;
            </summary>
            <param name="lstTrialData">试验结果集</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.FindDatByType(CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.String@)" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialDataSum">
            <summary>
            获取所有实验数据
            </summary> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.String)">
            <summary>
            根据试验类型，源控参数，项目参数抓取试验数据
            </summary>
            <param name="trialType">试验类型</param>
            <param name="SOURCEPARAMS">源控参数</param>
            <param name="ITEMPARAMS">项目参数</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取指定表位试验数据
            </summary>
            <param name="MeterID">表位号</param>
            <param name="lstTrialData">试验结果</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32)">
            <summary>
            通过主键及试验类型获取试验数据
            </summary>
            <param name="trialType"></param>
            <param name="itemid"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(System.Int32,System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            抓取指定表位试验数据
            </summary>
            <param name="MeterNO">表条码号</param>
            <param name="itemid">试验ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.String,System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取指定表条码的表指定控源参数的试验数据
            </summary>
            <param name="MeterCode">表条码</param>
            <param name="SOURCEPARAMS">控源参数</param>
            <param name="lstTrialData">试验结果</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            抓取试验数据
            </summary>
            <param name="trialType">试验类型</param>
            <param name="lstTrialData">试验数据集合</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetOneTrialData_IsUploadedFalse(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            某个试验项是否有未上传成功的数据
            </summary>
            <param name="trialType">实验类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData_UploadedFalse(System.String,System.String,System.Boolean)">
            <summary>
            抓取没有成功上传到服务器的试验数据
            </summary>
            <returns>未上传成功的检定结果集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.GetTrialData_UploadedTrue">
            <summary>
            抓取成功上传到服务器的试验数据
            </summary>
            <returns>上传成功的检定结果集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialData">
            <summary>
            删除试验数据
            </summary>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.DeleteTrialData(System.Boolean)">
            <summary>
            删除试验数据
            </summary>
            <param name="uploaded">试验数据上传控制中心是否成功(true-成功)</param>
            <returns>返回值(true-成功;false-失败)</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.QueryTrialData(System.String)">
            <summary>
            根据SQL语句查询所想要的东西(返回Datatable)
            </summary>
            <param name="strSQL">SQL语句</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.ListQueryTrialData(System.String)">
            <summary>
            根据SQL语句查询所想要的东西(返回List)
            </summary>
            <param name="strSQL">SQL语句</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.SchemeDataManage.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml},System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{System.String}})">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="tableName">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.OleDAL.TerminalIpAddressManage.SaveTerminalIpInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTerminalIp})">
            <summary>
            保存表信息
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <returns>返回值(true-成功；false-失败)</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.ConversionInfoAcc">
            <summary>
            码值转换
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.CommonMethodDAL.DeleteDatasFromTaBale(System.String,System.String)">
            <summary>
            删除指定表的数据
            </summary>
            <param name="tableName">删除的表名</param>
            <param name="strWhere">删除条件:带 where 关键字</param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.CommonMethodDAL.TransferDataToHisTable(System.String,System.String,System.String,System.Int32)">
            <summary>
            迁移实时表数据到历史表数据(整张表迁移)
            </summary>
            <param name="tableName">实时表名</param>
            <param name="strWhere">实时表查询条件：带 where 关键字</param>
            <param name="HisTableName">历史表名</param>
            <param name="dataType">迁移数据类型：1：试验数据；2、表信息(T_BD_METER_INFO)；3、检定表信息(T_BO_METER_CHECK)；4、踪合检定结论表(MT_TRIALDATA_RESULT)；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.CommonMethodDAL.ChecktIsExistData(System.Data.DataTable,System.String,System.Int32)">
            <summary>
            校验是否存在数据历史表中
            </summary>
            <param name="data">需要迁移的数据</param>
            <param name="hisTableName">历史表名</param>
            <param name="dataType">迁移数据类型：1：试验数据；2、表信息(T_BD_METER_INFO)；3、检定表信息(T_BO_METER_CHECK)；4、踪合检定结论表(MT_TRIALDATA_RESULT)；</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.ConversionInfo">
            <summary>
            转码
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.DictItemDAL">
            <summary>
            数据字典
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.DictItemDAL.GetDicValuesByTypeCode(System.String)">
            <summary>
            根据TypeCode获取基础数据
            </summary>
            <param name="TypeCode"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.DictItemDAL.GetDicValuesByLimet">
            <summary>
            获取误差限
            </summary>
            <param name="TypeCode">误差类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.DictItemDAL.GetItemName(System.String,System.String)">
            <summary>
            数据字典查询
            </summary>
            <param name="typeCode">类型编码</param>
            <param name="itemValue">项值</param>
            <returns>项名称</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.DictItemDAL.GetTypeCodeAndValueByItemName(System.String,System.String@,System.String@)">
            <summary>
            根据类别名称查询类别编码
            </summary>
            <param name="typeName"></param>
            <param name="typeCode"></param>
            <param name="itemValue"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL">
            <summary>
            表信息数据库操作类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.DeleteMeterCheck">
            <summary>
            刪除检定表信息 删除前先迁移到历史表
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.SaveMeterCheck(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存检定信息
            </summary>
            <param name="meterInfo"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.GetMeterCheck">
            <summary>
            获取检定信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.UpdateMeterCheck(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表检定信息
            </summary>
            <param name="isTomis">0未上传 1上传成功 2待重传</param>
            <param name="lstMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.DeleteMeterInfo">
            <summary>
            删除表信息（全表删除）删除前先迁移到历史表
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.GetMeterInfo(System.Boolean)">
            <summary>
            获取表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.GetMeterInfo(System.String)">
            <summary>
            根据GUID获取单个表信息
            </summary>
            <param name="guidId"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.SaveMeterAddress(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存表地址
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.SaveMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            保存表信息
            </summary>
            <param name="lstMeterInfo"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.SaveMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            保存单个表信息
            </summary>
            <param name="meterInfo"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.UpdateMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            更新表信息的CHECK_RESULT状态
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterInfoDAL.MoveMeterInfoDataToHisDB">
            <summary>
            迁移表信息到历史表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterProtocolDAL.GetProtocolInfo">
            <summary>
            获取本地数据库中表协议信息
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.MeterProtocolDAL.SaveProtocolInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StProtocol})">
            <summary>
            保存表协议信息到本地数据库
            </summary>
            <param name="lstProtocols"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_BASICERR_MET_CONC">
            <summary>
            B.2基本误差
            基本误差（51）、时钟示值误差（66）、标准偏差（52）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_BASICERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_BASICERR_MET_CONC.FormatIniError(System.String,System.String)" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONTROL_MET_CONC">
            <summary>
            控制试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONTROL_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONTROL_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_BASICERR_MET_CONC">
            <summary>
            B.53确定基本误差试验分项结论表
            试验类型 59703
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_BASICERR_MET_CONC.#ctor">
            <summary>
            确定基本误差试验分项结论表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_BASICERR_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_BASICERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_DISPLAY_ERR_MET_CONC">
            <summary>
            B.56确定监视示值误差和显示试验分项结论表
            试验类型 59706
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_DISPLAY_ERR_MET_CONC.#ctor">
            <summary>
            确定监视示值误差和显示试验分项结论表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_DISPLAY_ERR_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_DISPLAY_ERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MEASURE_REPEAT_MET_CONC">
            <summary>
            B.54确定测量重复性试验分项结论表
            试验类型 59703
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MEASURE_REPEAT_MET_CONC.#ctor">
            <summary>
            确定测量重复性试验分项结论表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MEASURE_REPEAT_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MEASURE_REPEAT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MUL_OUTPUT_STANDARD_MET_CONC">
            <summary>
            B.55确定多路输出的一致性试验分项结论表
            试验类型 59705
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MUL_OUTPUT_STANDARD_MET_CONC.#ctor">
            <summary>
            确定多路输出的一致性试验分项结论表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MUL_OUTPUT_STANDARD_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_MUL_OUTPUT_STANDARD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_SYMMETRY_MET_CONC">
            <summary>
            B.57确定对称度试验分项结论表
            试验类型 59707
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_SYMMETRY_MET_CONC.#ctor">
            <summary>
            确定对称度试验分项结论表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_SYMMETRY_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DETERMINE_SYMMETRY_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_HPLC_MET_CONC">
            <summary>
            HPLC芯片ID认证
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_HPLC_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_HPLC_MET_CONC.ChipIDAuthenticationCs(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            芯片认证
            </summary>
            <param name="lstTrialData"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_RESET_MET_CONC">
            <summary>
            清零
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_RESET_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_RESET_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CLOCK_VALUE_MET_CONC">
            <summary>
            B.34 时钟示值误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CLOCK_VALUE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CLOCK_VALUE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_COMMINICATE_MET_CONC">
             <summary>
            B.10通讯测试（与B.20 载波通信实验 共用一张表）
             </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONSIST_MET_CONC">
            <summary>
            B.12 误差一致性试验
            误差一致性试验（472）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONSIST_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONSIST_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONST_MET_CONC">
            <summary>
            电能表常数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_CONST_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DAYERR_MET_CONC.#ctor">
            <summary>
            日计时误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DAYERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMANDVALUE_MET_CONC">
            <summary>
            B.32 需量示值误差，试验项中也是最大需量误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMANDVALUE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMANDVALUE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMAND_MET_CONC">
             <summary>    
            B.31需量周期误差，都采用，需量示值误差
            B.32需量示值误差   (61,需量示值误差 )	
            B.33最大需量功能
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMAND_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_DEMAND_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_EQ_MET_CONC">
             <summary>
            剩余电量递减试验
             </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_EQ_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ERROR_MET_CONC">
            <summary>
            B.11 误差变差
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ERROR_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ERROR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name = "lstTrialData" > 数据集合 </param >
            <returns> JSON数据 </returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_MET_CONC">
            <summary>
            密钥下装
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_READ_MET_CONC">
            <summary>
            B.28 费控试验：MT_ESAM_READ_MET_CONC
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_SECURITY_MET_CONC">
            <summary>
            B.21 安全认证试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_SECURITY_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_ESAM_SECURITY_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_EVENT_RECORD_MET_CONC">
            <summary>
            B.24事件记录功能：MT_EVENT_RECORD_MET_CONC
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_EVENT_RECORD_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_EVENT_RECORD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_FEE_MET_CONC">
            <summary>
            B.27 费率和时段功能：MT_FEE_MET_CONC
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_FEE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_FEE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_HUTCHISON_COMBINA_MET_CONC.#ctor">
            <summary>
            计度器电能示值组合误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_HUTCHISON_COMBINA_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_INFLUENCE_QTY_MET_CONC">
            <summary>
            B.25影响量试验：MT_INFLUENCE_QTY_MET_CONC
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_INTUIT_MET_CONC">
            <summary>
            B.1外观检查结论
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_INTUIT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_MAX_DEMAND_ERR_MET_CONC">
             <summary>
            最大需量误差
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_MAX_DEMAND_ERR_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_MAX_DEMAND_ERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_MEASURE_REPEAT_MET_CONC">
            <summary>
            B.16 标准偏差（与基本误差共一张表）
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_OVERLOAD_MET_CONC">
             <summary>
            电流过载试验
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_OVERLOAD_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_OVERLOAD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PASSWORD_CHANGE_MET_CONC">
            <summary>
            B.26 485密码修改：MT_PASSWORD_CHANGE_MET_CONC
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PASSWORD_CHANGE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PASSWORD_CHANGE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_POWER_MEASURE_MET_CONC">
            <summary>
            B.30 电能计量功能：MT_POWER_MEASURE_MET_CONC
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_POWER_MEASURE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_POWER_MEASURE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_POWER_MET_CONC">
            <summary>
            功耗
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_POWER_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PRESETPARAM_MET_CONC">
            <summary>
            B.22、B.29预置参数设置、预置参数检查
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PRESETPARAM_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_PRESETPARAM_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项结论数据
            </summary>
            <param name="lstTrialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_SHORT_TERM_VARIATION_MET_CONC">
            <summary>
            B.58确定短期稳定性变差
            试验类型 59708
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_SHORT_TERM_VARIATION_MET_CONC.#ctor">
            <summary>
            确定短期稳定性变差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_SHORT_TERM_VARIATION_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取保存数据字段的SQL
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_SHORT_TERM_VARIATION_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_STANDARD_MET_CONC">
            <summary>
            B.9规约一致性检查
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_STANDARD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_STARTING_MET_CONC">
            <summary>
            B.3起动试验
            起动试验（54）、潜动试验（55）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_STARTING_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT">
            <summary>
            B.0 检定综合结论 MT_TRIALDATA_RESULT
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            获取字段字典列表（综合结论）
            </summary>
            <param name="meterInfo">表信息</param>
            <param name="lstTrialData">试验数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            上传（综合）数据到平台的JSON字符串
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT.GetTrialResult(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},CLDC.Framework.DataModel.Enum.EmTrialType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.String)">
            <summary>
            获取检定结果
            </summary>
            <param name="trialDataList">检定数据</param>
            <param name="trialType">当前试验类型</param>
            <param name="schemeInfoList">当前检定方案</param>
            <param name="detectParaType">当前检定项类型</param>
            <returns>如果检定方案中有该试验类型，但是该试验类型没有数据，返回“03”表示未检</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT.GetTrialResult(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},CLDC.Framework.DataModel.Enum.EmTrialType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Int32[])">
            <summary>
            获取检定结果
            </summary>
            <param name="trialDataList">检定数据</param>
            <param name="trialType">当前试验类型</param>
            <param name="schemeInfoList">当前检定方案</param>
            <returns>如果检定方案中有该试验类型，但是该试验类型没有数据，返回“03”表示未检</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TRIALDATA_RESULT.GetAbnormalCode(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            获取异常代码
            </summary>
            <param name="trialType"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TS_MET_CONC">
             <summary>
            时段投切误差
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TS_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_TS_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VARIATION_MET_CONC">
            <summary>
            负载电流升降变差试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VARIATION_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="dataInfo">数据信息</param>
            <param name="schemeInfo">方案信息</param>
            <param name="trialData">试验数据</param>
            <returns>返回字段字典列表</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VARIATION_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VOLT_MET_CONC">
            <summary>
            B.23交流耐压压试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VOLT_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_VOLT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传分项实验结论的JSON字符串
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_WAVE_MET_CONC">
            <summary>
            B.20 载波通信实验（一次采集成功率）
            载波通信实验（521）、485通讯测试（3）、无线通讯检测
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_WAVE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            载波通信实验：获取字段字典列表
            </summary>
            <param name="trialData">实验数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.Meter.MT_WAVE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            载波通信实验：上传（分项）数据到平台的JSON字符串
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.GetRelationCertificates">
            <summary>
            获取证书信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存（分项）数据到数据库的SQL语句集合
            </summary>
            <param name="lstTrialData">数据集合</param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存（综合）数据到数据库的SQL语句集合
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            上传（综合）数据到平台的JSON字符串
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表（分项结论）
            </summary>
            <param name="trialData">试验数据</param>
            <returns>返回数据字典</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            获取字段字典列表（综合结论）
            </summary>
            <param name="meterInfo">表信息</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.AddCommonFieldToDic(System.Collections.Generic.IDictionary{System.String,System.Object}@,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            公共字段添加字典集合中
            </summary>
            <param name="fieldDics">字典集合</param>
            <param name="trialData">实验数据对像</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.SetCommonFieldValue(CLDC.CLAT.CLWBS.DataModel.Class.PublicPropertyModel,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            设置公共字段值
            </summary>
            <param name="publicPropertyModel">实体模型基类</param>
            <param name="clTrialData_Local">实验数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData">实验数据</param>
            <param name="tableName">查询表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.FormatErrorData(System.String)">
            <summary>
            误差原始值列表格式化   竖线分割
            </summary>
            <param name="errorData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SaveUploaderBase.DiffSeconds(System.DateTime,System.DateTime)">
            <summary>
            计算起止时间差，返回秒
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SqlTextBuilder.GetDeleteSql(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            获取删除SQL语句
            </summary>
            <param name="tableName"></param>
            <param name="taskId"></param>
            <param name="meterBarcode"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SqlTextBuilder.GetInsertSql(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.DataTableColumnEntity})">
            <summary>
            获取插入SQL名句
            </summary>
            <param name="tableName"></param>
            <param name="fieldDictionary"></param>
            <param name="lstColumnDataType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.SqlTextBuilder.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData"></param>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TerminalIpAddressDAL">
            <summary>
            终端IP地址
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL">
            <summary>
            实验数据数据操作类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.DeleteTrialData">
            <summary>
            删除实验数据（主控发开始检定后，清空实验数据表）
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32)">
            <summary>
            根据实验类型，ItemID，获取实验数据
            </summary>
            <param name="trialType"></param>
            <param name="itemid"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local}@)">
            <summary>
            根据实验类型获取实验数据
            </summary>
            <param name="trialType"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetTrialData_ALL">
            <summary>
            获取所有数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetTrialData_UploadedFalse(System.String,System.String,System.Boolean)">
            <summary>
            获取上传失败的数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetDataSearch(System.Int32,System.String,System.String,System.String)">
            <summary>
            数据查询页面中 查询方法
            </summary>
            <param name="ITEMID">项目ID</param>
            <param name="METERNO">表条码</param>
            <param name="METERID">表位ID</param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.SaveTrialData(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存实验结果数据
            </summary>
            <param name="lstTrialData">实验数据结果集</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.SaveTrialResultDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存实验结果数据（综合结论数据）
            </summary>
            <param name="meterInfos"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.SaveTrialData(System.Boolean,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            保存实验数据（单个实验数据）
            </summary>
            <param name="uploaded"></param>
            <param name="trialData"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.String,System.String)">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="tableName">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.UpdateIsuplodaed(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml},System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{System.String}})">
            <summary>
            更新上传成功标志
            </summary>
            <param name="lstResult"></param>
            <param name="isuploaded">上传标记 0未上传 1已上传 2待重传</param>
            <param name="dicTabales">表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialDataDAL.GetSelectSQL(System.String,System.String)">
            <summary>
            获取查询实验数据SQL
            </summary>
            <param name="tableName"></param>
            <param name="strWhere"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL">
            <summary>
            实验方案数据库操作类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteTrialScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            根据 SCHEME_ID,ITEM_NAME,TRIAL_TYPE,POWER_PARAM, ITEM_PARAM 删除方案数据
            </summary>
            <param name="stTrialScheme_Local"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteParameterSchemes">
            <summary>
            删除参数验证表 T_AC_TRIAL_SCHEME_PARAM
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteParameterSchemesByItemID(System.Int32)">
            <summary>
            根据itemID删除参数验证表
            </summary>
            <param name="itemID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteTrialScheme(System.String)">
            <summary>
            根据项目编号 删除试验项目
            </summary>
            <param name="delItemIDsWhere">需要删除的ITEM_ID，支持多个</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteTrialScheme">
            <summary>
            删除实验方案
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteSchemeInfo(System.String)">
            <summary>
            根据方案ID删除方案信息
            </summary>
            <param name="schemeId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteSchemeInfo">
            <summary>
            删除方案信息
            </summary>
            <param name="schemeId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.DeleteTrialSchemeByItemID(System.Int32)">
            <summary>
            根据项目ItemID删除实验方案
            </summary>
            <param name="itemID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.GetENCRYPTOR_SERVER">
            <summary>
            获取是否使用加密机检测多功能表
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.GetParameterCheckScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck}@)">
            <summary>
            获取DB中的参数验证PARAMTED试验方案
            </summary>
            <param name="ParameterCheck_Info"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.GetTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@)">
            <summary>
            获取本地方案
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveParamCheckScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck,System.Int32)">
            <summary>
            保存参数验证方案到DB
            </summary>
            <param name="ParamCheckItem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveParamCheckSchemeItems(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StParameterCheck},System.Int32)">
            <summary>
            保存参数验证到数据库
            </summary>
            <param name="lstParamCheck"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local})">
            <summary>
            保存实验方案
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveTrialSchemeContinItemId(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            保存实验方案(含ItemId)
            </summary>
            <param name="ArrSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveTrialSchemeMES(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Boolean)">
            <summary>
            保存试验方案到DB数据库（MES解析存储方案）
            </summary>
            <param name="lstTrialScheme"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveTrialScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{System.Int32})">
            <summary>
            保存方案,先删除需要保存的项目编号，再保存
            </summary>
            <param name="lstTrialScheme"></param>
            <param name="delItemIDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SaveTrialScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            保存实验方案
            </summary>
            <param name="ArrSchmem"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.SetENCRYPTOR_SERVER(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local)">
            <summary>
            插入是否使用加密机检测多功能表
            </summary>
            <param name="ArrSchmem"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SQLiteDAL.TrialSchemeDAL.StrToInt(System.String)">
            <summary>
            字符串转Int
            </summary>
            <param name="te"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_BASICERR_MET_CONC">
            <summary>
            基本误差（51）、 分元合元之差（67）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_BASICERR_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_BASICERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            组拼上传数据SQL语句
            </summary>
            <param name="lstTrialData">数据集合</param>
            <returns>JSON数据</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CLOCK_VALUE_MET_CONC">
            <summary>
            时钟示值误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CLOCK_VALUE_MET_CONC.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_COMMINICATE_MET_CONC">
            <summary>
            接驳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_COMMINICATE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CONSIST_MET_CONC">
            <summary>
            误差一致性试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CONSIST_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            误差一致性，
            拆分扩展字段数据
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CONSIST_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CONST_MET_CONC">
            <summary>
            常数实验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CONST_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CREEPING_MET_CONC">
            <summary>
            潜动
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_CREEPING_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DAYERR_MET_CONC.#ctor">
            <summary>
            日计时误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DAYERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DEMAND_ERR_MET_CONC.#ctor">
            <summary>
            最大需量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DEMAND_ERR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DETECT_ELEC_PRICE.#ctor">
            <summary>
            电价比对
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DETECT_ELEC_PRICE.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DETECT_RSLT">
            <summary>
            综合结论-四川
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DETECT_RSLT.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            获取综合结论字段
            </summary>
            <param name="meterInfo"></param>
            <param name="lstTrialData"></param>
            <param name="lstTrialScheme"></param>
            <param name="lstMachineInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_DETECT_RSLT.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取分项结论字段
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_EQ_MET_CONC.#ctor">
            <summary>
            剩余电量递减试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_EQ_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_EQ_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ERROR_MET_CONC.#ctor">
            <summary>
            误差变差试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ERROR_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            误差一致性，
            拆分扩展字段数据
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ERROR_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_MET_CONC.#ctor">
            <summary>
            密钥下装
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_READ_MET_CONC.#ctor">
            <summary>
            费控
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_READ_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_ESAM_READ_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_HUTCHISON_COMBINA_MET_CONC.#ctor">
            <summary>
            计度器总电能组合示值误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_HUTCHISON_COMBINA_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_HUTCHISON_COMBINA_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_INTUIT_MET_CONC.#ctor">
            <summary>
            外观检查
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_INTUIT_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_INTUIT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_MEASURE_REPEAT_MET_CONC.#ctor">
            <summary>
            标准偏差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_MEASURE_REPEAT_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_MEASURE_REPEAT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_OVERLOAD_MET_CONC.#ctor">
            <summary>
            电流过载试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_OVERLOAD_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_OVERLOAD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MEASURE_MET_CONC.#ctor">
            <summary>
            电能计量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MEASURE_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MEASURE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MET_CONC.#ctor">
            <summary>
            功耗实验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_POWER_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_PRESETPARAM_CHECK_MET_CONC.#ctor">
            <summary>
            参数验证
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_PRESETPARAM_CHECK_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_PRESETPARAM_CHECK_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STANDARD_MET_CONC.#ctor">
            <summary>
            规约一致性检查
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STANDARD_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STANDARD_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STARTING_MET_CONC.#ctor">
            <summary>
            启动
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STARTING_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_STARTING_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_TS_MET_CONC.#ctor">
            <summary>
            时段投切误差
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_TS_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_TS_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VARIATION_MET_CONC.#ctor">
            <summary>
            负载电流升降变差试验
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VARIATION_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VARIATION_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VOLT_MET_CONC.#ctor">
            <summary>
            交流耐压
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VOLT_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_VOLT_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_WAVE_MET_CONC.#ctor">
            <summary>
            载波通信实验（一次采集成功率）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_WAVE_MET_CONC.GetFieldDictionary(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取数据库字段和对应值
            List-表位条数
            Dic-key=字段，values=值
            </summary>
            <param name="trialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.Meter.MT_WAVE_MET_CONC.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            获取上传数据SQL语句
            </summary>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.OraTrialDataDAL.SaveTrialData(System.Collections.Generic.IList{System.String})">
            <summary>
            保存数据到中间库
            </summary>
            <param name="strSql"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetDatas(System.String,System.String)">
            <summary>
            获取表信息
            </summary>
            <param name="UNIT_NO">线体编号</param>
            <param name="DEVICE_NO">工位编号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetMeterData(System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.Dictionary{System.String,System.Int32}}@,System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            获取表中间库数据
            </summary>
            <param name="taskId"></param>
            <param name="outWarehouseNumber"></param>
            <param name="equipCateg"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetData(System.Collections.Generic.List{System.String},CLDC.CLAT.CLWBS.DataModel.Enum.EmBarcodeType)">
            <summary>
            根据条码从中间库获取表结构
            </summary>
            <param name="listBarcodes"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetTerminalData(System.Collections.Generic.List{System.String})">
            <summary>
            根据条码从中间库获取终端的表结构，如果一个条码有多条数据，只取第一条数据
            </summary>
            <param name="listBarcodes">条码集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetMeterData(System.Collections.Generic.List{System.String})">
            <summary>
            根据条码从中间库获取电表的表结构，如果一个条码有多条数据，只取第一条数据
            </summary>
            <param name="listBarcodes">条码集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.PlatformMeterInfo.GetData(System.String)">
            <summary>
            根据批次号获取设备型号
            </summary>
            <param name="equipCateg"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.GetRelationCertificates">
            <summary>
            获取证书信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            保存（分项）数据到数据库的SQL语句集合
            </summary>
            <param name="lstTrialData">数据集合</param>
            <param name="trans"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.SaveSqlList(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            保存（综合）数据到数据库的SQL语句集合
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})" -->
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            获取字段字典列表（分项结论）
            </summary>
            <param name="trialData">试验数据</param>
            <returns>返回数据字典</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.GetFieldDictionaryList(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            获取字段字典列表（综合结论）
            </summary>
            <param name="meterInfo">表信息</param>
            <param name="lstTrialData">数据集合</param>
            <param name="lstTrialScheme">方案集合</param>
            <param name="lstMachineInfo">专机信息集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.AddCommonFieldToDic(System.Collections.Generic.IDictionary{System.String,System.Object}@,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            公共字段添加字典集合中
            </summary>
            <param name="fieldDics">字典集合</param>
            <param name="trialData">实验数据对像</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.SetCommonFieldValue(CLDC.CLAT.CLWBS.DataModel.Class.PublicPropertyModel,CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local)">
            <summary>
            设置公共字段值
            </summary>
            <param name="publicPropertyModel">实体模型基类</param>
            <param name="clTrialData_Local">实验数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.IsMeterBelowStandard(CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local,System.String)">
            <summary>
            同一实验同一表位是否存在连续两次不合格
            </summary>
            <param name="lstTrialData">实验数据</param>
            <param name="tableName">查询表名</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.IDAL.SiChuan.SaveUploaderBase.Upload(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo})">
            <summary>
            上传综合结论数据SQL
            </summary>
            <param name="lstMeterInfo"></param>
            <param name="lstTrialData"></param>
            <param name="lstTrialScheme"></param>
            <param name="lstMachineInfo"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
    </members>
</doc>
