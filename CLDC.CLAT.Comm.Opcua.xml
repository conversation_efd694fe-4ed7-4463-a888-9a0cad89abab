<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.Comm.Opcua</name>
    </assembly>
    <members>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Open(System.Int32)">
            <summary>
            <para>Discription: 打开读写通道</para>
            <para>-----------: 打开时需传入模式类型，0：测试模式，1：正式运行模式，2：调试模式</para>
            <para>Return-----: 成功与否</para>
            <para>-----------# true:代表打开成功</para>
            <para>-----------# false:代表打开失败</para>
            <param name="runModel">运行模式</param>
            </summary>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Open(System.String,System.Int32)">
            <summary>
            打开读写通道
            <para>-----------: 打开时需传入模式类型，0：测试模式，1：正式运行模式，2：调试模式</para>
            <param name="runModel">运行模式</param>
            <param name="plcAddress">OPCUA服务地址</param>
            </summary>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Close">
            <summary>
            <para>Discription: 关闭读写通道</para>
            </summary>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.AddGroup(System.String,System.Boolean)">
            <summary>
            <para>Discription: 添加读写地址组</para>
            </summary>
            <param name="groupName">添加组名</param>
            <param name="isAsy">该组是订阅异步</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.AddItem(System.String,System.String,System.Boolean)">
            <summary>
            <para>Discription: 向组内添加单个项</para>
            </summary>
            <param name="groupName">将项要添加到的组名</param>
            <param name="itemName">添加的项名</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.AddItems(System.String,System.String[],System.Boolean)">
            <summary>
            <para>Discription: 向组内添加多个项</para>
            </summary>
            <param name="groupName">将项要添加到的组名</param>
            <param name="itemName">添加的多项的项名组</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Write(System.String,System.String,System.Object,System.Boolean)">
            <summary>
            <para>Discription: 向PLC写入单个项值（地址值）</para>
            <para>--------#Return:成功与否</para>
            <para>--------#True:成功</para>
            <para>--------#false:失败</para>
            </summary>
            <param name="groupName">要写入项所有的组名</param>
            <param name="itemName">项名组（地址）</param>
            <param name="value">项值（地址值）</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Write(System.String,System.Collections.Generic.Dictionary{System.String,System.Object},System.Boolean)">
            <summary>
            <para>Discription: 向PLC写入多个项值（地址值）</para>
            <para>--------#Return:成功与否</para>
            <para>--------#True:成功</para>
            <para>--------#false:失败</para>
            </summary>
            <param name="groupName">要写入项所有的组名</param>
            <param name="itemName">多个项,Key：项名，Value：项值</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Read(System.String,System.String,System.Boolean)">
            <summary>
            <para>Discription: 读取单个项值（地址值）</para>
            <para>Return-----: 返回读取的值</para>
            </summary>
            <param name="groupName">要读取项所有的组名</param>
            <param name="itemName">要读取的项名</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperate.Read(System.String,System.Collections.Generic.List{System.String},System.Boolean)">
            <summary>
            <para>Discription: 读取多个项值（地址值）</para>
            <para>Return-----: 返回读取的值列表</para>
            </summary>
            <param name="groupName">要读取项所有的组名</param>
            <param name="itemName">要读取的项名列表</param>
            <param name="isException">是否返回实际异常</param>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperateCallBack.Receive(System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            接收PLC返回的信息
            <para>----#其中返回的信息中Key值为地址，Value值为地址值</para>
            <param name="groupName">分组信息</param>
            <param name="addressValue">返回的信息</param>
            </summary>
        </member>
        <member name="F:CLDC.CLAT.Comm.Opcua.Model.EmValueRank.GeneralValue">
            <summary>
            数值
            </summary>
        </member>
        <member name="F:CLDC.CLAT.Comm.Opcua.Model.EmValueRank.Array">
            <summary>
            一维数组
            </summary>
        </member>
        <member name="F:CLDC.CLAT.Comm.Opcua.Model.EmValueRank.DArray">
            <summary>
            二维数组
            <para>int[,]</para>
            </summary>
        </member>
        <member name="P:CLDC.CLAT.Comm.Opcua.PLCOpcuaService.opcuaClient">
            <summary>
            服务对象
            </summary>
        </member>
        <member name="P:CLDC.CLAT.Comm.Opcua.PLCOpcuaService.runModel">
            <summary>
            运行模式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.Comm.Opcua.PLCOpcuaService.opcServerUrl">
            <summary>
            服务地址
            </summary>
        </member>
        <member name="M:CLDC.CLAT.Comm.Opcua.PLCOperateRealize.#ctor(CLDC.CLAT.Comm.Opcua.Interface.IOPCUAOperateCallBack)">
            <summary>
            默认以SimaticNET方式构造同步服务对象
            </summary>
            <param name="opcOperateCallBack">PLC回调对象</param>
        </member>
    </members>
</doc>
