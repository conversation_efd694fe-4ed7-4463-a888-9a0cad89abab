<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.COMM.REST</name>
    </assembly>
    <members>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.MethodURLAttribute">
            <summary>
            用于路径属性
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.MethodURLAttribute.MethodUrl">
            <summary>
            路径
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.MethodURLAttribute.RequestMethod">
            <summary>
            请求方式
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.MethodURLAttribute.GetDataStr">
            <summary>
            GET请求字段
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.MethodURLAttribute.#ctor(System.String,CLDC.CLAT.CLWBS.COMM.REST.DataContract.DataAttribute.RequestMethod,System.String)">
            <summary>
            构造
            </summary>
            <param name="url">路径</param>
            <param name="des">描述</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod">
            <summary>
            调用MES接口枚举
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getDetectEquipEnquire">
            <summary>
            被检品信息查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getDetectSchemeQuery">
            <summary>
            检定方案查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.upDetectSubentryRslt">
            <summary>
            检定分项结论上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.setResults">
            <summary>
            检定综合结论上传接口 
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.fileUpload">
            <summary>
            外观检测照片上传接口
            照片的命名规则：设备条码_检定任务单编号_照片属性_合格标志_编号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getEquipCarveCode">
            <summary>
            雕刻数据信息查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.uploadEquipCarveCode">
            <summary>
            雕刻验证信息上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getEquipEleSeal">
            <summary>
            电子封印信息查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.upEquipEleSeal">
            <summary>
            电子封印结果上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getEquipEleLabel">
            <summary>
            电子标签写入信息查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.upEquipEleLabel">
            <summary>
            电子标签写入结果上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getAlarm">
            <summary>
            异常告警接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getMachineInfo">
            <summary>
            专机信息查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getPCode">
            <summary>
            标准代码查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getDetectSchemeList">
            <summary>
            检定方案列表查询接口
            线体专机工位软件每日24点定时调REST用接口，通过系统编号获取检定方案列表，MES返回检定方案列表。
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getHplcIdCertRslt">
            <summary>
            芯片ID认证结果查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.setTerminalTemp">
            <summary>
            端子温度上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.setEnvironmentTemp">
            <summary>
            环境温湿度上传接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getVerificationCode">
            <summary>
            专机软件随机验证码查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.checkVerificationCode">
            <summary>
            专机软件进入管理员模式鉴权接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getSoftVersionRslt">
            <summary>
            工位软件版本号查询接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod.getSoftVersionDownload">
            <summary>
            工位软件最新版本下载接口
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethodExtensions">
            <summary>
            MES接口枚举扩展方法
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethodExtensions.GetMethodURL(CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod)">
            <summary>
            获取接口访问路径字符串
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.PCodeConvert.CUR_PERCENT">
            <summary>
            额定电流下的百分数值
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.PCodeConvert.meterFaultType">
            <summary>
            不合格原因对应关系待定
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.Service.RESTRequester">
            <summary>
            访问主站WebApi(REST)
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Service.RESTRequester.HttpPostFormDataAsync(System.String,System.String,System.Byte[])">
            <summary>
            使用multipart/form-data方式上传文件
            </summary>
            <param name="requestUrl">请求接口地址</param>
            <param name="imageName">图片文件名称</param>
            <param name="bytes">图片字节数据</param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.REST.Service.ServiceProxy.communication">
            <summary>
            MES通讯
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Service.ServiceProxy.CallMESRestApi(CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            调用MES接口
            </summary>
            <param name="method"></param>
            <param name="josn"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.Service.ServiceProxyFactory">
            <summary>
            代理服务工厂
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Service.ServiceProxyFactory.CreateServiceProxy(System.String,System.String,System.String)">
            <summary>
            创建代理服务实例
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.IServiceProxy">
            <summary>
            REST外部通讯契约
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.IServiceProxy.CallMESRestApi(CLDC.CLAT.CLWBS.COMM.REST.DataContract.MESMethod,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            调用MES接口
            </summary>
            <param name="method"></param>
            <param name="josn"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.DataConversionJsonHelperCls">
            <summary>
            
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.DataConversionJsonHelperCls.ParsMeterJsonInfo(System.String,CLDC.CLAT.CLWBS.IDAL.DBUtility.DataConverter,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}}@,System.String@)">
            <summary>
            JSON数据解析表信息到Dictionary
            </summary>
            <param name="MeterJson"></param>
            <param name="MeterInfos"></param>
            <param name="ErrInfo"></param>
            <param name="LstTrayNo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.DataConversionJsonHelperCls.ParsSchemeJsonInfo(System.String,CLDC.CLAT.CLWBS.DataModel.Class.SchemeInfoReqReutrndata@,System.String@)">
            <summary>
            解析方案信息
            </summary>
            <param name="SchemeJson"></param>
            <param name="schemes"></param>
            <param name="ErrInfo"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.Utility.ImageXmlToData">
            <summary>
            外观数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.ImageXmlToData.AnalysisImageData(System.String)">
            <summary>
            解析图像处理软件返回的数据
            key=表位ID，value=合格状态
            </summary>
            <param name="xmlData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.Utility.UpSysXmlToData">
            <summary>
            上位系统通讯数据组帧解析
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.UpSysXmlToData.ParsMeterXmlInfo(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}@,System.String@)">
            <summary>
            解析上位系统发送的表信息数据
            </summary>
            <param name="Meterxml"></param>
            <param name="MeterInfos"></param>
            <param name="ErrInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.UpSysXmlToData.GetErrorData(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.ImageProcessXmlData)">
            <summary>
            错误信息数据
            </summary>
            <param name="errData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase">
            <summary>
            XML转为数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.DataAnalysis(System.String)">
            <summary>
            解析数据
            </summary>
            <param name="XmlData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.ReturnData(System.String)">
            <summary>
            返回数据解析
            </summary>
            <param name="XmlData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.SetHeartBeatData(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData)">
            <summary>
            组心跳发送数据
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.VerificationProgress(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData)">
            <summary>
            检定进度
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.SecondaryCrimping(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData,System.Collections.Generic.List{System.Int32})">
            <summary>
            二次压接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.VerificationCompleted(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData)">
            <summary>
            检定完成
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.REST.Utility.SetOfDataBase.RecoveryMessage(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.ReturnData)">
            <summary>
            接收信息成功后回复报文
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
    </members>
</doc>
