<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.License</name>
    </assembly>
    <members>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.GetComputerId">
            <summary>
            获取计算机唯一识别码
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.GetDiskID">
            <summary>
            获取硬盘ID
            </summary>
            <returns></returns>
            
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.Identifier(System.String,System.String,System.String)">
            <summary>
            获取硬件标识符
            </summary>
            <param name="wmiClass"></param>
            <param name="wmiProperty"></param>
            <param name="wmiMustBeTrue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.Identifier(System.String,System.String)">
            <summary>
            获取硬件标识符
            </summary>
            <param name="wmiClass"></param>
            <param name="wmiProperty"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.UUID">
            <summary>
            获取UUID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.CpuId">
            <summary>
            获取CPUID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.BiosId">
            <summary>
            获取BIOSID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.DiskId">
            <summary>
            获取硬盘ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.BaseId">
            <summary>
            获取主板ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.VideoId">
            <summary>
            获取主视频控制器ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.ComputerAttributes.MacId">
            <summary>
            获取网卡ID
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.License.EmDataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.License.EmDataType.SeriaNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.License.EmDataType.FunctionItem">
            <summary>
            功能项
            </summary>
        </member>
        <member name="T:CLDC.CLAT.License.EmStorageType">
            <summary>
            存储类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.License.EmStorageType.Registry">
            <summary>
            注册表
            </summary>
        </member>
        <member name="F:CLDC.CLAT.License.EmStorageType.File">
            <summary>
            文件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.License.EmStorageType.DataBase">
            <summary>
            数据库
            </summary>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.EncryptStringAes(System.String)">
            <summary>
            AES加密
            </summary>
            <param name="plainText">明文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.EncryptStringAes(System.String,System.String)">
            <summary>
            AES加密
            </summary>
            <param name="plainText">明文</param>
            <param name="key">对称秘钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.DecryptStringFromBytesAes(System.String)">
            <summary>
            AES解密
            </summary>
            <param name="cipherText">密文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.DecryptStringFromBytesAes(System.String,System.String)">
            <summary>
            AES解密
            </summary>
            <param name="cipherText">密文</param>
            <param name="key">对称秘钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.WriteSeriaNumber(CLDC.CLAT.License.EmStorageType,System.String,System.String)">
            <summary>
            存储序列号
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <param name="seriaNumber">序列号</param>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.WriteFunctionItem(CLDC.CLAT.License.EmStorageType,System.String,System.String)">
            <summary>
            存储功能项
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <param name="functionItem">功能项</param>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadSeriaNumber(CLDC.CLAT.License.EmStorageType,System.String)">
            <summary>
            读取序列号
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadFunctionItem(CLDC.CLAT.License.EmStorageType,System.String)">
            <summary>
            读取功能项
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadDecryptSeriaNumber(CLDC.CLAT.License.EmStorageType,System.String)">
            <summary>
            读取已解密的序列号
            </summary>
            <param name="storageType">对称秘钥</param>
            <param name="path">存储路径</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadDecryptSeriaNumber(CLDC.CLAT.License.EmStorageType,System.String,System.String)">
            <summary>
            读取已解密的序列号
            </summary>
            <param name="storageType">对称秘钥</param>
            <param name="path">存储路径</param>
            <param name="key">对称秘钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadDecryptFunctionItem(CLDC.CLAT.License.EmStorageType,System.String)">
            <summary>
            读取已解密的功能项
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadDecryptFunctionItem(CLDC.CLAT.License.EmStorageType,System.String,System.String)">
            <summary>
            读取已解密的功能项
            </summary>
            <param name="storageType">存储方式</param>
            <param name="path">存储路径</param>
            <param name="key">对称秘钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.WriteIniFile(System.String,System.String,System.String,System.String)">
             <summary>
            设置ini文件
             </summary>
             <param name="section">节点</param>
             <param name="key">键</param>
             <param name="value">内容</param>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.ReadIniFile(System.String,System.String,System.String)">
            <summary>
            读取ini文件
            </summary>
            <param name="section">节点</param>
            <param name="key">键</param>
            <returns>值</returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.EncryptByRSA(System.String,System.String)">
            <summary>
            RSA加密
            </summary>
            <param name="plainText">明文</param>
            <param name="key">对称密钥/公钥</param>
            <returns>密文字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.DecryptByRSA(System.String)">
            <summary>
            RSA解密
            </summary>
            <param name="cipherText">密文</param>
            <param name="key">对称公钥/密钥</param>
            <returns>明文字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.DecryptByRSA(System.String,System.String)">
            <summary>
            RSA解密
            </summary>
            <param name="ciphertext">密文</param>
            <param name="key">对称公钥/密钥</param>
            <returns>明文字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.License.LicenseManager.GetDeviceSerialNumber(System.Int32,System.Collections.Generic.List{System.String}@)">
            <summary>
            获取设备序列号，为16进制数据
            </summary>
            <param name="size">序列号集合大小</param>
            <param name="deviceSerialNumberList">长度为2的序列号集合</param>
            <returns>0表示读，1表示写</returns>
        </member>
        <member name="M:CLDC.CLAT.License.PassWordEncryption.HashString(System.String)">
            <summary>  
            使用utf8编码将字符串散列  
            </summary>  
            <param name="sourceString">要散列的字符串</param>  
            <returns>散列后的字符串</returns>  
        </member>
        <member name="M:CLDC.CLAT.License.PassWordEncryption.HashString(System.Text.Encoding,System.String)">
            <summary>  
            使用指定的编码将字符串散列  
            </summary>  
            <param name="encode">编码</param>  
            <param name="sourceString">要散列的字符串</param>  
            <returns>散列后的字符串</returns>  
        </member>
        <member name="M:CLDC.CLAT.License.PassWordEncryption.Encryption(System.String)">
            <summary>
            加密后自定义排列结果
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.License.PassWordEncryption.restoreEncryption(System.String)">
            <summary>
            拆分数据库中密码，重组成正确顺序
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
