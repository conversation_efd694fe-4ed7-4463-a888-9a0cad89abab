<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.COMM.XMLWCF</name>
    </assembly>
    <members>
        <member name="T:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls">
            <summary>
            外部通讯类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.CloseComm">
            <summary>
            关闭对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.DealToShutDown">
            <summary>
            关闭窗体更新图标
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.GetMeterInfo(System.String,System.Int32)">
            <summary>
            手工获取表信息
            </summary>
            <param name="barCode">表条码</param>
            <param name="meterId">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.GetSchemeInfo(System.String,CLDC.CLAT.CLWBS.DataModel.Enum.EmLocalStationType)">
            <summary>
            手工获取方案信息
            </summary>
            <param name="taskId">任务编号</param>
            <param name="stationType">工位类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Bind_CarveInfo(System.String,System.String)">
            <summary>
            绑定表条码与雕刻二维码
            </summary>
            <param name="carveInfo"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.ApplyMeterInfoByMeterNo(System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            手动录入条码获取表信息和方案信息
            </summary>
            <param name="infos">表位号，表条码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.ApplySchemeBySchemeId(System.Int32,System.String)">
            <summary>
            手动获取方案
            </summary>
            <param name="SchemeId">方案编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.SendCheckFinish(CLDC.Framework.DataModel.Enum.EmPlugCardType,CLDC.Framework.DataModel.Enum.EmRunState)">
            <summary>
            外发检定完成
            </summary>
            <param name="CardType"></param>
            <param name="RunState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.ReSendTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml})">
            <summary>
            重新上传保存失败的实验数据 
            </summary>
            <param name="MeterAndItem">条码及实验项目</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_TrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            外发实验结果数据
            </summary>
            <param name="lstTrialData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_AlarmInfo(System.String,System.String,System.Int32,System.String)">
            <summary>
            外发警告信息
            </summary>
            <param name="code">日志编码</param>
            <param name="expandMsg">附加信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_StateInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            外发状态信息
            </summary>
            <param name="stationState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_TempHumInfo(System.String)">
            <summary>
            外发温湿度信息
            </summary>
            <param name="para">温湿度数据xml</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_ActionTypeInfo(CLDC.CLAT.CLWBS.DataModel.Enum.EmActionType)">
            <summary>
            外发动作信息
            </summary>
            <param name="emActionType">动作类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.Send_MoniterInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            外发监视器信息
            </summary>
            <param name="monitorData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.ReceivedStationState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.StationStateEventArgs)">
            <summary>
            接收台体状态信息
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.ReceivedDeviceState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.ControlRunEventArgs)">
            <summary>
            接收运行控制命令：暂停，恢复等
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.DataAsyncReceivedHandler(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.DataAsyncEventArgs)">
            <summary>
            异步接收数据
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessCls.CheckStartHandler(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.CheckStartEventArgs)">
            <summary>
            检定开始
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParsMeterXmlInfo(System.String,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}}@,System.String@)">
            <summary>
            从XML解析表信息到Dictionary
            </summary>
            <param name="Meterxml"></param>
            <param name="MeterInfos"></param>
            <param name="ErrInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParsSchemeXmlInfo(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.String@,CLDC.Framework.DataModel.Enum.EmMeterType)">
            <summary>
            解析方案信息
            </summary>
            <param name="Schemexml">方案</param>
            <param name="schemes">方案信息结构体</param>
            <param name="ParameterCheck_Info">参数验证信息</param>
            <param name="ErrInfo">错误信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParsSchemeXmlInfoCDZ(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.String@)">
            <summary>
            充电桩专用
            </summary>
            <param name="Schemexml"></param>
            <param name="schemes"></param>
            <param name="ErrInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParsFinishReturnXml(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml}@)">
            <summary>
            解析执行结果返回信息
            </summary>
            <param name="xmldata"></param>
            <param name="errInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParsReturnXml(System.String,System.String@)">
            <summary>
            解析执行结果返回信息
            </summary>
            <param name="xmldata"></param>
            <param name="errInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.ParseRetResultData(System.String)">
            <summary>
            解析上传结果集后返回保存成功的数据
            </summary>
            <param name="RetXmlData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateFailedMeterInfoXml(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Int32,System.String)">
            <summary>
            创建上传检定不合格表位信息XML
            </summary>
            <param name="FaildMeter"></param>
            <param name="TrialType"></param>
            <param name="TrialName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateStateInfoXml(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            生成状态信息XML格式
            </summary>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateMoniterXml(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            生成标准监视信息XML格式
            </summary>
            <param name="state"></param>
            <returns>XML</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateErrInfoXml(System.String,System.String,System.Int32,System.String)">
            <summary>
            生成状态信息XML格式
            </summary>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateReturnXml(System.Boolean,System.String)">
            <summary>
            创建返回xml
            </summary>
            <param name="flag"></param>
            <param name="err"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.DataConvertHelperCls.CreateReturnXml(System.Boolean,System.String,System.String,System.String)">
            <summary>
            传检返回xml
            </summary>
            <param name="flag">成功标识</param>
            <param name="err">错误信息</param>
            <param name="stationId">工位id</param>
            <param name="state">状态1空闲，2忙碌，3隔离，4禁止停止</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan">
            <summary>
            外部通讯类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.CloseComm">
            <summary>
            关闭对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.DealToShutDown">
            <summary>
            关闭窗体更新图标
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.GetMeterInfo(System.String,System.Int32)">
            <summary>
            手工获取表信息
            </summary>
            <param name="barCode">表条码</param>
            <param name="meterId">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.GetSchemeInfo(System.String,CLDC.CLAT.CLWBS.DataModel.Enum.EmLocalStationType)">
            <summary>
            手工获取方案信息
            </summary>
            <param name="taskId">任务编号</param>
            <param name="stationType">工位类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Bind_CarveInfo(System.String,System.String)">
            <summary>
            绑定表条码与雕刻二维码
            </summary>
            <param name="carveInfo"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.ApplyMeterInfoByMeterNo(System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            手动录入条码获取表信息和方案信息
            </summary>
            <param name="infos">表位号，表条码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.ApplySchemeBySchemeId(System.Int32,System.String)">
            <summary>
            手动获取方案
            </summary>
            <param name="SchemeId">方案编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.SendCheckFinish(CLDC.Framework.DataModel.Enum.EmPlugCardType,CLDC.Framework.DataModel.Enum.EmRunState)">
            <summary>
            外发检定完成
            </summary>
            <param name="CardType"></param>
            <param name="RunState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.ReSendTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml})">
            <summary>
            重新上传保存失败的实验数据 
            </summary>
            <param name="MeterAndItem">条码及实验项目</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_TrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            外发实验结果数据
            </summary>
            <param name="lstTrialData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_AlarmInfo(System.String,System.String,System.Int32,System.String)">
            <summary>
            外发警告信息
            </summary>
            <param name="code">日志编码</param>
            <param name="expandMsg">附加信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_StateInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            外发状态信息
            </summary>
            <param name="stationState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_TempHumInfo(System.String)">
            <summary>
            外发温湿度信息
            </summary>
            <param name="para">温湿度数据xml</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_ActionTypeInfo(CLDC.CLAT.CLWBS.DataModel.Enum.EmActionType)">
            <summary>
            外发动作信息
            </summary>
            <param name="emActionType">动作类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.Send_MoniterInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            外发监视器信息
            </summary>
            <param name="monitorData"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.ReceivedStationState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.StationStateEventArgs)">
            <summary>
            接收台体状态信息
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.ReceivedDeviceState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.ControlRunEventArgs)">
            <summary>
            接收运行控制命令：暂停，恢复等
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.DataAsyncReceivedHandler(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.DataAsyncEventArgs)">
            <summary>
            异步接收数据
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsFoShan.CheckStartHandler(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.CheckStartEventArgs)">
            <summary>
            检定开始
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi">
            <summary>
            
            B模式，数据上传、方案获取直接从IDS（WCF）
             </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="index"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.CloseComm">
            <summary>
            关闭对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.InitOPCComm">
            <summary>
            初始化OPCUA连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendPlcHeartbeat">
            <summary>
            发送PLC心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.InitComm">
            <summary>
            初始通信
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ReceivedDeviceState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.ControlRunEventArgs)">
            <summary>
            接收运行控制命令：暂停，恢复等
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ReceivedStationState(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.StationStateEventArgs)">
            <summary>
            接收台体状态信息
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.DataAsyncReceivedHandler(System.Object,CLDC.CLAT.Comm.InternalCommClient.ClientEventArgs.DataAsyncEventArgs)">
            <summary>
            异步接收数据
            </summary>
            <param name="sender"></param>
            <param name="args"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.QuerySoftwareVersion">
            <summary>
            查询软件版本
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.UpdateSoftware(System.String)">
            <summary>
            更新软件
            </summary>
            <param name="fileName">更新包名称</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ChipIDAuthentication(CLDC.CLAT.CLWBS.DataModel.Struct.ChipIDAuthentication)">
            <summary>
            芯片ID认证
            </summary>
            <param name="chipID"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ApplyMeterInfo(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接收：获取被检品信息，指令流向：PLC->IBS
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.GetAppearanceResult">
            <summary>
            获取外观结果
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.GetPLCGetMeterInfoAsk(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            收到PLC发送过来的获取被检品信息指令，数据流向 PLC->IBS
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ApplySchemeInfo(System.String,System.String@)">
            <summary>
            申请方案信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ApplyMeterInfoByMeterNo(System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            手动录入条码获取表信息和方案信息
            </summary>
            <param name="infos">表位号，表条码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.CalculateMeterId(System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}})">
            <summary>
            江西项目表位号由MES计算
            </summary>
            <param name="dicMeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.GetCarveData(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            监听OPCUA节点变化，获取雕刻数据
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.GetCarveVerifyData(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            监听OPCUA节点变化，获取雕刻验证数据
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendUpLoadEquipEleLabel(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.UploadEquipCarveReqArg})">
            <summary>
            上传雕刻验证数据
            </summary>
            <param name="uploadEquipCarveReqArgs">雕刻验证数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传实验结果数据（分项结论）
            </summary>
            <param name="lstTrialData">试验数据集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendPlanBModeTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传B模式（历史表）实验结果数据（分项结论）
            </summary>
            <param name="lstTrialData">试验数据集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendComprehensiveTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传实验结果数据（综合结论）
            </summary>
            <param name="lstMeterInfo">表信息集合</param>
            <param name="lstTrialScheme">方案信息集合</param>
            <param name="lstTrialData">试验数据集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ReSendTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StReslultDataXml})">
            <summary>
            PLAN B重新上传保存失败的实验数据 
            </summary>
            <param name="MeterAndItem">条码及实验项目</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.FileUpload(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            上传CCD图片
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.CheckStartReceived(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接收PLC开始检定
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ConnectionCompleted(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接驳完成
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.MoveMeterPosition(System.Int32)">
            <summary>
            移动表位
            </summary>
            <param name="meterId"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.AnewCheckStartReceived(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接收PLC重新检定
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.SendCheckFinish(CLDC.Framework.DataModel.Enum.EmPlugCardType,CLDC.Framework.DataModel.Enum.EmRunState)">
            <summary>
            发送检定完成，数据流向：IBS->PLC->SCADA->MES
            上传检测试验数据到MES后，再发送检定完成的信号给到PLC
            </summary>
            <param name="CardType"></param>
            <param name="RunState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.Send_StateInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            外发状态信息
            </summary>
            <param name="stationState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ClearAlarmInfo">
            <summary>
            清除告警信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.Send_AlarmInfo(System.String,System.String,System.Int32,System.String)">
            <summary>
            外发警告信息
            </summary>
            <param name="code">日志编码</param>
            <param name="expandMsg">附加信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.FindNum(System.Int64,System.Int32,System.Int32)">
            <summary>
            报警编码
            </summary>
            <param name="logCode"></param>
            <param name="startIndex"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.UploadEnvironmentTempHum(System.String)">
            <summary>
            环境温湿度仪写入OPCUA数据
            </summary>
            <param name="datastr">设备编号|设备类型|温度数据|湿度数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.Send_MoniterInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData)">
            <summary>
            发送标准表监视器
            TODO:标准表温湿度不在此结构中
            </summary>
            <param name="monitorData"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.UploadLocation(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接收上传表位状态
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.GetMachineInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.MachineInfo}@,System.String@)">
            <summary>
            获取专机信息
            </summary>
            <param name="lstMachineInfo">专机信息集合</param>
            <param name="strErrInfo">错误信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ControlRunReceived(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            接收PLC运行控制
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.StarTest(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            一键启停，开始检定
            MES->PLC->IBS
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.StopTest(CLDC.CLAT.CLWBS.DataModel.Class.PLCNode.PLCSubsripts)">
            <summary>
            一键启停，停止
            </summary>
            <param name="subsripts"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.Receive(System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            接收PLC回调信息
            </summary>
            <param name="addressValue">nodeid,value</param>
            <param name="groupName">组名</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ReadOPCValue(System.Object)">
            <summary>
            读取PLC节点数据
            </summary>
            <param name="pLCs"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ReadPlcCheckMode">
            <summary>
            读检定模式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.BackDataBase">
            <summary>
            数据库备份
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.ConnectServer">
            <summary>
            连接内部通信
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.CheckConnected">
            <summary>
            检查IDS是否连接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.LoadOpcuaConfig">
            <summary>
            加载OPCUA配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.CreateLotEquipmentRealAddress(System.Xml.Linq.XElement,System.String,System.Int32)">
            <summary>
            XML格式数据批量自动转换为OPC硬件上传模型
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsJiangXi.TransXmlToRealAddress(System.String,System.String,System.Int32)">
            <summary>
            XML格式数据转换为OPC格式地址
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan">
            <summary>
            四川外部通讯使用
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan._LocalSocket">
            <summary>
            平台分控系统-TCP客户端
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.RemoteEndPoint">
            <summary>
            平台分控系统IP地址
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.IsConnected">
            <summary>
            是否连接上分控系统
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StrReceiveData">
            <summary>
            分控系统返回的数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StationId">
            <summary>
            检定工位号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.isConnectedIDS">
            <summary>
            IDS是否连接
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.Client">
            <summary>
            IDS服务客户端
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ImageSocket">
            <summary>
            与图像处理软件(服务端)连接-IBS为TCP客户端，图像处理软件=服务端
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ImageIPEndPoint">
            <summary>
            图像处理软件的IP地址
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.IsImagesConnected">
            <summary>
            图像处理软件是否连接
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.RecvImageDataBuffer">
            <summary>
            接收图像处理软件数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StrImageReceiveData">
            <summary>
            接收图像处理软件返回的数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.UpSYSSocket">
            <summary>
            上位系统
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.LocalUpSysIPEndPoint">
            <summary>
            创建TCP服务端的IP端口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.RemoteUpSysIPEndPoint">
            <summary>
            上位系统TCP客户端
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.IsUpSysConnected">
            <summary>
            上位系统是否连接
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.LstClientInfo">
            <summary>
            客户端连接列表
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.RecvUpSysDataBuffer">
            <summary>
            接收上位系统数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StrUpSysReceiveData">
            <summary>
            接收上位系统返回的数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan._SyncLock">
            <summary>
            同步_锁
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ConnectSocketServer">
            <summary>
            平台通讯连接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ConnectIDSServer">
            <summary>
            IDS服务连接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.CheckConnected">
            <summary>
            检查IDS是否连接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.CloseComm">
            <summary>
            关闭对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.VerificationProgress(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData)">
            <summary>
            发送检定进度
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SecondaryCrimping(System.Collections.Generic.List{System.Int32})">
            <summary>
            二次压接
            </summary>
            <param name="mesterID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.VerificationCompleted">
            <summary>
            检定完成
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SetOfData(CLDC.CLAT.CLWBS.DataModel.Enum.EmTaskName,CLDC.CLAT.CLWBS.DataModel.Class.XMLData.BaseXmlData,System.Collections.Generic.List{System.Int32})">
            <summary>
            组数据
            </summary>
            <param name="emTask"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendHeartBeat">
            <summary>
            发送心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.AssignmentModel">
            <summary>
            赋值模型
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ProcessingReturnData">
            <summary>
            等待数据返回
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ClearReceiveData">
            <summary>
            清除返回数据变量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ReadCon">
            <summary>
            获取帧序列号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendData(System.Byte[])">
            <summary>
            发送数据
            </summary>
            <param name="SendBuff"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendDataEnd(System.IAsyncResult)">
            <summary>
            数据发送完成处理函数
            </summary>
            <param name="iar"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ReceiveData(System.IAsyncResult)">
            <summary>
            接收返回的数据
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.FunctionSelection(System.String)">
            <summary>
            功能选择，平台-->检定工位软件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StartIBSThread">
            <summary>
            系统复位
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StopTask(System.Boolean)">
            <summary>
            停止检定
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StartCheckRequestItem(System.String)">
            <summary>
            检测指定点
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SocketClose">
            <summary>
            关闭socket连接
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.StartCheckBusiness">
            <summary>
            开始检测
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ApplySchemeBySchemeId(System.Int32,System.String)">
            <summary>
            手动申请方案
            </summary>
            <param name="ApplyType">类型：任务号；方案号</param>
            <param name="SchemeId">编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.InitComm">
            <summary>
            初始化
            </summary>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.Send_TrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            上传实验结果数据（分项结论）
            </summary>
            <param name="lstTrialData">试验数据集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.Send_ComprehensiveTrialDatas(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClTrialData_Local})">
            <summary>
            发送综合结论数据
            </summary>
            <param name="lstMeterInfo"></param>
            <param name="lstTrialScheme"></param>
            <param name="lstTrialData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.Send_StateInfo(CLDC.CLAT.CLWBS.DataModel.Struct.StStationState)">
            <summary>
            发送检定进度
            </summary>
            <param name="stationState"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.Send_CheckFinish(CLDC.Framework.DataModel.Enum.EmPlugCardType,CLDC.Framework.DataModel.Enum.EmRunState)">
            <summary>
            外发检定完成
            </summary>
            <param name="CardType"></param>
            <param name="RunState"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ConnectImagesServer">
            <summary>
            IBS作为外观控制软件，IBS=TCP客户端，图像处理软件=TCP服务端
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ReceiveImageData(System.IAsyncResult)">
            <summary>
            接收图像处理软件数据
            </summary>
            <param name="iar"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendMeterInfoToImage">
            <summary>
            转发上位系统的表信息数据到图像处理软件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendImageData(System.String)">
            <summary>
            发送数据到图像处理软件
            </summary>
            <param name="sendStr"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ProcessImageReturnData">
            <summary>
            图像处理软件是否有数据返回
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.GetImageDatas(System.String)">
            <summary>
            获取图像处理软件的数据，转到业务和界面
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.CreatUpSysTCPServer">
            <summary>
            创建TCP服务
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.AcceptConn(System.IAsyncResult)">
            <summary>
            客户端连接处理函数
            </summary>
            <param name="iar">欲建立服务器连接的Socket对象</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.ReceiveUpSysData(System.IAsyncResult)">
            <summary>
            接受数据完成处理函数，异步的特性就体现在这个函数中
            </summary>
            <param name="iar">目标客户端Socket</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendImageUpSysData(System.String)">
            <summary>
            转发 图像处理软件后数据给上位系统
            </summary>
            <param name="sendStr"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.SendErrorData(System.String)">
            <summary>
            发送错误提示反馈给上位系统
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.GetHeardData(CLDC.CLAT.CLWBS.DataModel.Class.XMLData.ImageProcessXmlData,System.String)">
            <summary>
            上位系统通讯帧组包头格式
            </summary>
            <param name="data"></param>
            <param name="strCode"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.UpSysSendData(System.Byte[])">
            <summary>
            TCP服务端发送数据
            </summary>
            <param name="SendBuff"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.FunUpSysData(System.String,System.String@)">
            <summary>
            接收上位系统的数据
            </summary>
            <param name="strData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.ExternalCommProcessClsSiChuan.CheckMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.String@)">
            <summary>
            检验上位系统发过来的表信息中必须要有的数据，四川单相-IBS与上位系统
            </summary>
            <param name="LstMeter"></param>
            <param name="strErrMsg"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.SYSTEMTIME.FromDateTime(System.DateTime)">
            <summary>
            从System.DateTime转换。
            </summary>
            <param name="time">System.DateTime类型的时间。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.SYSTEMTIME.ToDateTime">
            <summary>
            转换为System.DateTime类型。
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.COMM.XMLWCF.SYSTEMTIME.ToDateTime(CLDC.CLAT.CLWBS.COMM.XMLWCF.SYSTEMTIME)">
            <summary>
            静态方法。转换为System.DateTime类型。
            </summary>
            <param name="time">SYSTEMTIME类型的时间。</param>
            <returns></returns>
        </member>
    </members>
</doc>
