<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.EquipMent</name>
    </assembly>
    <members>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.RevEquipMentData(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.GetIRTextResult(System.String@,System.Single@)">
            <summary>
            获取绝缘电阻测试结果
            </summary>
            <param name="ResultStatus">结论状态字</param>
            <param name="TextValue">绝缘电阻测试值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.StartIRText">
            <summary>
            启动绝缘电阻测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.StopIRText">
            <summary>
            停止绝缘电阻测试
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.SetIRTextMode">
            <summary>
            设置绝缘仪IR测试方式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.SetIRTextPRM(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置绝缘仪IR测试参数
            </summary>
            <param name="TextVoltage">测试电压</param>
            <param name="MinLimit">电阻下限</param>
            <param name="MaxLimit">电阻上限</param>
            <param name="TestTime">测试时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.ReadIRTextPRM(System.Int32@,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            读取绝缘仪IR测试参数
            </summary>
            <param name="TextVoltage">测试电压</param>
            <param name="MinLimit">电阻下限</param>
            <param name="MaxLimit">电阻上限</param>
            <param name="TestTime">测试时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.ParseIRResult(System.Byte[],System.String@,System.Single@)">
            <summary>
            解析绝缘电阻测试结果
            </summary>
            <param name="RevData">绝缘仪返回报文</param>
            <param name="ResultStatus">结论状态字</param>
            <param name="TextValue">绝缘电阻测试值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.ParseIRTextPRM(System.Byte[],System.Int32@,System.Int32@,System.Int32@,System.Int32@)">
            <summary>
            解析绝缘仪IR测试参数
            </summary>
            <param name="RevData"></param>
            <param name="TextVoltage"></param>
            <param name="MinLimit"></param>
            <param name="MaxLimit"></param>
            <param name="TestTime"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.AN9632.ParseResult(System.Byte[])">
            <summary>
            解析操作绝缘仪操作应答
            </summary>
            <param name="RevData"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve">
            <summary>
            雕刻专机 Hanslaser 协议设备
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.initTemplateKey">
            <summary>
            初始化模板key
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.dataKey">
            <summary>
            数据传输key
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.markStartKey">
            <summary>
            启动打标
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.DataBuf">
            <summary>
            接收数据
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.SynLock">
            <summary>
            全局锁
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.RevEquipMentData">
            <summary>
            等待数据返回
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.CarveInitTemplate(System.String)">
            <summary>
            初始化模板
            </summary>
            <param name="templateName"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.CarveSenData(System.String)">
            <summary>
            数据传输
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Carve.CarveMarkStart">
            <summary>
            开始打标
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CCDCamera.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CCDCamera.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CCDCamera.ReadCCDResult(System.String,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StCCDResult}@,System.String@)">
            <summary>
            向外观软件发送xml数据，并读取返回结果
            </summary>
            <param name="meterInfoXml"></param>
            <param name="lstResult"></param>
            <param name="errorStr"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CCDCamera.GetFlipScreenResult(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StCCDResult}@,System.String@)">
            <summary>
            获取翻屏结果(CCD)
            </summary>
            <param name="dicResult">key：表位号、value：翻屏结果</param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L">
            <summary>
            误差板
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.waitSend">
            <summary>
            等待发送数据锁
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.RevEquipMentDataPowerConsumption">
            <summary>
            功耗实验
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.CheckFrame(System.Byte[],System.Int32@)">
            <summary>
            功耗实验数据帧校验
            </summary>
            <param name="data"></param>
            <param name="len">数据帧总长度</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.AbortAllFunction(System.Int32[],System.Byte,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            停止或者开始服务板相应功能
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="actTag">动作类型[0-开始计算 1-停止计算]</param>
            <param name="chkType">检定类型</param>
            <param name="channel">通道号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetErrPanelParam(System.Int32[],System.UInt32,System.Single,System.UInt32,System.UInt32,System.Byte,System.Int32)">
            <summary>
            设置误差检定参数
            </summary>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetClockParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置日计时误差参数
            </summary>
            <param name="sClkFreq">标准时钟频率(单位: KHZ; 500KHZ)</param>
            <param name="cClkFreq">被检时钟频率(单位: HZ; 100HZ)</param>
            <param name="pClkCount">被检脉冲个数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetDemandParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置需量误差检定参数（CL188L）
            </summary>
            <param name="sClkFreq">标准时钟频率（单位：Hz）</param>
            <param name="cClkFreq">时间周期（单位：ms）</param>
            <param name="pClkCount">被检脉冲个数</param>
            <param name="channel">通道号</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetPulseChannel(System.Int32[],CLDC.Framework.DataModel.Enum.EmPulseChannel,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置误差板脉冲通道
            </summary>
            <param name="plsChannel">被检脉冲通道[0-P+,1-P-,2-Q+,3-Q-,4-日计时,5-需量脉冲]</param>
            <param name="plsType">脉冲输入类型[0-电子式脉冲输入 1-感应式脉冲输入]</param>
            <param name="polar">共阴或共阳(0共阴,1共阳)</param>
            <param name="chkType">检定类型[0-电能误差 1-走字计数 2-预付费功能检定 3-对标 4-日计时误差 5-需量误差]</param>
            <param name="pulseSpeendType">脉冲类型[0-低速脉冲（普通电能表输出） 1-高速脉冲（标准电能表输出)]</param>
            <param name="countType">计数类型[0-脉冲计数 1-脉冲间隔时间]</param>
            <param name="photoelectricity">光电类型[0-电脉冲 1-光脉冲]</param>
            <param name="channel">通道号</param>
            <param name="divisionFactor">分频系数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetCommMode(System.Int32,System.Int32[])">
            <summary>
            设置通讯模式
            </summary>
            <param name="mode">
            通讯模式[0-一对一模式485通讯 1-奇数表位485通讯 2-偶数表位485通讯 3-一对一模式红外通讯 
                     4-奇数表位红外通讯  5-偶数表位红外通讯 6-切换到485总线] 
            </param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetCommModetd(System.Int32,System.Int32[],System.String)">
            <summary>
            设置CommMode
            </summary>
            <param name="mode"></param>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.ClearAllFaultState(System.Int32[],System.Byte)">
            <summary>
            清除所有表位故障状态
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="faultType">故障类型[1-接线故障状态 2-预付费跳闸状态 3-报警信号状态]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.CheckErrMeterState(System.Int32[],System.Byte,System.Collections.Generic.Dictionary{System.Int32,System.Int32}@)">
            <summary>
            查询误差板当前误差及当前状态指令
            </summary>
            <param name="meterIds"></param>
            <param name="faultType">误差类型包括：电能误差（00）、需量周期误差（01）、日计时误差（02）、脉冲个数（03）、对标状态（04）</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.InsulateFaultMeters(System.Int32[],System.Int32)">
            <summary>
            隔离故障表位电压电流
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="ctlType">控制类型[0-隔离解除 1-隔离]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetCTtypeMeters(System.Int32[],System.Int32)">
            <summary>
            通过误差板控制设置CT档位
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="ctType">档位类型[档位类型0x01=100A 0x02=2A]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetEquiqMode(System.Int32[],System.Byte,System.Byte,System.Byte)">
            <summary>
            设误差板台体设备选择
            </summary>
            <param name="meterIds">表位号</param>
            <param name="MeterType">电能表类型 0表示为国网电能表，1表示南网电能表</param>
            <param name="MeterMode">0表示单项电能表,1表示三相电能表</param>
            <param name="CtType">0x00表示不带CT，0x01表示2030-3C,0x02表示2030-3B, 0x03表示2030-3A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetUAndIOutputLoop(System.Byte,System.Byte,System.Int32[])">
            <summary>
            选择电压和电流输出回路（CL188L）
            </summary>
            <param name="uLoop">电压回路[0-直接接入式 1-互感器接入式 2-表位无电表接入]</param>
            <param name="iLoop">电流回路[0-第一回路 1-第二回路]</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetCommDoor(System.Byte,System.Byte,System.Int32[])">
            <summary>
            设置通讯通道选择
            </summary>
            <param name="oneDoorType">1路485[0-485 1-蓝牙通讯 2-232通讯]</param>
            <param name="twoDoorType">2路485[0-485 1-蓝牙通讯 2-232通讯]</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetMorMeterTenErrValues(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@,System.Int32@,System.Int32,System.Int32,System.Int32)">
            <summary>
            获取多块表10次误差值
            </summary>
            <param name="clChkParam"></param>
            <param name="chkType">误差类型</param>
            <param name="readType">“清远期间核查”误差读取类型 >=1表示读取误差板获取的脉冲数 </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetMeterMoreErrValues(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@,System.Int32@,System.Int32,System.Int32)">
            <summary>
            获取误差板多次误差数据
            实际的有效误差次数和脉冲个数
            </summary>
            <param name="MeterIds">表位ID</param>
            <param name="chkType">检测类型</param>
            <param name="DitpanelInfo">误差板信息</param>
            <param name="DitLstData">数据列表</param>
            <param name="normalizingPulse">脉冲数</param>
            <param name="channel">通道</param>
            <param name="readType">“清远期间核查”误差读取类型 >=1表示读取误差板获取的脉冲数 </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.QueryTenErrorPower(System.Collections.Generic.List{System.Int32},System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            获取误差板功耗参数
            </summary> 
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.CheckErrorPulse(System.Int32[])">
            <summary>
            查询电能误差检定时脉冲参数命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.CheckClockFreAndAmountTime(System.Int32[])">
            <summary>
            查询日计时检定时钟频率及需量周期检定时间
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.SetRelayOverLoadTime(System.Int32[],System.Byte)">
            <summary>
            设置隔离继电器过载动作可靠性检测时间
            </summary>
            <param name="meterIds"></param>
            <param name="byMin">检测时间(s)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetRelayOverLoadTime(System.Int32[])">
            <summary>
            读取隔离继电器过载动作可靠性检测时间
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetErrorCurrent(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@)">
            <summary>
            扩展查询误差板当前误差命令
            </summary>
            <param name="meterIds"></param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetReadPrintInfo(System.Int32[])">
            <summary>
            读取打印信息命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetCurrentVersion(System.Int32[])">
            <summary>
            查询当前版本命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetVoltageCurr(System.Int32[])">
            <summary>
            查询表位电压电流隔离命令--
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetQueryLoop(System.Int32[])">
            <summary>
            查询双回路命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetQueryPulseComm(System.Int32[])">
            <summary>
            查询被检脉冲通道及检定类型命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetDeviceInfoRead(System.Int32[])">
            <summary>
            检定装置信息读取命令
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetQueryRelayStatus(System.Int32[],System.Byte)">
            <summary>
            查询继电器状态命令
            </summary>
            <param name="meterIds">继电器Id</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.HexStrToBytsDataA(System.String,System.Int32)">
            <summary>
            16进制字符串转字节数组
                【"123" => {0x01,0x23}】
            </summary>
            <param name="strData">16进制字符串</param>
            <param name="aryLen">数组长度</param>
            <returns>字节数组</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.AdjustmentStrSize(System.String,System.Int32,System.Boolean)">
            <summary>
            调整字符串大小
            </summary>
            <param name="origStr">待调字符串</param>
            <param name="desiredSize">调整的大小</param>
            <param name="isForward">是否前移</param>
            <returns>调整后的字符串</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL188L.GetPanelCFigure(System.Int32[],System.Int32)">
            <summary>
            误差板控制字处理
            </summary>
            <param name="meterIDs">操作表位集合</param>
            <param name="trayCount"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL191B.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL191B.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL191B.SelectCtnPulse(System.Byte)">
            <summary>
            191B设置脉冲类型
            </summary>
            <param name="pulseType">脉冲类型[0-标准电能脉冲 1-标准时钟脉冲]</param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.CtRelayControl(System.Boolean)">
            <summary>
            顺德CT切换继电器切换蓝牙通道
            </summary>
            <param name="actTag">动作类型[0-开始 1-停止]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.PowerOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Boolean)">
            <summary>
            关源
            </summary>
            <param name="sourceParam">关源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.AllowPowerOn">
            <summary>
            释放关源后不能升源的信号量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.SetLightColor(CLDC.Framework.DataModel.Enum.EmLightColor)">
            <summary>
            设置指示灯颜色
            </summary>
            <param name="color">指示灯颜色</param>
            <returns>true-成功,false-失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.DLRelayControl(System.Byte)">
            <summary>
            切换大电流
            </summary>
            <param name="linkType">直接或互感</param>
            <returns>true-成功,false-失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.ChangUbRelayControl(System.Byte)">
            <summary>
            单相表切换B相电压
            </summary>
            <param name="linkType"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B.CarrierRelayControl(System.Byte)">
            <summary>
            做载波时,切换2041与功率源和表同回路
            </summary>
            <param name="linkType">直接或互感</param>
            <returns>true-成功,false-失败</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B_1.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B_1.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B_1.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029B_1.DYRelayControl(System.Byte)">
            <summary>
            CT电源控制，直接接入式接通15V电源。互感切断CT电源
            </summary>
            <param name="linkType">直接或互感</param>
            <returns>true-成功,false-失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029C.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029C.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029C.OpenLED">
            <summary>
            开LED灯
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029C.CloseLED">
            <summary>
            关LED灯
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029F.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029F.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2029F.PowerOnOrShutDown(System.Byte)">
            <summary>
            远程停上电
            </summary>
            <param name="Type">01：上电，00：断电</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.ClearOverLoaded">
            <summary>
            清除过载信号
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.SelectOverLoaded(System.Byte@)">
            <summary>
            查询信号是否过载
            </summary>
            <param name="type">0x00代表没过载，0x01代表过载</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.SetCtType(System.Byte)">
            <summary>
            控制CT档位
            </summary>
            <param name="type">0x00代表100A，0x01代表2A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.SelectCtType(System.Byte@)">
            <summary>
            查询CT档位
            </summary>
            <param name="type">0x64代表100A，0x02代表2A</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.ControlCT(System.Byte[])">
            <summary>
            CT继电器控制
            </summary>
            <param name="controls">CT继电器选择断开和闭合，
            长度为8,
            第一位为0代表A相断开，为1代表A相闭合，
            第二位为0代表B相断开，为1代表B相闭合，
            第三位为0代表C相断开，为1代表C相闭合，
            第五位为0代表A相不被控制，为1代表控制A相，
            第六位为0代表B相不被控制，为1代表控制B相，
            第七位为0代表C相不被控制，为1代表控制C相，</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2030.ControlCTWireMode(System.Byte)">
            <summary>
            CT继电器控制
            </summary>
            <param name="type">0x33代表三相三线,0x34代表三线四线</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2031B.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2031B.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2031B.GetLostPower(System.Int32,System.Byte,System.Single@,System.Single@,System.Single@)">
            <summary>
            读功耗
            </summary>
            <param name="type">相位，1=电压A相，2=电流A相,3=电压B相，4=电流B相，5=电压C相，6=电流C相</param>
            <param name="Rxid">功耗板地址</param>
            <param name="flt_P">有功</param>
            <param name="flt_Q">无功</param>
            <param name="flt_S">视在</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035">
            <summary>
            红外
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.RevEquipMentData376">
            <summary>
            南方电网
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.RevEquipMentData_376">
            <summary>
            国网/云网
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.ZDSubData(System.Int32,System.Byte[])">
            <summary>
            截取终端完整报文
            </summary>
            <param name="startNum">开始字符</param>
            <param name="buffdata">报文</param>
            <returns>截取后报文</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.NWZDSubData(System.Int32,System.Byte[])">
            <summary>
            用于南网终端协议
            </summary>
            <param name="startNum"></param>
            <param name="buffdata"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.InfraredRayGetMeterEnergy(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            红外方式读取电量
            </summary>
            <param name="meterAddrs">电表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="powerType">做功类型</param>
            <param name="LstEng">返回电量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.PackParameters(CLDC.CLAT.CLWBS.DataModel.Enum.Em698.EmSecurityMode,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            698协议组参
            </summary>
            <param name="mode"></param>
            <param name="MeterAddress"></param>
            <param name="Ods"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2035.ReadMeterData(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            红外方式读取任意数据
            </summary>
            <param name="meterAddrs">电表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="dataCode">操作者</param>
            <param name="ReadData">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.GetPressFreq(System.Single@)">
            <summary>
            获取频率
            </summary>
            <param name="sfFreq">频率（单位：Hz）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.GetPressIValue(System.Single@)">
            <summary>
            获取电流值
            </summary>
            <param name="sfIValue">电流值（单位：mA）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.GetPressUValue(System.Single@)">
            <summary>
            获取电压值
            </summary>
            <param name="sfUValue">电压值（单位：V）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.GetPressState(System.Byte@)">
            <summary>
            获取耐压状态
            </summary>
            <param name="state">状态[0-正常 1-超过阈值]</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.SetPressILimen(System.Single)">
            <summary>
            设置电流阈值
            </summary>
            <param name="sfLimen">阈值（单位：mA；范围：1.0mA-120.0mA）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.SetPressUValue(System.Single)">
            <summary>
            设置电压值
            </summary>
            <param name="sfUValue">电压值（单位：V；范围：220.0V-6000.0V）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.SetWorkTime(System.Int32)">
            <summary>
            设置工作时间
            </summary>
            <param name="workTime">工作时间（单位：s；范围：1s-999s）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.CL2038_ControlWork(System.Byte)">
            <summary>
            控制耐压工作
            </summary>
            <param name="cellID">单元编号</param>
            <param name="actTag">动作类型[0-关源 1-升源]</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2038.ResetPressState">
            <summary>
            重置工作状态
            </summary>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.RevEquipMentData">
            <summary>
            操作等待时间
            </summary> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetCommunicationMode(System.Int32)">
            <summary>
            设置载波通讯方式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.CheckFrame(System.Byte[])">
            <summary>
            检定是否符合376.2协议
            </summary>
            <param name="dataBuff">数据缓存</param>
            <returns>符合则返回帧起始下标；否则返回错误编码</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SubStringBuff(System.Byte[],System.Int32)">
            <summary>
            截取帧长
            </summary>
            <param name="dataBuff">源数据</param>
            <param name="startNum">帧头位置</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadWirelessSpectrum">
            <summary>
            读取无线通信频段
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetWirelessSpectrum(System.Int32,System.Int32)">
            <summary>
            设置无线通信频段
            </summary>
            <param name="modulationMethod">无线调制方式</param>
            <param name="channelNumber">无线信道编号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadNetworkType">
            <summary>
            读取双模组网方式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetNetworkType(CLDC.CLAT.CLWBS.DataModel.Enum.EmNetworkType)">
            <summary>
            设置双模组网方式
            </summary>
            <param name="networkType"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.CarrierEquipInit">
            <summary>
            硬件初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.CarrierParamInit">
            <summary>
            载波参数区初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.CarrierDataInit">
            <summary>
            载波数据区初始化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadCarrierAddress">
            <summary>
            读取载波主节点地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetPrimaryNode(System.String)">
            <summary>
            设置主节点地址
            </summary>
            <param name="address"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetCarrierRoute(System.String,System.Int32)">
            <summary>
            载波路由设置
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetCarrierRoute(System.Collections.Generic.List{System.String})">
            <summary>
            表地址集合
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetCarrierRoute(System.String[],System.Int32)">
            <summary>
            载波路由设置
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SendDataRoute(System.Byte[],System.Byte[]@)">
            <summary>
            路由转发数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SendDataDirect(System.Byte[],System.Byte[]@)">
            <summary>
            直接转发数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.PackParameters(CLDC.CLAT.CLWBS.DataModel.Enum.Em698.EmSecurityMode,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            698协议组参
            </summary>
            <param name="mode"></param>
            <param name="MeterAddress"></param>
            <param name="Ods"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.GetMeterData(System.String,System.String)">
             <summary>
             抄电表数据组帧
             </summary>
             <param name="MeterAddress">表地址</param>
            <param name="oad">数据标识</param>
             <returns>组帧</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseMeterData(System.Byte[],System.String@)">
            <summary>
            解析载波抄表数据
            </summary>
            <param name="RevData"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.GetMeterEng(System.String)">
            <summary>
            操表电量帧
            </summary>
            <param name="MeterAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SendCarrierFrameDirect(System.Byte[],System.Byte[]@)">
            <summary>
            发送读取载波参数数据帧
            </summary>
            <param name="sourceData"></param>
            <param name="RevDataBuff"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseReadCarrierFrame(System.Byte[],System.Collections.Generic.List{System.Byte},System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            解析读取载波参数报文
            </summary>
            <param name="RevData"></param>
            <param name="IDs">元素ID集合</param>
            <param name="data">key：元素ID，value：元素ID对应的值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.GetReadCarrierFrame(System.String,System.Collections.Generic.List{System.Byte},System.String)">
            <summary>
            读取载波参数 组帧
            </summary>
            <param name="MeterAddress"></param>
            <param name="IDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseMeterEng(System.Byte[],System.Collections.Generic.List{System.Single}@)">
            <summary>
            解析操表电量
            </summary>
            <param name="RevData">收到的帧</param>
            <param name="lstEng">电量</param>
            <returns>错误编码</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.SetCL2041Channel(System.Int32)">
            <summary>
            切换CL2041通道
            </summary>
            <param name="type">通道</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.GetReadMeterData(System.String,System.String)">
            <summary>
            读表数据
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseMeterDatas(System.Byte[],System.Byte[]@)">
            <summary>
            解析读取表数据
            </summary>
            <param name="RevData">收到数据</param>
            <param name="DataRegion">数据域</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadCoreID(System.Int32,System.Int32)">
            <summary>
            读取芯片id
            </summary>
            <param name="count"></param>
            <param name="StartNum"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadNodeCount">
            <summary>
            读取节点数量
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseCoreData(System.Byte[],System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            解析
            </summary>
            <param name="revData"></param>
            <param name="dicCoreInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ReadNetworking(System.Int32,System.Int32)">
            <summary>
             读取组网信息
            </summary>
            <param name="count">节点数量</param>
            <param name="startNum">开始节点</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041.ParseNetWorkingData(System.Byte[],System.Collections.Generic.Dictionary{System.String,System.String}@)">
            <summary>
            提取组网信息
            </summary>
            <param name="revData"></param>
            <param name="dicCoreInfo"></param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041A.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041A.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041A.RevEquipMentData">
            <summary>
            操作等待时间
            </summary> 
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2041A.GetPanelCFigure(System.Int32)">
            <summary>
            控制字处理
            </summary>
            <param name="meterIDs">操作表位集合</param>
            <param name="trayCount"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043">
            <summary>
            切换卡槽
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043.#ctor(System.Int32)">
            <summary>
            类型
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043.ModifyMapped(System.Byte)">
            <summary>
            修改卡槽和替代卡映射关系
            </summary>
            <param name="CardNum">卡槽号：0x00,0x01,0x02,0x03</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2043.GetMapped(System.Byte@,System.Byte@)">
            <summary>
            获取卡槽对应关系
            </summary>
            <param name="CardNum">卡槽号</param>
            <param name="CardNo">替代卡号</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046">
            <summary>
            电压源
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.PowerOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Boolean)">
            <summary>
            关源
            </summary>
            <param name="sourceParam">关源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2046.AllowPowerOn">
            <summary>
            释放关源后不能升源的信号量
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101">
            <summary>
            用于载波继电器切换的多功能板
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2101.SwitchRelay(System.Byte,System.Byte)">
            <summary>
            继电器控制
            </summary>
            <param name="relayId">继电器ID</param>
            <param name="actTag">动作类型[0-断开 1-闭合]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.GetSampleFreq(System.Single@)">
            <summary>
            获取频率
            </summary>
            <param name="s8Freq">频率（单位：Hz）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.GetAllMeterIValue(System.Single[]@)">
            <summary>
            获取全部表位电流值
            </summary>
            <param name="values">电流值数组（单位：mA，L=承载数）</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.GetAllMeterState(System.Byte[]@)">
            <summary>
            获取全部表位状态
            </summary>
            <param name="states">表位状态数组[0-正常 1-故障]</param>
            <returns>True-读取成功；Flase-读取失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.SetILimenValue(System.Single)">
            <summary>
            设置电流阈值
            </summary>
            <param name="s8Limen">电流阈值（单位：mA；范围：0.1mA-12.0mA）</param>
            <returns>True-设参成功；Flase-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.ControlWork(System.Int32,System.Byte[])">
            <summary>
            控制表位工作     单相
            </summary>
            <param name="actTag">动作类型[0-耐压准备 1-放电]</param>
            <param name="ctlFigures">控制字数组[0-不操作 1-操作]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.ControlWork(System.Byte)">
            <summary>
            控制表位工作
            </summary>
            <param name="actTag">动作类型[0-复位 1-电压端子短接对地打耐压 2-三相电流短接对地打耐压 3-电压短接对电流短接打耐压 
            4-A相电流对B相电流打耐压 5-A相电流对C相电流打耐压 6-B相电流对C相电流打耐压 7-放电]</param>
            <returns>True-操作成功；Flase-操作失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2103.ResetAllMeterState">
            <summary>
            重置全部表位状态
            </summary>
            <param name="cellID">单元编号</param>
            <returns>True-重置成功；False-重置失败</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.GetTemperature(System.Collections.Generic.Dictionary{System.Int32,System.Single}@,System.Collections.Generic.List{System.Int32},System.Byte,System.Byte)">
            <summary>
            读取温度
            </summary>
            <param name="temperature">各通道表位温度</param>
            <param name="lstMeterIDs">各2151的受信地址</param>
            <param name="startChannelNum">起始通道号</param>
            <param name="askChannelAmount">读取通道数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL2151.GetTemperature(System.Boolean,System.Single@)">
            <summary>
            中鼎温湿度仪设备读取温度
            </summary>
            <param name="TypeValue">温度:True;湿度:False</param>
            <param name="Temperature"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013">
            <summary>
            3013标准源
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.#ctor(System.Int32)">
            <summary>
            3013标准源构造函数
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.PowerOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Boolean)">
            <summary>
            关源
            </summary>
            <param name="sourceParam">关源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL3013.AllowPowerOn">
            <summary>
            释放关源后不能升源的信号量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SubData(System.Int32,System.Byte[])">
            <summary>
            截取报文
            </summary>
            <param name="startNum">开始字符</param>
            <param name="buffdata">报文</param>
            <returns>截取后报文</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetTeleControlInfoState(System.Byte,System.Byte)">
            <summary>
            遥信状态（1200H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetTeleControlState(System.Byte,System.Byte,System.Byte)">
            <summary>
            遥控状态（1201H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="type"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetTransformerState(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            互感器状态（1202H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="stateA"></param>
            <param name="stateB"></param>
            <param name="stateC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetTerminalRS485State(System.Byte,System.Byte)">
            <summary>
            终端RS485接入状态（1203H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetOutputPulse(System.Byte,System.Int16,System.String,System.Byte,System.String)">
            <summary>
            设置输出频率（1204H~120aH）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="frequence"></param>
            <param name="dutyRadio"></param>
            <param name="pulseNum"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetMeterConstant(System.Byte,System.String)">
            <summary>
            设置标准表常数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetCheckedMeterConstant(System.Byte,System.Int16,System.String)">
            <summary>
            表位一路被检表常数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetCheckedMeterCircleNum(System.Byte,System.Int16,System.String)">
            <summary>
            表位一路被检表校验圈数
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="circle"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetMeterFrequence(System.Byte,System.String)">
            <summary>
            设置标准表时钟频率（1005H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="frequence"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.SetCheckedMeterFrequence(System.Byte,System.Int16,System.String)">
            <summary>
            设置被检表时钟频率（1006H，1012H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="frequence"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.ReadClockErr(System.Byte,System.Int16,System.Int16@,System.String@)">
            <summary>
            读日计时误差（1015H，1017H）  
            Byte4Byte5:数
             (Byte0-Byte3):误差植
             Byte0: 最高位bit7=0正，1=负;
             Bit3-Bit0小数点位数（从后算起）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="dayErrValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.ReadBaseErr(System.Byte,System.Int16,System.Int16@,System.String@)">
            <summary>
            读基本误差（1016H，1018H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="commAddr"></param>
            <param name="baseErrNum"></param>
            <param name="baseErrValue"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.ReadTeleControlState(System.Byte,System.Byte@,System.Byte@)">
            <summary>
            读遥控状态（1201H）
            </summary>
            <param name="frameData"></param>
            <param name="IsReadOrWriter"></param>
            <param name="meterID"></param>
            <param name="type"></param>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StartDayClock(System.Byte,System.Byte)">
            <summary>
            启动读日计时误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StartBaseError(System.Byte,System.Byte)">
            <summary>
            启动读基本误差功能0x01
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StartOutputPulse(System.Byte,System.Byte)">
            <summary>
            启动脉冲输出功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StopDayClock(System.Byte,System.Byte)">
            <summary>
            停止读日计时误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StopBaseError(System.Byte,System.Byte)">
            <summary>
            停止读基本误差功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CL321.StopOutputPulse(System.Byte,System.Byte)">
            <summary>
            停止脉冲输出功能
            </summary>
            <param name="meterID"></param>
            <param name="route"></param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.SetWireMode(CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            设置接线方式
            </summary>
            <param name="WireMode"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.SetCurrentGear(System.Single)">
            <summary>
            设置电流源固定档位
            </summary>
            <param name="current"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode,System.Int32)">
            <summary>
            升源
            </summary>
            <param name="sourceParam">升源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <param name="ValueOrPh">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="WireMode">被检电能表接线方式</param>
            <param name="DingDang">0://第一次升电流定档,1://后期更新电流定档</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.PowerOff(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Boolean)">
            <summary>
            关源
            </summary>
            <param name="sourceParam">关源需要数据参数</param>
            <param name="type">0:只更新电压 ,1:只更新电流,其他:更新电压电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.AllowPowerOn">
            <summary>
            释放关源后不能升源的信号量
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.SetVotFalloff(System.Int32)">
            <summary>
            电压特殊输出
            </summary>
            <param name="intType">电压操作类型，0=电压跌落和短时中断，1=电压逐渐变化，2=逐渐关机和启动</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.GetYuanEncryptionCode(System.Byte[],System.Byte[]@)">
            <summary>
            获取源序列号
            </summary>
            <param name="constantref">指令数据集</param>
            <param name="constant">返回数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.SetYuanEncryptionCode(System.Byte[])">
            <summary>
            设置源序列号
            </summary>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.ClearAlarm">
            <summary>
            清除报警
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX09.QueryAlarmInfo(System.String@)">
            <summary>
            查询报警状态信息
            </summary>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetEncryptionCode(System.Byte[])">
            <summary>
            设置表加密代码
            </summary>
            <param name="constant"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetStdMeterConst(System.UInt32)">
            <summary>
            设置标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetStdMeterEnergyType(System.Byte,System.Byte,System.Byte)">
            <summary>
            设置标准表电能指示
            </summary>
            <param name="powerType">功率类型(0.有功 1.无功 2.视在)</param>
            <param name="phaseType">相别(0.总1.A相2.B相3.C相)</param>
            <param name="energyType">电能类型(0.总电能1.基波电能2.谐波总电能3.谐波分次电能)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.ChangeStdMeterLineMode(CLDC.Framework.DataModel.Enum.EmLineMode,CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置标准表接线模式
            </summary>
            <param name="lMode">接线模式(3P3L,3P4L)</param>
            <param name="sMode">档位类型(auto-自动,manual-手动)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetStdMeterStartType(System.Byte)">
            <summary>
            设置标准表启动类型
            </summary>
            <param name="startType">启动类型(0.停止1.电能误差计算2.电能走字)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetStdMeterStall(System.Single,System.Single)">
            <summary>
            设置标准表档位
            </summary>
            <param name="vol">电压</param>
            <param name="cur">电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetUAndIStall(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            设置电压和电流档位
            </summary>
            <param name="ua">A相电压幅值</param>
            <param name="ub">B相电压幅值</param>
            <param name="uc">C相电压幅值</param>
            <param name="ia">A相电流幅值</param>
            <param name="ib">B相电流幅值</param>
            <param name="ic">C相电流幅值</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.GetEncryptionCode(System.Byte[],System.Byte[]@)">
            <summary>
            获取标准表序列号
            </summary>
            <param name="constantref">指令数据集</param>
            <param name="constant">返回数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.GetStdMeterConst(System.UInt32@)">
            <summary>
            获取标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            获取标准表监视数据
            </summary>
            <param name="monitorData">标准表监视数据</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.GetStdMeterEnergy(System.Int64@,System.UInt64@)">
            <summary>
            获取标准表累加电量和脉冲
            </summary>
            <param name="Energy">标准表电量</param>
            <param name="Pulses">标准表脉冲</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.SetMeterCheckParams(System.UInt32,System.UInt32)">
            <summary>
            设置标准表检定参数
            </summary>
            <param name="meterConst">被检标准表常数</param>
            <param name="pulseCount">校验圈数(脉冲个数)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115.GetMeterErr(System.Single@)">
            <summary>
            获取被检标准表误差
            </summary>
            <param name="meterErr">误差值</param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.SetStdMeterConst(System.UInt32)">
            <summary>
            设置标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.SetStdMeterEnergyType(System.Byte,System.Byte,System.Byte)">
            <summary>
            设置标准表电能指示
            </summary>
            <param name="powerType">功率类型(0.有功 1.无功 2.视在)</param>
            <param name="phaseType">相别(0.总1.A相2.B相3.C相)</param>
            <param name="energyType">电能类型(0.总电能1.基波电能2.谐波总电能3.谐波分次电能)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.ChangeStdMeterLineMode(CLDC.Framework.DataModel.Enum.EmLineMode,CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置标准表接线模式
            </summary>
            <param name="lMode">接线模式(3P3L,3P4L)</param>
            <param name="sMode">档位类型(auto-自动,manual-手动)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.SetStdMeterStartType(System.Byte)">
            <summary>
            设置标准表启动类型
            </summary>
            <param name="startType">启动类型(0.停止1.电能误差计算2.电能走字)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.SetStdMeterStall(System.Single,System.Single)">
            <summary>
            设置标准表档位
            </summary>
            <param name="vol">电压</param>
            <param name="cur">电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.GetStdMeterConst(System.UInt32@)">
            <summary>
            获取标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            获取标准表监视数据
            </summary>
            <param name="monitorData">标准表监视数据</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.GetStdMeterEnergy(System.Int64@,System.UInt64@)">
            <summary>
            获取标准表累加电量和脉冲
            </summary>
            <param name="Energy">标准表电量</param>
            <param name="Pulses">标准表脉冲</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.SetMeterCheckParams(System.UInt32,System.UInt32)">
            <summary>
            设置标准表检定参数
            </summary>
            <param name="meterConst">被检标准表常数</param>
            <param name="pulseCount">校验圈数(脉冲个数)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CLX115_1.GetMeterErr(System.Single@)">
            <summary>
            获取被检标准表误差
            </summary>
            <param name="meterErr">误差值</param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.SubData(System.Int32,System.Byte[])">
            <summary>
            截取报文
            </summary>
            <param name="startNum">开始字符</param>
            <param name="buffdata">报文</param>
            <returns>截取后报文</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.PackParameters(CLDC.CLAT.CLWBS.DataModel.Enum.Em698.EmSecurityMode,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            698协议组参
            </summary>
            <param name="mode"></param>
            <param name="MeterAddress"></param>
            <param name="Ods"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Controller.SendDataControll(System.String,System.Collections.Generic.List{System.Single}@)">
            <summary>
            操控器发送数据
            </summary>
            <param name="sourceData">要发送的数据</param>
            <param name="RevDataBuff">收到的数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CPCamera.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CPCamera.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.CPCamera.HTS(System.String)">
            <summary>
            16进制字符串转文本
            </summary>
            <param name="mHex"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Create_Rand(System.String@)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13Servers.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_IdentifyAuthentication(System.Int32,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            水投规范身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="index">城市索引 – 对应于密钥系统中的城市索引值（当 iType 为 0 时此值无意义）</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_ExternalAuthtication(System.Int32,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            水投规范外部认证
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="index">城市索引  对应于密钥系统中的城市索引值   当 iType 为 0 时此值无意义</param>
            <param name="EsamRandom">ESAM随机数长度8</param>
            <param name="PutDiv">输出的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="pMsg">输出的错误信息</param>>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_ParameterUpdate(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
             <param name="index">城市索引 对应于密钥系统中的城市索引值   当 iType 为 0 时此值无意义 </param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_GetEncryptData(System.Int32,System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="index">城市索引 – 对应于密钥系统中的城市索引值   当 iType 为 0 时此值无意义</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_IncreasePurse(System.Int32,System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013远程充值
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="index">城市索引 – 对应于密钥系统中的城市索引值 当 iType 为 0 时此值无意义</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,表示输入的参数明文,包含4 字节购电次数 + 4 字节购电金额</param>
            <param name="OutData">输出的数据,输出的 4 字节购电次数 + 4 字节购电金额 + MAC，字符型</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_InitPurse(System.Int32,System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="index">城市索引 – 对应于密钥系统中的城市索引值（当 iType 为 0 时此值无意义）</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,表示输入的参数明文,4 字节预置金额 + 4 字节预置购电次数</param>
            <param name="OutData">输出的数据,4 字节预置金额 + 4 字节 MAC1 + 4 字节预置购电次数 + 4 字节MAC2，字符型；</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServerDllImport.SC_ST_KeyUpdate(System.Int32,System.Int32,System.Int32,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="iSpecialEsam">标识是否为特殊 ESAM 用于存量部分 ESAM 未发行二次使用   0x00  标准 ESAM（已发行第二遍）0x01 – 特殊 ESAM（未发行第二遍）</param>
            <param name="index">城市索引 – 对应于密钥系统中的城市索引值  当 iType 为 0 时此值无意义</param>
            <param name="OutData">返回的 0x20 * 7 条密钥密文</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0成功,其它失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            水投规范身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_ExternalAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            外部认证
            </summary>
            <param name="Flag">密钥状态</param>
            <param name="PutRand">电表随机数</param>
            <param name="PutDiv">输出的分散因子</param>
            <param name="OutEndata">输出的密文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
             <summary>
            水投规范一类参数MAC计算函数
             </summary>
             <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
             <param name="PutRand">输入的随机数2(字符型,长度8)</param>
             <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
             <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
             <param name="PutData">输入的一类参数明文(字符型)</param>
             <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
             <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_STServers.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            水投规范密钥更新
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_IdentifyAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
            威盛09控制命令加密函数
             </summary>
             <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
             <param name="PutRand">输入的随机数2(字符型,长度8)</param>
             <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
             <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
             <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
             <param name="OutEndata">输出的密文(字符型)</param>
             <param name="pMsg">输出的错误信息</param>
             <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 2 字节后，补成模 16 的倍数再加 4 字节。 将密文写入到 ESAM 后存储格式为：1 字节明文数据长度 + 明文数据 DATA </param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09备用套电价参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <param name="pMsg">输出的错误信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_IncreasePurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09远程充值
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,表示输入的参数明文,包含购电金额、购电次数和户号；</param>
            <param name="OutData">输出的数据,购电金额+购电次数+MAC1+户号+MAC2</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_MacCheck(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09ServerDllImport.SC_GW09_KeyUpdate(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            威盛09密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeySum">要更新/恢复的密钥类型 0x01 – 远程身份认证密钥 0x02 – 远程控制密钥 0x03 – 二类参数类型密钥</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="pDataIn">4 字节远程密钥信息</param>
            <param name="OutData">N *（4 字节密钥信息+32 字节密钥密文）+ 4 字节 MAC，N不大于 4</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            威盛09身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            威盛09控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            威盛09一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
             <summary>
            威盛09二类参数加密函数
             </summary>
             <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
             <param name="PutRand">输入的随机数2(字符型,长度8)</param>
             <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
             <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
             <param name="PutData">输入的二类参数明文(字符型)</param>
             <param name="OutEndata">输出的密文(字符型)</param>
             <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            威盛09第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            威盛09第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            威盛09数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_WS09Servers.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            威盛09密钥更新
            </summary>
            <param name="PutKeySum">要更新/恢复的密钥类型 0x01 – 远程身份认证密钥 0x02 – 远程控制密钥 0x03 – 二类参数类型密钥</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.ConnectDevice(System.String,System.String,System.String)">
            <summary>
            连接加密机
            </summary>
            <param name="cIP">IP地址</param>
            <param name="cPort">端口号</param>
            <param name="cTime">超时时间</param>
            <returns>0：成功，1：失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Formal_GetRandHost(System.Text.StringBuilder)">
            <summary>
            用于从主站获取随机数
            </summary>
            <param name="cOutRandHost">输出随机数16字节</param>
            <returns>0：成功，1：失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取主站会话协商数据
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cASCTR">应用会话计数器可从芯片读取，系统须存储，每次会话必须必上次大1</param>
            <param name="cFLG">"01"</param>
            <param name="cOutRandHost">主站随机数 16bytes</param>
            <param name="cOutSessionInit">会话协商数据</param>
            <param name="cOutMac">对会话协商数据计算的mac 值，4Byte；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_JL_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取主站会话协商数据 计量芯
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cDiv">表号</param>
            <param name="cASCTR">应用会话计数器可从芯片读取，系统须存储，每次会话必须必上次大1</param>
            <param name="cFLG">"01"</param>
            <param name="cOutRandHost">主站随机数 16bytes</param>
            <param name="cOutSessionInit">会话协商数据</param>
            <param name="cOutMac">对会话协商数据计算的mac 值，4Byte；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数 16bytes</param>
            <param name="cSessionData">电能表返回的应用会话协商数据，48Byte</param>
            <param name="cSign">电能表返回的应用会话协商数据签名，4Byte</param>
            <param name="cOutSessionKey">：会话密钥，176Byte</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_JL_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证 计量芯
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数 16bytes</param>
            <param name="cSessionData">电能表返回的应用会话协商数据，48Byte</param>
            <param name="cSign">电能表返回的应用会话协商数据签名，4Byte</param>
            <param name="cOutSessionKey">：会话密钥，176Byte</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_VerifyReadData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            抄读数据验证
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数，16Byte 抄读数据时主站下发</param>
            <param name="cReadData">抄读数据</param>
            <param name="cMac">MAC 数据</param>
            <param name="cOutData">返回的抄读明文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_GetSessionData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取下发参数数据
            </summary>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskType">参数类型：4，安全模式设置、设置会话时效门限；5，电价设、电价切换时间、费率时段、对时任务设置；6，除拉闸外的控制任务设置；8，拉闸任务设置；3，除上述操作外的操作。</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID">安全标示</param>
            <param name="cOutAttachData">数据附加</param>
            <param name="cOutData">明文或数据</param>
            <param name="cOutMAC">数据验证码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_GetMeterSetData(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
             通用数据加密函数，写 ESAM 操作和钱包操作数据下发通过此函数进行安
             </summary>
             <param name="cOperateMode">操作模式</param>
             <param name="cESAMID">ESAM 序列</param>
             <param name="cSessionKey">会话密钥，176Byte</param>
             <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID">安全标示</param>
             <param name="cOutAttachData">数据附加</param>
             <param name="cOutData">明文或数据</param>
             <param name="cOutMAC">数据验证码</param>
             <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_VerifyMeterData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskData">数据</param>
            <param name="cMac">mac</param>
            <param name="cOutData">数据明文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_SetESAMData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
             设置 ESAM 参数:用于设置表号、当前套电价文件、备用套电价文件、ESAM 存储标识。
             </summary>
             <param name="InKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
             <param name="InOperateMode">操作模式</param>
             <param name="cEsamId">ESAM 序列</param>
             <param name="cSessionKey">会话密钥，176Byte</param>
             <param name="cMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
             <param name="cESAMRand"></param>
             <param name="cData">4ByteOAD + 1Byte 内容LEN + 内容</param>
            <param name="OutSID">安全标示</param>
             <param name="OutAttachData">数据附加</param>
             <param name="OutData">明文或数据</param>
             <param name="OutMAC">数据验证码</param>
             <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_GetTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            电能表对称密钥更新
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="InMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
            <param name="cKeyType">密钥类型，00 应用密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData">电能表对称密钥数据</param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_InitTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            电能表对称密钥初始化
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="InMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
            <param name="cKeyType">密钥类型，00 应用密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData">电能表对称密钥数据</param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Meter_Formal_GetPurseData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            钱包操作函数
            </summary>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskType">任务序编号，9 钱包初始化；10，钱包充值；11，钱包退费</param>
            <param name="cTaskData">数据明文，包含预置金额，4Byte</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData"></param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            主站会话协商终端（数字签名连接认证机制，用于主站与设备进行会话协商时产生密文和签名数据，该过程在建立应用连接时完成。）
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cASCTR">计数器</param>
            <param name="cFLG">应用密钥产生标识，1Byte，默认”01”；</param>
            <param name="cMasterCert">主站证书；</param>
            <param name="cOutRandHost">主站随机数（16Byte）；</param>
            <param name="cOutSessionInit">会话协商数据（32Byte），建立应用连接中的密文1；</param>
            <param name="cOutSign">协商数据签名(64Byte) ，建立应用连接中的客户机签名1；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证(数字签名连接认证机制，用于主站验证设备会话协商时返回的数据，验证成功主站产生会话密钥。)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cRandHost">主站随机数R1（16Byte）</param>
            <param name="cSessionData">终端返回的应用会话协商数据(48Byte)，对应建立应用连接中的密文</param>
            <param name="cSign">终端返回的应用会话协商数据签名(64Byte)，对应建立应用连接中的签名数据2；</param>
            <param name="cTerminalCert">终端证书</param>
            <param name="cOutSessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_VerifyReadData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            抄读数据验证(主站验证设备返回的抄读数据，具体指抄读终端返回的数据)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cRandHost">主站随机数(16Byte)</param>
            <param name="cReadData">抄读数据</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文抄读数据，iOperateMode=1，为空</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_VerifyReportData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            上报数据验证(设备主动上报数据时，主站验证数据的合法性)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cRandT">终端随机数(12B)</param>
            <param name="cReportData">上报数据</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文数据，iOperateMode=1，为空</param>
            <param name="cOutRSTCTR">主动上报随机数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_GetResponseData(System.Int32,System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            上报数据返回报文加密(用于设备主动上报主站返回帧数据加密计算)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="RandHost">上报随机数，12Byte</param>
            <param name="cReportData">上报数据</param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="ucOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_GetSessionData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            安全传输加密(用于对具体业务数据进行数据加密计算)
            </summary>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTaskType">参数类型(4，安全模式参数，会话时效；7，拉闸；8，文件传输。3，除上述操作外的数据加密。)</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="cOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_VerifyTerminalData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            安全传输解密(用于验证终端返回帧数据解密验证)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_GetGrpBrdCstData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            广播数据加密(用于广播数据加密计算)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cBrdCstAddr">广播地址</param>
            <param name="AGSEQ">广播应用通信序列号，4Byte</param>
            <param name="cBrdCstData">广播数据明文；</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="cOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_GetTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            终端对称密钥更新(用于对称密钥更新)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTerminalAddress">终端地址</param>
            <param name="cKeyType">密钥类型，00 应用密钥，01 链路密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData"></param>
            <param name="cOutMac">MAC数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_InitTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            终端对称密钥初始化(用于对终端密钥进行初始化，会话计数器次数为1 时，须先对密钥进行初始化)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTerminalAddress">终端地址</param>
            <param name="cKeyType">密钥类型，00 应用密钥，01 链路密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData"></param>
            <param name="cOutMAC">MAC数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImportAPI.Obj_Terminal_Formal_GetCACertificateData(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取证书信息(用于对终端通密钥进行初始化，会话计数器次数为1 时，须先对密钥进行初始化。)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cCerType">证书类型</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutCertificateData"></param>
            <param name="cOutMAC">MAC数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentData(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentDataEv(System.Int32)">
            <summary>
            操作等待时间事件清零
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentDataParameter(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SubData(System.Int32,System.Byte[])">
            <summary>
            截取报文
            </summary>
            <param name="startNum">开始字符</param>
            <param name="buffdata">报文</param>
            <returns>截取后报文</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadMeterLoadRecord(System.String,System.String,System.String,System.Collections.Generic.List{System.Object}@)">
            <summary>
            读负荷记录数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="DataId">数据标识</param>
            <param name="ReadData">指定时间块数YYMMDDhhmmNN</param>
            <param name="OutData">负荷记录数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetMetersAddress(System.String@)">
            <summary>
            获取电能表的通讯地址
            </summary>
            <param name="meterIDs">表位数组</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterBroadcastData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            写电表广播数据
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="dataCode">数据标识04001401</param>
            <param name="strWriteData">写入数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadMetersEnergy(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            读取电能表各费率和总电能(单位: kWh)
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="meterAddrs">通讯地址数组</param>
            <param name="powerType">(1=正向有功2=反向有功3=正向无功4=反向无功)</param>
            <param name="workType">工作类型(0-操作,1-载波,2-红外)</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterTime(System.String,System.String,System.String,System.DateTime)">
            <summary>
            设置多块表的时间
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dateTime">时间</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterDate(System.String,System.String,System.String,System.DateTime)">
            <summary>
            设置多块表的日期
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dateTime">时间</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterPulseType(System.String,System.String,System.String,System.String)">
            <summary>
            设置多块表的多功能端子
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="pulseType">脉冲类型：00-时钟秒脉冲，01-需量周期，02-时段投切</param>
            <param name="medType">通讯媒介</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetMacAdress(System.String,System.String,System.String,System.String)">
            <summary>
            设置蓝牙Mac地址
            </summary>
            <param name="commAddrs">地址 默认全A</param>
            <param name="localMacAddr">本地Mac</param>
            <param name="salaveMacAddr">从机Mac</param>
            <param name="power">功率</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetMeterModeSwicth(System.String,System.Int32,System.String,System.String)">
            <summary>
            校表模式切换
            </summary>
            <param name="commAddrs">地址 默认全A</param>
            <param name="meterId">表号</param>
            <param name="channel">通道号  低4位有效（有功、无功）0F表示4路同时校准</param>
            <param name="power"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ControlMorMeterClear(System.String,System.String,System.String)">
            <summary>
            清电表电量
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="medType">通讯媒介</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ControlMaxDemandClear(System.String,System.String,System.String)">
            <summary>
            清电表需量
            </summary>
            <param name="cellID">单元编号</param>
            <param name="meterIDs">表位号数组</param>
            <param name="commAddrs">通讯地址数组</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="medType">通讯媒介</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadSinMeterData(System.String,System.String,System.String,System.String,System.String@,System.Int32,System.Boolean)">
            <summary>
            读表数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterFullDemand(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            读电表各费率需量及总需量
            </summary>
            <param name="commAddr">表地址</param>
            <param name="wattType">做功方向</param>
            <returns>需量集合</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterSpareMoney(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表剩余金额
            </summary>
            <param name="commAddr">表地址</param>
            <returns>金额</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterJtdj(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表阶梯电价
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>电价</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterSpareEng(System.String,System.String,System.String,System.Single@)">
            <summary>
            读取电表中剩余电量
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterRunState(System.String,System.String,System.String,CLDC.CLAT.CLWBS.DataModel.Struct.StMeterRunState@)">
            <summary>
            获取电表运行状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>返回电能表运行状态</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetSinMeterRunState(System.String,System.String,System.String,CLDC.CLAT.CLWBS.DataModel.Struct.StRunState@)">
            <summary>
            获取电表运行状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <returns>返回电能表运行状态</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetEsamFileData(System.String,System.String,System.String,System.Byte,CLDC.CLAT.CLWBS.DataModel.Struct.StEsamRunFile@,System.String@)">
            <summary>
            抄表ESAM中数据
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="fileType">文件类型[0-密钥文件 2-参数信息文件 5-密钥信息文件 6-运行信息文件 7-控制命令文件]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.Identity_AuthenticationTime(System.String,System.String,System.String)">
            <summary>
            身份认证时间设置
            </summary>
            <param name="commAdd"></param>
            <param name="code"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterWriteData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            写电表任意数据
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="dataCode">数据标识04001401</param>
            <param name="strWriteData">写入数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ControlSinMeterState(System.String,System.String,System.String,System.String)">
            <summary>
            控制电能表状态
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者</param>
            <param name="Endata">控制数据，拉合闸报警保电密文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterPullSwitch(System.String,System.String,System.String,System.Byte,System.Byte)">
            <summary>
            设置电表跳闸延时时间
            </summary>
            <param name="commAddr">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="ctlType">控制类型[0-跳闸 1-合闸 2-报警 3-报警解除 4-保电 5-保电解除]</param>
            <param name="times">时间</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterStatusAticPKey(System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutRand1">随机数1</param>
            <param name="OutEndata1">密文1</param>
            <param name="PutDiv">分散因子</param>
            <param name="OutRand2">返回随机数2</param>
            <param name="OutESAMNo">返回ESAM序列号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterExternalStatusAticPKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            外部安全认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata2">密文2</param>
            <param name="PutDiv">分散因子</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterKeyUpdata_Main(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            主控密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo1">密钥信息</param>
            <param name="OutKeyInfo1">MAC</param>
            <param name="OutKey1">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterKeyUpdata_Control(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            控制命令密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo2">密钥信息</param>
            <param name="OutKeyInfo2">MAC</param>
            <param name="OutKey2">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterKeyUpdata_Params(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            参数更新，密钥
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo3">密钥信息</param>
            <param name="OutKeyInfo3">MAC</param>
            <param name="OutKey3">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterKeyUpdata_Identity(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            身份认证密钥下装
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="putKeyinfo4">密钥信息</param>
            <param name="OutKeyInfo4">MAC</param>
            <param name="OutKey4">密钥数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ClearSinMeterKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            密钥信息清零
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="macAddr">MAC</param>
            <param name="keyData">密钥信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptClearMeter(System.String,System.String,System.String,System.String)">
            <summary>
            加密方式电能表清零
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，N1～Nm为密文。N1～Nm解密后的明文数据为R1～R8，其中R1=1AH，R2保留，默认为00H，R3～R8代表命令有效截止时间，数据格式为YYMMDDhhmmss。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptInintPurse(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            加密方式电能表钱包初始化
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，N1～Nm为密文。N1～Nm解密后的明文数据为R1～R8，其中R1=1AH，R2保留，默认为00H，R3～R8代表命令有效截止时间，数据格式为YYMMDDhhmmss。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptReadMeterData(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            加密方式读数据
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata">密文数据</param>
            <param name="handlCode">操作者代码</param>
            <param name="RevData">收到的数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptWriteMeterData(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            加密方式写数据
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata">写入的数据，98H级密码权限代表通过密文+MAC的方式进行数据传输，不需要密码验证也不需要按编程键配合。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptClearDemand(System.String,System.String,System.String,System.String)">
            <summary>
            加密方式最大需量清零
            </summary>
            <param name="commAddrs">表地址集合</param>
            <param name="passWord">密码：98H级密码权限下，P0P1P2保留，默认为000000H。</param>
            <param name="handleCode">操作者代码</param>
            <param name="OutEndata"> 98H级密码权限下，N1～Nm为密文。N1～Nm解密后的明文数据为R1～R8，其中R1=19H，R2保留，默认为00H，R3～R8代表命令有效截止时间，数据格式为YYMMDDhhmmss。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.EncryptUpdateKey(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            2013新的方式密钥下装，一次4条密钥指令下发，共20条密钥。
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutData">加密机返回的密钥信息，4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetSinMeterMode(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            模式切换
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>        
            <param name="OutEndata1">密文1</param>        
            <param name="OutModeNo">返回费控模式状态字</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetMeterInfraredStatusAticPKey(System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            红外认证
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>        
            <param name="dataCode">数据标识</param>    
            <param name="OutEndata1">密文1</param>        
            <param name="Data">红外认证返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.WriteData(System.String,System.String,System.String,System.String,System.String,System.Int32,System.Byte[]@,System.Byte[]@,System.Boolean)">
            <summary>
            写表参数
            </summary>
            <param name="Addrs">地址表s</param>
            <param name="dataCode">功能代码</param>
            <param name="passWord">操作密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="writeMeterData">下发参数</param>
            <param name="outTime">超时时间</param>
            <param name="isOldProtocol">是否为老协议（DLT 645-1997）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.MeterEventCleal(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            事件清零
            </summary>
            <param name="Addrs"></param>
            <param name="dataCode"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RestorationBaudRate(System.String,System.Byte,System.Byte@)">
            <summary>
            复位波特率
            </summary>
            <param name="commAddr">表地址</param>
            <param name="taggedWord">波特率特征字</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentDataqy(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetMeterInspectType(System.String,System.Byte,System.Int32)">
            <summary>
            设置核查表模式
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="dataCode">功能码</param>
            <param name="setType">设置模式类型 0 设置进入核查模式， 1启动物理核查</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetConstantPattern(System.String,System.Int32)">
            <summary>
            设置台体脉冲常数模式
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="Constant">被检脉冲常数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetImpulseCount(System.String,System.String,System.Int32)">
            <summary>
            设置台体脉冲数
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="impulseNum">脉冲编号</param>
            <param name="impulseCount">脉冲数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadMeterErrorValue(System.String,System.String[]@)">
            <summary>
            读取误差
            </summary> 
            <param name="commAddr">表接收地址</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetMeterLineMode(System.String,System.Int32,System.Int32)">
            <summary>
            设置表接线模式
            </summary> 
            <param name="commAddr">表接收地址</param>
            <param name="lineMode">接线模式</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentData376">
            <summary>
            南方电网报文接收
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentData_376">
            <summary>
            国网、云网报文接收
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ZDSubData(System.Int32,System.Byte[])">
            <summary>
            南方电网截取终端完整报文
            </summary>
            <param name="startNum">开始字符</param>
            <param name="buffdata">报文</param>
            <returns>截取后报文</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.NWZDSubData(System.Int32,System.Byte[])">
            <summary>
            用于南方电网终端协议
            </summary>
            <param name="startNum"></param>
            <param name="buffdata"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ZDSubData_376(System.Int32,System.Byte[])">
            <summary>
            用于国网、云网终端协议
            </summary>
            <param name="startNum"></param>
            <param name="buffdata"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadTerminalInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            南方电网读取终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="str">返回信息，多个信息以|分隔开</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.ReadTerminalInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String@)">
            <summary>
            国网、云网读取终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="str">返回信息，多个信息以|分隔开</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端信息
            </summary>
            <param name="terminalInfo"></param>
            <param name="b">是否需要带参数</param>
            <param name="strSetInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalMasterIP376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端主站信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置的主站信息（主站IP|端口号|通信通道模式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalMasterIP_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端主站IP
            </summary>
            <param name="terminalInfo"></param>
            <param name="strSetInfo">主站IP|端口|备用IP|备用端口|APN</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalIPInfo376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            南方电网设置终端IP信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">要设置IP信息（通讯模式|TCP端口|终端IP|子网掩码|网关地址|IP获取的设置方式）</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetTerminalIPInfo_376(CLDC.CLAT.CLWBS.DataModel.Class._376.TerminalInfo,System.String)">
            <summary>
            国网、云网设置终端IP信息
            </summary>
            <param name="terminalInfo">终端基础信息</param>
            <param name="strSetInfo">终端IP|子网掩码|网关|代理类型|代理服务IP|代理端口|代理连接方式|用户长度|用户名|密码长度|密码|侦听端口</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.StartRevTerinalLinkThread">
            <summary>
            南方电网开启一个线程处理预链接（登录和心跳）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.StartRevTerinalLinkThread_376">
            <summary>
            国网、云网开启一个线程处理预链接（登录和心跳）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.Link">
            <summary>
            南方电网登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.Link376">
            <summary>
            国网、云网登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.StopTerinalLinkThread">
            <summary>
            关闭登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SatartRevLinkFrameThread(System.String)">
            <summary>
            开启一个线程处理预链接（登录和心跳）
            </summary>
            <param name="Address">终端地址</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.StopRevLinkFrameThread">
            <summary>
            关闭登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.RevEquipMentData698s(System.Boolean,System.Int32)">
            <summary>
            蓝牙模块返回数据处理 
            </summary>
            <param name="fg">读取模块频点 为true</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetBuildBluetoothInstruct(System.String,CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel)">
            <summary>
            智芯蓝牙盒子指令发送
            </summary>
            <param name="commAddrs"></param>
            <param name="pulseType"></param>
            <param name="Functioncode"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.B_Reset">
            <summary>
            复位操作
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.SetStdMeterConst(System.UInt32)">
            <summary>
            设置(雷电)标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetStdMeterConst(System.UInt32@)">
            <summary>
            获取(雷电)标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            获取(雷电)标准表监视数据
            </summary>
            <param name="monitorData">标准表监视数据</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.HexStrToBytsDataA(System.String,System.Int32)">
            <summary>
            16进制字符串转字节数组
                【"123" => {0x01,0x23}】
            </summary>
            <param name="strData">16进制字符串</param>
            <param name="aryLen">数组长度</param>
            <returns>字节数组</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Meter.AdjustmentStrSize(System.String,System.Int32,System.Boolean)">
            <summary>
            调整字符串大小
            </summary>
            <param name="origStr">待调字符串</param>
            <param name="desiredSize">调整的大小</param>
            <param name="isForward">是否前移</param>
            <returns>调整后的字符串</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService">
            <summary>
            表位服务板
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.CheckType">
            <summary>
            当前检定类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.waitSend">
            <summary>
            等待发送数据锁
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.#ctor(System.Int32)">
            <summary>
            表位服务
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.DebugMsgToFile(System.Int32,System.String)">
            <summary>
            发送日志到文件
            </summary>
            <param name="Msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SendMsgToFile(System.Int32,System.Int32,System.String,System.String,System.String)">
            <summary>
            发送日志到文件
            </summary>
            <param name="Msg">日志信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.RevEquipMentData(System.Int32,System.Int32,System.Boolean)">
            <summary>
            接收数据
            </summary>
            <param name="sendFrameLen">发送帧长度</param>
            <param name="revFrameCount">接收帧数量</param>
            <param name="isReturnOneFrame">是否只接收一帧数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SendServicePanelAddrs(System.Int32[])">
            <summary>
            下发服务板地址
            </summary>
            <param name="meterIDs"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SendMeterId(System.Byte)">
            <summary>
            下发表位地址
            </summary>
            <param name="servicePanelAddr">服务板地址</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetPortParams(System.Int32[],System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            设置串口参数
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="dicPortParam">串口参数字典[key-串口编号 value-波特率,例：2400,e,8,1]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPort})">
            <summary>
            设置同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPorts">端口数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetNoSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin})">
            <summary>
            设置不同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetPinPulseCapture(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Int32)">
            <summary>
            设置引脚脉冲捕获
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="actionType">捕获动作0.停止捕获 1.开始捕获</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.AbortAllFunction(System.Int32[],System.Byte,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            停止或者开始服务板相应功能
            </summary>
            <param name="meterIDs">表位号数组</param>
            <param name="actTag">动作类型[0-开始计算 1-停止计算]</param>
            <param name="chkType">检定类型</param>
            <param name="channel">通道号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetErrPanelParam(System.Int32[],System.UInt32,System.Single,System.UInt32,System.UInt32,System.Byte,System.Int32)">
            <summary>
            设置服务板检定参数
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetClockParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置日计时误差参数
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="sClkFreq">标准时钟频率(单位: KHZ; 500KHZ)</param>
            <param name="cClkFreq">被检时钟频率(单位: HZ; 100HZ)</param>
            <param name="pClkCount">被检脉冲个数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetDemandParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,System.Int32)">
            <summary>
            设置需量服务检定参数（CL188L）
            </summary>(
            <param name="meterIds">表位集合</param>
            <param name="sClkFreq">标准时钟频率（单位：Hz）</param>
            <param name="cClkFreq">时间周期（单位：ms）</param>
            <param name="pClkCount">被检脉冲个数</param>
            <param name="channel">通道号</param>
            <returns>True-设参成功；False-设参失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SetPulseChannel(System.Int32[],CLDC.Framework.DataModel.Enum.EmPulseChannel,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            设置服务板脉冲通道
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="plsChannel">被检脉冲通道[0-P+,1-P-,2-Q+,3-Q-,4-日计时,5-需量脉冲]</param>
            <param name="plsType">脉冲输入类型[0-电子式脉冲输入 1-感应式脉冲输入]</param>
            <param name="polar">共阴或共阳(0共阴,1共阳)</param>
            <param name="chkType">检定类型[0-电能服务 1-走字计数 2-预付费功能检定 3-对标 4-日计时服务 5-需量服务]</param>
            <param name="pulseSpeendType">脉冲类型[0-低速脉冲（普通电能表输出） 1-高速脉冲（标准电能表输出)]</param>
            <param name="countType">计数类型[0-脉冲计数 1-脉冲间隔时间]</param>
            <param name="photoelectricity">光电类型[0-电脉冲 1-光脉冲]</param>
            <param name="channel">通道号</param>
            <param name="divisionFactor">分频系数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.ClearAllFaultState(System.Int32[],System.Byte)">
            <summary>
            清除所有表位故障状态
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="faultType">故障类型[1-接线故障状态 2-预付费跳闸状态 3-报警信号状态]</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.GetMorMeterTempratureValues(System.Collections.Generic.List{System.Int32},System.Int32,System.Int32,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@)">
            <summary>
            获取表位接线柱温度
            </summary>
            <param name="MeterIds">表位号数组</param>
            <param name="MeterType">电表类型 1：单相</param>
            <param name="AccessType">接入方式(直接：1，互感：2)</param>
            <param name="DitpanelInfo">状态</param>
            <param name="DitLstData">返回结果数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.ReadNoSamePortPinState(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StPinConfigurationParam}}@)">
            <summary>
            查询不同端口引脚状态
            </summary>
            <param name="MeterIds">表位号数组</param>
            <param name="clServicePanelPortPins">端口引脚列表</param>
            <param name="DicClServicePanelPortPin">端口引脚参数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.ReadPinPulseCapture(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Int32}}@)">
            <summary>
            查询引脚脉冲捕获
            </summary>
            <param name="MeterIds">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="DicClServicePulseCaptures">端口引脚脉冲</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.GetErrorCurrent(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@)">
            <summary>
            扩展查询误差板当前误差命令
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.GetMorMeterTenErrValues(System.Collections.Generic.List{System.Int32},CLDC.Framework.DataModel.Enum.EmCheckType,System.Collections.Generic.Dictionary{System.Int32,CLDC.CLAT.CLWBS.DataModel.Struct.StErrPanelInfo}@,System.Collections.Generic.Dictionary{System.Int32,System.Collections.Generic.List{System.Single}}@,System.Int32@,System.Int32,System.Int32,System.Int32)">
            <summary>
            获取多块表10次误差值
            </summary>
            <param name="meterIds">表位号数组</param>
            <param name="chkType">检定类型</param>
            <param name="DitpanelInfo">服务板数据</param>
            <param name="DitLstData">误差值</param>
            <param name="channel">通道号</param>
            <param name="readType">“清远期间核查”误差读取类型 >=1表示读取误差板获取的脉冲数 </param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SendTakingDownDeviceData(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Byte[]},System.Int32,System.Collections.Generic.Dictionary{System.Int32,System.Byte[]}@)">
            <summary>
            下发从站设备数据
            </summary>
            <param name="meterIds">表位号</param>
            <param name="data">下发数据</param>
            <param name="takingDownDeviceId">下挂设备编号</param>
            <param name="ReturnData">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.GetPanelCFigure(System.Int32[],System.Int32)">
            <summary>
            服务板控制字处理
            </summary>
            <param name="meterIDs">操作表位集合</param>
            <param name="trayCount"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.InteractiveRadioAutoSetAddr(System.Int32[])">
            <summary>
            交互广播自动设置地址
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.UpgradeEnabled(System.Int32[])">
            <summary>
            升级使能
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.SoftwareReset(System.Int32[])">
            <summary>
            软件重启
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.WriteUpgradeData(System.Int32[],System.Byte[],System.Int32)">
            <summary>
            写升级数据
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="writeData">写入数据</param>
            <param name="dataIndex">写入序号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterService.GetCurrentVersions(System.Int32)">
            <summary>
            查询当前软件本号
            </summary>
            <param name="meterId"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit">
            <summary>
            电表数据（服务板）中转
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.GetMetersAddress(System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            获取电能表的通讯地址
            </summary>
            <param name="MeterAddress">表地址</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.GetMetersAddress(System.String@)">
            <summary>
            获取电能表的通讯地址
            </summary>
            <param name="meterAddress">表位数组</param>、
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.ReadSinMeterData(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String}@,System.Int32,System.Boolean)">
            <summary>
            读表任意数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.ReadSinMeterData(System.String,System.String,System.String,System.String,System.String@,System.Int32,System.Boolean)">
            <summary>
            读表数据
            </summary>
            <param name="commAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.ReadMetersEnergy(System.String,System.String,System.String,CLDC.Framework.DataModel.Enum.EmWattType,System.Collections.Generic.List{System.Single}@)">
            <summary>
            读取电能表各费率和总电能(单位: kWh)
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="meterAddrs">通讯地址数组</param>
            <param name="powerType">(1=正向有功2=反向有功3=正向无功4=反向无功)</param>
            <param name="workType">工作类型(0-操作,1-载波,2-红外)</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.SetSinMeterStatusAticPKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String}@,System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            安全认证
            </summary>
            <param name="Meteraddress"></param>
            <param name="passWord"></param>
            <param name="handleCode"></param>
            <param name="OutRand1"></param>
            <param name="OutEndata1"></param>
            <param name="PutDiv"></param>
            <param name="OutRand2"></param>
            <param name="OutESAMNo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.EncryptUpdateKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            2013新的方式密钥下装，一次4条密钥指令下发，共20条密钥。
            </summary>
            <param name="dicCommAddrs">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="dicOutData">加密机返回的密钥信息，4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.SetMacAdress(System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String},System.String)">
            <summary>
            设置电表蓝牙MAC地址
            </summary>
            <param name="Meteraddress"></param>
            <param name="localMacAddr"></param>
            <param name="salaveMacAddr"></param>
            <param name="power"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit.SetMeterInfraredStatusAticPKey(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,System.String,System.String,System.Collections.Generic.Dictionary{System.Int32,System.String},System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            红外安全认证（串口服务器转换板）
            </summary>
            <param name="Meteraddress">表地址</param>
            <param name="passWord">密码</param>
            <param name="handleCode">操作者代码</param>
            <param name="dataCode">数据标识</param>
            <param name="OutEndata1">密文1</param>        
            <param name="Data">返回数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.SatartRevLinkFrameThread(System.String)">
            <summary>
            开启一个线程处理预链接（登录和心跳）
            </summary>
            <param name="Address">终端地址</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.StopRevLinkFrameThread">
            <summary>
            关闭登录和心跳
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.RevEquipMentData698s(System.Boolean,System.Int32)">
            <summary>
            蓝牙模块返回数据处理 
            </summary>
            <param name="fg">读取模块频点 为true</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.SetBuildBluetoothInstruct(System.Collections.Generic.Dictionary{System.Int32,System.String},CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel)">
            <summary>
            智芯蓝牙盒子指令发送
            </summary>
            <param name="commAddrs"></param>
            <param name="pulseType"></param>
            <param name="Functioncode"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.HexStrToBytsDataA(System.String,System.Int32)">
            <summary>
            16进制字符串转字节数组
                【"123" => {0x01,0x23}】
            </summary>
            <param name="strData">16进制字符串</param>
            <param name="aryLen">数组长度</param>
            <returns>字节数组</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.MeterTransit698.AdjustmentStrSize(System.String,System.Int32,System.Boolean)">
            <summary>
            调整字符串大小
            </summary>
            <param name="origStr">待调字符串</param>
            <param name="desiredSize">调整的大小</param>
            <param name="isForward">是否前移</param>
            <returns>调整后的字符串</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel">
            <summary>
            脉冲切换板
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.#ctor(System.Int32)">
            <summary>
            脉冲切换板
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.RevEquipMentData(System.Int32,System.Int32,System.Boolean)">
            <summary>
            接收数据
            </summary>
            <param name="sendFrameLen">发送帧长度</param>
            <param name="revFrameCount">接收帧数量</param>
            <param name="isReturnOneFrame">是否只接收一帧数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.AbortAllFunction(System.Int32[],System.Byte,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            停止或者开始服务板相应功能
            </summary>
            <param name="meterIDs">表位号数组</param>
            <param name="actTag">动作类型[0-开始计算 1-停止计算]</param>
            <param name="chkType">检定类型</param>
            <param name="channel">通道号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.InteractiveRadioAutoSetAddr(System.Int32[])">
            <summary>
            交互广播自动设置地址
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.GetCurrentPluseCount(System.Int32@,System.Int32@)">
            <summary>
            获取当前脉冲计数
            </summary>
            <param name="state">状态[0-计数未完成 1到达计数]</param>
            <param name="currentPluse">当前脉冲计数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.SetMagneticHoldingRelayPins(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            设置磁保持继电器引脚
            </summary>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.MagneticHoldingRelayControl(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            磁保持继电器控制
            </summary>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.SetPulseCount(System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            设置脉冲计数
            </summary>
            <param name="pulseCount">脉冲数</param>
            <param name="clServicePanelMhrPortPins">服务板继电器端口引脚信息列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.PluseTogglePanel.GetPanelCFigure(System.Int32[],System.Int32)">
            <summary>
            服务板控制字处理
            </summary>
            <param name="meterIDs">操作表位集合</param>
            <param name="trayCount"></param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure">
            <summary>
            推压力装置设备
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.RevEquipMentData">
            <summary>
            等待接收数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.CommonSendInstructions(System.Int32,System.Int32,CLDC.CLAT.CLWBS.DataModel.Pressure.PressureModel@,System.Func{System.Int32,System.Int32,System.Byte[]},System.Func{System.Byte[],System.Byte[],System.Int32,System.Int32,CLDC.CLAT.CLWBS.DataModel.Pressure.PressureModel})">
            <summary>
            公共发送指令并解析数据 - 读功能
            </summary>
            <param name="MeterID">表位ID</param>
            <param name="outData">解析后的数据</param>
            <param name="GetCodeMethod">获取指令方法</param>
            <param name="AnalysisBufferMethod">解析数据方法</param>
            <returns>成功或失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.CommonSendInstructions(System.Int32,System.Int32,System.Func{System.Int32,System.Byte[]})">
            <summary>
            公共发送指令 - 写功能（不解析返回的数据帧）
            </summary>
            <param name="MeterID"></param>
            <param name="communicationID"></param>
            <param name="GetFrameMethod"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.CommonSendInstructions(System.Int32,System.Int32,System.Int32,System.Func{System.Int32,System.Int32,System.Byte[]})">
            <summary>
            公共发送指令-设置通讯地址 - 写功能（不解析返回的数据帧）
            </summary>
            <param name="MeterID"></param>
            <param name="communicationID"></param>
            <param name="newAddress"></param>
            <param name="GetFrameMethod"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.GetMeterPressureValue(System.Int32,System.Int32,CLDC.CLAT.CLWBS.DataModel.Pressure.PressureModel@)">
            <summary>
            获取压力值
            </summary>
            <param name="MeterId"></param>
            <param name="communicationID"></param>
            <param name="outData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.SetZeroValue(System.Int32,System.Int32)">
            <summary>
            手动置零 （不需要解锁）
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.SetAddressValue(System.Int32,System.Int32,System.Int32)">
            <summary>
            设置通讯地址
            </summary>
            <param name="MeterID"></param>
            <param name="communicationID"></param>
            <param name="newAddres"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Pressure.CloseOrOpenSystemConfigure(System.Int32,System.Int32,System.String)">
            <summary>
            解锁/锁定系统配置
            </summary>
            <param name="MeterId">表位ID</param>
            <param name="communicationID"></param>
            <param name="value">解锁：5AA5；其他任意值锁定</param>
            <returns></returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera._TxRestFrame">
            <summary>
            读取结果指令
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.#ctor(System.Int32)">
            <summary>
            青海CCD图像处理通讯
            </summary>
            <param name="type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.RevEquipMentData">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.StartPicProcess(System.Collections.Generic.List{System.String},System.Byte,System.String@)">
            <summary>
            开始处理图像
            </summary>
            <param name="lstCodes">表条码集合</param>
            <param name="bThick">高表还是矮表(0x01:高表, 0x00:矮表)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.QingHaiCamera.GetPicResult(System.Collections.Generic.List{System.Boolean}@,System.String@)">
            <summary>
            读取检测结果(0x11)	
            </summary>
            <param name="lstResults">检测结果集合</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Scan.DataBuf">
            <summary>
            接收缓存
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Scan.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.Scan.RevEquipMentData(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_IdentifyAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 2 字节后，补成模 16 的倍数再加 4 字节。 将密文写入到 ESAM 后存储格式为：1 字节明文数据长度 + 明文数据 DATA </param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013备用套电价参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)Apdu 中的 LC 的计算方法为：明文数据长度 + 4 字节</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <param name="pMsg">输出的错误信息</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_IncreasePurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013远程充值
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,表示输入的参数明文,包含购电金额、购电次数和户号；</param>
            <param name="OutData">输出的数据,购电金额+购电次数+MAC1+户号+MAC2</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_InitPurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,表示输入的参数明文,包含预置金额，4 字节， HEX 码</param>
            <param name="OutData">输出的数据,预置金额 + MAC1 + “00000000” + MAC2</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_DataClear1(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">输出的 20 字节密文+MAC</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_DataClear2(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
            达州自管 2013新事件或需量清零
             </summary>
             <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
             <param name="PutRand">输入的随机数2(字符型,长度8)</param>
             <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
             <param name="PutData">输入的清零数据</param>
             <param name="OutData">输出的 20 字节密文+MAC</param>
             <param name="pMsg">输出的错误信息</param>
             <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_InfraredRand(System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管红外认证请求函数
            </summary>
            <param name="PutRand">随机数</param>
            <param name="pMsg">输出的错误信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_MacCheck(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_DecreasePurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管钱包退费加密函数
            </summary>
            <param name="Flag"></param>
            <param name="PutRand"></param>
            <param name="PutDiv"></param>
            <param name="PutData">表示输入的 4 字节退费金额,字符型；</param>
            <param name="OutData">输出的密文和 MAC,字符型</param>
            <param name="pMsg">输出的错误信息</param>
            <param name="OutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SC_DZ13ServerDllImport.SC_DZ13_KeyUpdateV2(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            达州自管2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="OutData">N *（4 字节密钥信息+32 字节密钥密文）+ 4 字节 MAC，N不大于 4</param>
            <param name="pMsg">输出的错误信息</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport">
            <summary>
            加密机API
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.OpenUsbkey">
            <summary>
            获取加密服务器登陆权限
            </summary>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.LgServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密服务器
            </summary>
            <param name="ip">加密服务器IP</param>
            <param name="port">加密服务器端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">USBKEY密码</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.LgoutServer">
            <summary>
            断开与加密服务器的连接
            </summary>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.ClseUsbkey">
            <summary>
            释放加密服务器登陆权限
            </summary>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.IdentityAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.String)">
            <summary>
            获取随机数以及密文
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="NameId">调用者ID(推荐使用厂家或单位的名称)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程跳/合闸命令加密,用于形成跳/合闸命令的645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程二类参数设置加密函数,用于形成二类参数设置645命令 
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            电能表远程密钥信息清零
            </summary>
            <param name="Flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="rand">输入的随机数(字符型,长度8)</param>
            <param name="div">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="EsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的远程控制密钥信息明文(字符型)</param>
            <param name="Outkey1">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的远程控制密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.KeyUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取3条远程密钥和主控密钥的密钥信息和密钥密文,用于产生远程更新密钥的645命令。
            </summary>
            <param name="Counter">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.Getinfor(System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            用于产生密钥下装时向发卡仿真器发送报文的数据
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度16字符)</param>
            <param name="PutCardNo">输入的卡片序列号(字符型,长度16字符)</param>
            <param name="PutCardflag">??????</param>
            <param name="OutEnData">输出的密文(字符型,长度32字符)</param>
            <param name="OutData">输出的数据明文(字符型,长度16字符)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.ChangeKeyCard(System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            用于产生密钥下装时向发卡仿真器发送报文的数据
            </summary>
            <param name="PutCardinfo">输入的卡片信息(256位)</param>
            <param name="PutRand">输入的随机数(字符型,长度16字符)</param>
            <param name="PutCardNo">输入的卡片序列号,复位信息的后16位</param>
            <param name="OutDes">输出的认证密文(字符型,32位)</param>
            <param name="OutEnData1">输出的数据密文1(字符型,96位)</param>
            <param name="OutEnData2">输出的数据密文2(字符型,96位)</param>
            <param name="OutEnData3">输出的数据密文3(字符型,96位)</param>
            <param name="OutEnData4">输出的数据密文4(字符型,96位)</param>
            <param name="OutEnData5">输出的数据密文5(字符型,32位)</param>
            <param name="OutEnData6">输出的数据密文6(字符型,32位)</param>
            <param name="OutEnData7">输出的数据密文7(字符型,160位)</param>
            <param name="OutData">输出的数据明文(字符型,16位)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServerDllImport.MakeSetCard(System.String,System.String,System.String,System.String)">
            <summary>
            参数预制卡发卡函数,用于读卡器发行参数预置卡
            </summary>
            <param name="PutEnData1">输入的参数预置卡0001 文件数据(字符型)</param>
            <param name="PutEnData2">输入的参数预置卡0002 文件数据(字符型)</param>
            <param name="PutEnData3">输入的参数预置卡0003 文件数据(字符型)</param>
            <param name="PutEnData4">输入的参数预置卡0004 文件数据(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers">
            <summary>
            功能描述：
            作    者：
            编写时间：
            修改记录：
                        修改时间    修改人    修改内容
                        2014-2-10   谢恒      加密机升级，增加函数ParameterUpdate,ParameterElseUpdate,ParameterUpdate1,
                                                      ParameterUpdate2,Meter_Formal_IdentityAuthentication,
                                                      Meter_Formal_UserControl,Meter_Formal_ParameterUpdate,
                                                      Meter_Formal_ParameterElseUpdate,Meter_Formal_ParameterUpdate1,
                                                      Meter_Formal_ParameterUpdate2,Meter_Formal_InintPurse,
                                                      Meter_Formal_DataClear1,Meter_Formal_DataClear2,Meter_Formal_InfraredAuth,
                                                      Meter_Formal_MacCheck,Meter_Formal_KeyUpdateV2
                                               修改函数LoginServer，IdentityAuthentication，SI_IdentityAuthentication，UserControl
                        
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.LoginServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密机服务器
            </summary>
            <param name="ip">加密机IP</param>
            <param name="port">加密机端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">密码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.LgoutServer">
            <summary>
            断开与服务器的连接
            </summary>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ClseUsbkey">
            <summary>
            释放服务器登录权限
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Create_Rand(System.String@)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.SI_IdentityAuthentication(System.String,System.String@,System.String@,System.String@)">
            <summary>
            私钥认证
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand"></param>
            <param name="OutEndata"></param>
            <param name="PutDiv"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.UserControl(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            控制命令
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
             WinSocketServerDllImport.UserControl中参数"Flag"恒为0
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            远程二类参数设置加密函数,用于形成二类参数设置645命令 
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(需要特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>true:成功，false:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            清除密钥信息
            </summary>
            <param name="flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="rand">输入的随机数2(字符型,长度8)</param>
            <param name="div">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="EsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyInfo">输入的远程控制密钥信息明文(字符型)</param>
            <param name="Key1">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="KeyInfo1">输出的远程控制密钥信息(字符型,长度8)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.KeyUpdate(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥下载密文信息
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetFactor(System.Byte,System.String)">
            <summary>
            获取分散因子
            </summary>
            <param name="keyState">密钥状态</param>
            <param name="meterNum">表号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.Connect(System.String,System.String,System.String)">
            <summary>
            连接加密机
            </summary>
            <param name="ip"></param>
            <param name="port"></param>
            <param name="outTime"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.CreateRand(System.String@)">
            <summary>
            创建随机数
            </summary>
            <param name="OutRand1"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetInitSessionData(System.Int32,System.String,System.String,System.String@,System.String@,System.String@)">
            <summary>
            获取会话协商数据
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Asctr"></param>
            <param name="outRand"></param>
            <param name="outSessionData"></param>
            <param name="outMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetInitSessionDataJL(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@)">
            <summary>
            获取会话协商数据 计量芯
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Asctr"></param>
            <param name="outRand"></param>
            <param name="outSessionData"></param>
            <param name="outMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetVerifySessionData(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            会话协商验证
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Rand"></param>
            <param name="SessionData"></param>
            <param name="Sign"></param>
            <param name="SessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetVerifySessionDataJL(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            会话协商验证 计量芯
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="Rand"></param>
            <param name="SessionData"></param>
            <param name="Sign"></param>
            <param name="SessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.GetEncryptData(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            通用数据加密函数
            </summary>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="TaskType"></param>
            <param name="TaskData"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.VerifyMeterData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            电能表回复帧进行数据验证和解密。
            </summary>
            <param name="KeyState"></param>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="Data"></param>
            <param name="Mac"></param>
            <param name="outData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.KeyUpdate(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥更新
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.InitKey(System.Int32,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥初始化
            </summary>
            <param name="KeyState"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.PurseOperation(System.Int32,System.String,System.String,System.Int32,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            钱包操作
            </summary>
            <param name="OperationMode"></param>
            <param name="EasmId"></param>
            <param name="SessionKey"></param>
            <param name="TaskType">任务序编号，9 钱包初始化；10，钱包充值；11，钱包退费</param>
            <param name="TaskData"></param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
            
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009Servers.SetESAMData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            设置esam参数
            </summary>
            <param name="KeyState"></param>
            <param name="OperateMode"></param>
            <param name="EsamId"></param>
            <param name="SessionKey"></param>
            <param name="MeterNo"></param>
            <param name="ESAMRand"></param>
            <param name="Data"></param>
            <param name="OutSID"></param>
            <param name="OutAddData"></param>
            <param name="OutData"></param>
            <param name="OutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.LoginServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密机服务器
            </summary>
            <param name="ip">加密机IP</param>
            <param name="port">加密机端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">密码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.SI_IdentityAuthentication(System.String,System.String@,System.String@,System.String@)">
            <summary>
            私钥认证
            </summary>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand"></param>
            <param name="OutEndata"></param>
            <param name="PutDiv"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.UserControl(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            控制命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.KeyUpdate(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥下载密文信息
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            清除密钥信息
            </summary>
            <param name="flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="rand">输入的随机数2(字符型,长度8)</param>
            <param name="div">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="EsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyInfo">输入的远程控制密钥信息明文(字符型)</param>
            <param name="Key1">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="KeyInfo1">输出的远程控制密钥信息(字符型,长度8)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SJJ1009ServersOld.GetFactor(System.Byte,System.String)">
            <summary>
            获取分散因子
            </summary>
            <param name="keyState">密钥状态</param>
            <param name="meterNum">表号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.RevEquipMentData(System.String)">
            <summary>
            操作等待时间
            </summary>
            <param name="strEnd"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.SetStdMeterStartType(System.Byte)">
            <summary>
            设置标准表启动类型 "STP;DOS0(CR)";
            </summary>
            <param name="startType">启动类型(0.停止1.电能误差计算2.电能走字)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.SetStdMeterStall(System.Single,System.Single)">
            <summary>
            设置标准表档位
            </summary>
            <param name="vol">电压</param>
            <param name="cur">电流</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.SetEnergyModel">
            <summary>
            设置标准表走字模式
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.SetCheckModeAndLine(CLDC.CLAT.CLWBS.DataModel.Enum.EmStaMtrCheckMode,CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置SRS400.3测量模式档位
            </summary>
            <param name="mode">测量模式</param>
            <param name="sMode">档位</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.GetStdMeterConst(System.Double@)">
            <summary>
            获取标准表常数
            </summary>
            <param name="constant">标准表脉冲常数</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            获取标准表监视数据
            </summary>
            <param name="monitorData">标准表监视数据</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.GetStdMeterEnergy(System.Single@)">
            <summary>
            获取标准表累加电量
            </summary>
            <param name="Energy">标准表电量</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SRS400.GetStdMtrFrency(System.Single@)">
            <summary>
            读取频率
            </summary>
            <param name="Frency">频率</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.S500TH">
            <summary>
            温湿度仪
            </summary>
            <summary>
            温湿度仪
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.S500TH.#ctor">
            <summary>
            温湿度仪器的通讯方式：TCP，udp，串口获取温湿度数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.S500TH.ReadHumAndTemp(System.Single@,System.Single@,System.Int32@)">
            <summary>
            读取温湿度数据
            </summary>
            <param name="Temp"></param>
            <param name="Hum"></param>
            <param name="controlId">控制器ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.S500TH.WaitData">
            <summary>
            等待接收数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.S500TH.RevData(System.Byte[])">
            <summary>
            读取数据
            </summary>
            <param name="Buff"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck">
            <summary>
            温湿度仪
            </summary>
            <summary>
            温湿度仪
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck.#ctor">
            <summary>
            温湿度仪器的通讯方式：TCP，udp，串口获取温湿度数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck.ReadHumAndTemp(System.Single@,System.Single@,System.Int32@)">
            <summary>
            读取温湿度数据
            </summary>
            <param name="Temp"></param>
            <param name="Hum"></param>
            <param name="controlId">控制器ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck.ParseTempHum(System.Single@,System.Single@,System.Byte[])">
            <summary>
            解析数据帧
            </summary>
            <param name="Temp">温度</param>
            <param name="Hum">湿度</param>
            <param name="Frame">设备返回数据帧</param>
            <returns>帧解析结果 true：成功，false：失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck.WaitData">
            <summary>
            等待接收数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.TempHumCheck.RevData(System.Byte[])">
            <summary>
            读取数据
            </summary>
            <param name="Buff"></param>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.VirtualMeter">
            <summary>
            模拟表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.VoltageMeter.RevData(System.Byte[])">
            <summary>
            接收端口数据
            </summary>
            <param name="Buff">数据</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.VoltageMeter.RevEquipMentData(System.Int32)">
            <summary>
            操作等待时间
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.VoltageMeter.VoltageTextControl(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32@)">
            <summary>
            冲击电压控制
            </summary>
            <param name="VoltageValue">电压控制赋值[0-手调状态下的程控输出 1-12对应控制冲击电压为1KV-12KV,13停止试验]</param>
            <param name="Num">冲击次数</param>
            <param name="SpaceTime">间隔时间</param>
            <param name="WorkMode">工作模式[0-正极性测试 1-负极性测试]</param>
            <param name="BreakDownNum">击穿次数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.ConnectDevice(System.String,System.String,System.String)">
            <summary>
            连接加密机
            </summary>
            <param name="cIP">IP地址</param>
            <param name="cPort">端口号</param>
            <param name="cTime">超时时间</param>
            <returns>0：成功，1：失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Formal_GetRandHost(System.Text.StringBuilder)">
            <summary>
            用于从主站获取随机数
            </summary>
            <param name="cOutRandHost">输出随机数16字节</param>
            <returns>0：成功，1：失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取主站会话协商数据
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cASCTR">应用会话计数器可从芯片读取，系统须存储，每次会话必须必上次大1</param>
            <param name="cFLG">"01"</param>
            <param name="cOutRandHost">主站随机数 16bytes</param>
            <param name="cOutSessionInit">会话协商数据</param>
            <param name="cOutMac">对会话协商数据计算的mac 值，4Byte；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_JL_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取主站会话协商数据 计量芯
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cDiv">表号</param>
            <param name="cASCTR">应用会话计数器可从芯片读取，系统须存储，每次会话必须必上次大1</param>
            <param name="cFLG">"01"</param>
            <param name="cOutRandHost">主站随机数 16bytes</param>
            <param name="cOutSessionInit">会话协商数据</param>
            <param name="cOutMac">对会话协商数据计算的mac 值，4Byte；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数 16bytes</param>
            <param name="cSessionData">电能表返回的应用会话协商数据，48Byte</param>
            <param name="cSign">电能表返回的应用会话协商数据签名，4Byte</param>
            <param name="cOutSessionKey">：会话密钥，176Byte</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_JL_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证 计量芯
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数 16bytes</param>
            <param name="cSessionData">电能表返回的应用会话协商数据，48Byte</param>
            <param name="cSign">电能表返回的应用会话协商数据签名，4Byte</param>
            <param name="cOutSessionKey">：会话密钥，176Byte</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_VerifyReadData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            抄读数据验证
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cRandHost">主站随机数，16Byte 抄读数据时主站下发</param>
            <param name="cReadData">抄读数据</param>
            <param name="cMac">MAC 数据</param>
            <param name="cOutData">返回的抄读明文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_GetSessionData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取下发参数数据
            </summary>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskType">参数类型：4，安全模式设置、设置会话时效门限；5，电价设、电价切换时间、费率时段、对时任务设置；6，除拉闸外的控制任务设置；8，拉闸任务设置；3，除上述操作外的操作。</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID">安全标示</param>
            <param name="cOutAttachData">数据附加</param>
            <param name="cOutData">明文或数据</param>
            <param name="cOutMAC">数据验证码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_GetMeterSetData(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
             通用数据加密函数，写 ESAM 操作和钱包操作数据下发通过此函数进行安
             </summary>
             <param name="cOperateMode">操作模式</param>
             <param name="cESAMID">ESAM 序列</param>
             <param name="cSessionKey">会话密钥，176Byte</param>
             <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID">安全标示</param>
             <param name="cOutAttachData">数据附加</param>
             <param name="cOutData">明文或数据</param>
             <param name="cOutMAC">数据验证码</param>
             <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_VerifyMeterData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskData">数据</param>
            <param name="cMac">mac</param>
            <param name="cOutData">数据明文</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_SetESAMData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
             <summary>
             设置 ESAM 参数:用于设置表号、当前套电价文件、备用套电价文件、ESAM 存储标识。
             </summary>
             <param name="InKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
             <param name="InOperateMode">操作模式</param>
             <param name="cEsamId">ESAM 序列</param>
             <param name="cSessionKey">会话密钥，176Byte</param>
             <param name="cMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
             <param name="cESAMRand"></param>
             <param name="cData">4ByteOAD + 1Byte 内容LEN + 内容</param>
            <param name="OutSID">安全标示</param>
             <param name="OutAttachData">数据附加</param>
             <param name="OutData">明文或数据</param>
             <param name="OutMAC">数据验证码</param>
             <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_GetTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            电能表对称密钥更新
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="InMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
            <param name="cKeyType">密钥类型，00 应用密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData">电能表对称密钥数据</param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_InitTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            电能表对称密钥初始化
            </summary>
            <param name="iKeyState">对称密钥状态：0，出厂密钥；1，正式密钥</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="InMeterNum">表号，8Byte，不够8Byte 前面填充0</param>
            <param name="cKeyType">密钥类型，00 应用密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData">电能表对称密钥数据</param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Meter_Formal_GetPurseData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            钱包操作函数
            </summary>
            <param name="cOperateMode">操作模式</param>
            <param name="cESAMID">ESAM 序列</param>
            <param name="cSessionKey">会话密钥，176Byte</param>
            <param name="cTaskType">任务序编号，9 钱包初始化；10，钱包充值；11，钱包退费</param>
            <param name="cTaskData">数据明文，包含预置金额，4Byte</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData"></param>
            <param name="cOutMAC"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_InitSession(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            主站会话协商终端（数字签名连接认证机制，用于主站与设备进行会话协商时产生密文和签名数据，该过程在建立应用连接时完成。）
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cASCTR">计数器</param>
            <param name="cFLG">应用密钥产生标识，1Byte，默认”01”；</param>
            <param name="cMasterCert">主站证书；</param>
            <param name="cOutRandHost">主站随机数（16Byte）；</param>
            <param name="cOutSessionInit">会话协商数据（32Byte），建立应用连接中的密文1；</param>
            <param name="cOutSign">协商数据签名(64Byte) ，建立应用连接中的客户机签名1；</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_VerifySession(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            主站会话协商验证(数字签名连接认证机制，用于主站验证设备会话协商时返回的数据，验证成功主站产生会话密钥。)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cRandHost">主站随机数R1（16Byte）</param>
            <param name="cSessionData">终端返回的应用会话协商数据(48Byte)，对应建立应用连接中的密文</param>
            <param name="cSign">终端返回的应用会话协商数据签名(64Byte)，对应建立应用连接中的签名数据2；</param>
            <param name="cTerminalCert">终端证书</param>
            <param name="cOutSessionKey">会话密钥</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_VerifyReadData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            抄读数据验证(主站验证设备返回的抄读数据，具体指抄读终端返回的数据)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cRandHost">主站随机数(16Byte)</param>
            <param name="cReadData">抄读数据</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文抄读数据，iOperateMode=1，为空</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_VerifyReportData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            上报数据验证(设备主动上报数据时，主站验证数据的合法性)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cRandT">终端随机数(12B)</param>
            <param name="cReportData">上报数据</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文数据，iOperateMode=1，为空</param>
            <param name="cOutRSTCTR">主动上报随机数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_GetResponseData(System.Int32,System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            上报数据返回报文加密(用于设备主动上报主站返回帧数据加密计算)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="RandHost">上报随机数，12Byte</param>
            <param name="cReportData">上报数据</param>
            <param name="OutSID"></param>
            <param name="OutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="ucOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_GetSessionData(System.Int32,System.String,System.String,System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            安全传输加密(用于对具体业务数据进行数据加密计算)
            </summary>
            <param name="iOperateMode">操作模式</param>
            <param name="cESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTaskType">参数类型(4，安全模式参数，会话时效；7，拉闸；8，文件传输。3，除上述操作外的数据加密。)</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="cOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_VerifyTerminalData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            安全传输解密(用于验证终端返回帧数据解密验证)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTaskData">数据明文；NByte</param>
            <param name="cMac">MAC数据</param>
            <param name="cOutData">明文数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_GetGrpBrdCstData(System.Int32,System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            广播数据加密(用于广播数据加密计算)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="iOperateMode">操作模式</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cBrdCstAddr">广播地址</param>
            <param name="AGSEQ">广播应用通信序列号，4Byte</param>
            <param name="cBrdCstData">广播数据明文；</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutData">明文数据</param>
            <param name="cOutMac"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_GetTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            终端对称密钥更新(用于对称密钥更新)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTerminalAddress">终端地址</param>
            <param name="cKeyType">密钥类型，00 应用密钥，01 链路密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData"></param>
            <param name="cOutMac">MAC数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_InitTrmKeyData(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            终端对称密钥初始化(用于对终端密钥进行初始化，会话计数器次数为1 时，须先对密钥进行初始化)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cTerminalAddress">终端地址</param>
            <param name="cKeyType">密钥类型，00 应用密钥，01 链路密钥</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutTrmKeyData"></param>
            <param name="cOutMAC">MAC数据</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServer698DllImport.Obj_Terminal_Formal_GetCACertificateData(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取证书信息(用于对终端通密钥进行初始化，会话计数器次数为1 时，须先对密钥进行初始化。)
            </summary>
            <param name="iKeyState">密钥状态</param>
            <param name="cTESAMID">ESAM序列号</param>
            <param name="cSessionKey">会话密钥</param>
            <param name="cCerType">证书类型</param>
            <param name="cOutSID"></param>
            <param name="cOutAttachData"></param>
            <param name="cOutCertificateData"></param>
            <param name="cOutMAC">MAC数据</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport">
            <summary>
            功能描述：  检测客户端接口，新升级加密机2013-12-25
            作    者：  谢恒
            编写日期：  2014.2.10
            修改记录：
                        修改时间      修改人      修改内容
                        
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.OpenUsbkey">
            <summary>
            打开加密狗
            </summary>
            <returns>0成功，其他失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.LgServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密服务器
            </summary>
            <param name="ip">加密服务器IP</param>
            <param name="port">加密服务器端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">USBKEY密码</param>
            <returns>[0-成功，其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.LgoutServer">
            <summary>
            断开与加密服务器的连接
            </summary> 
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ClseUsbkey">
            <summary>
            释放加密服务器登陆权限
            </summary>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Create_Rand(System.Text.StringBuilder)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1">输出的随机数1</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.IdentityAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.String)">
            <summary>
            获取随机数以及密文
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)公钥下，函数内部分散因子默认为"0000000000000001"</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="NameId">调用者ID(推荐使用厂家或单位的名称)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程跳/合闸命令加密,用于形成跳/合闸命令的645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程二类参数设置加密函数,用于形成二类参数设置645命令
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(需要特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>0:成功，其他:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>0:成功，其他:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            密钥信息清零
            </summary>
            <param name="Flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥信息明文(字符型)</param>
            <param name="Outkey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.KeyUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取3条远程密钥和主控密钥的密钥信息和密钥密文,用于产生远程更新密钥的645命令。
            </summary>
            <param name="Counter">0:修改，1:恢复(需特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.WinSocketServerDllImport.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.LoginServer(System.String,System.UInt16,System.Int32,System.String)">
            <summary>
            登陆加密机服务器
            </summary>
            <param name="ip">加密机IP</param>
            <param name="port">加密机端口</param>
            <param name="nPwdLen">密码长度</param>
            <param name="pPwd">密码</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.LgoutServer">
            <summary>
            断开与服务器的连接
            </summary>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Create_Rand(System.String@)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.IdentityAuthentication(System.Int32,System.String,System.String@,System.String@,System.String@)">
            <summary>
            安全认证
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="MeterAddress">表地址</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="PutDiv">输出分散因子</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.UserControl(System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            控制命令
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
             SouthEncryptDllImport.UserControl中参数"Flag"恒为0
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功，false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>true:成功，false:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            清除密钥信息
            </summary>
            <param name="flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="rand">输入的随机数2(字符型,长度8)</param>
            <param name="div">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="EsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyInfo">输入的远程控制密钥信息明文(字符型)</param>
            <param name="Key1">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="KeyInfo1">输出的远程控制密钥信息(字符型,长度8)</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.KeyUpdate(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@,System.String@)">
            <summary>
            密钥下载密文信息
            </summary>
            <param name="PutRand">输入的随机数(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_IdentityAuthentication(System.Int32,System.String,System.String@,System.String@)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_DataClear1(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_InfraredAuth(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.String@,System.String@)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.Meter_Formal_KeyUpdateV2(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String@)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.SwichChargeMode(System.Int32,System.String,System.String,System.String,System.String@)">
            <summary>
            费控模式切换
            </summary>
            <param name="Flag">电表秘钥状态：1</param>        
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入参数明文，包含费控模式状态字+购电金额+购电次数</param>        
            <param name="OutData">输出：费控模式状态字+4字节MAC1+购电金额+购电次数+MAC2</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncrypServers.GetFactor(System.Byte,System.String)">
            <summary>
            获取分散因子
            </summary>
            <param name="keyState">密钥状态</param>
            <param name="meterNum">表号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.OpenDevice(System.String,System.String,System.UInt16,System.Int32)">
            <summary>
            登陆加密服务器
            </summary>
            <param name="type">加密机类型</param>
            <param name="ip">加密服务器IP</param>
            <param name="port">加密服务器端口</param>
            <param name="nPwdLen">超时时间</param>
            <returns>[0-成功，其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.CloseDevice">
            <summary>
            释放加密服务器登陆权限
            </summary>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.InfraredRand(System.Text.StringBuilder)">
            <summary>
            用于产生随机数，也可以不调用本函数自己产生随机数
            </summary>
            <param name="OutRand1">输出的随机数1</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.IdentityAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.String)">
            <summary>
            获取随机数以及密文
            </summary>
            <param name="Flag">0: 生产密钥状态;1: 交易密钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)公钥下，函数内部分散因子默认为"0000000000000001"</param>
            <param name="OutRand">输出的随机数(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <param name="NameId">调用者ID(推荐使用厂家或单位的名称)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程跳/合闸命令加密,用于形成跳/合闸命令的645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            远程一类参数MAC计算函数,用于形成一类参数设置645命令
            </summary>
            <param name="Flag">恒为0</param>
            <param name="PutRand">输入的随机数(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16, "0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.ParameterElseUpdate(System.Int32,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            远程二类参数设置加密函数,用于形成二类参数设置645命令
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(需要特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>0:成功，其他:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>0:成功，其他:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.ClearKeyInfo(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            密钥信息清零
            </summary>
            <param name="Flag">当前密钥状态(0:公钥状态下清零 1:私钥状态下清零)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥信息明文(字符型)</param>
            <param name="Outkey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.KeyUpdate(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            获取3条远程密钥和主控密钥的密钥信息和密钥密文,用于产生远程更新密钥的645命令。
            </summary>
            <param name="Counter">0:修改，1:恢复(需特殊授权)</param>
            <param name="PutRand">输入的随机数2(字符型,长度16)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutKeyinfo1">输入的主控密钥密钥信息明文(字符型)</param>
            <param name="PutKeyinfo2">输入的远程控制密钥信息明文(字符型)</param>
            <param name="PutKeyinfo3">输入的二类参数设置密钥信息明文(字符型)</param>
            <param name="PutKeyinfo4">输入的远程身份认证密钥信息明文(字符型)</param>
            <param name="OutKey1">输出的主控密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo1">输出的主控密钥信息(字符型,长度8)</param>
            <param name="OutKey2">输出的远程控制密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo2">输出的远程控制密钥信息(字符型,长度8)</param>
            <param name="OutKey3">输出的二类参数设置密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo3">输出的二类参数设置密钥信息(字符型,长度8)</param>
            <param name="OutKey4">输出的远程身份认证密钥密文(字符型,长度64)</param>
            <param name="OutKeyinfo4">输出的远程身份认证密钥信息(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.IdentityAuthentication(System.Int32,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新身份认证函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态(整型)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)分散因子为实际表号</param>
            <param name="OutRand">输出的随机数1(字符型,长度16)</param>
            <param name="OutEndata">输出的密文(字符型,长度16)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_UserControl(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新控制命令加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,复位信息的后8字节(字符型,长度16)</param>
            <param name="PutData">跳闸或合闸控制命令明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_ParameterUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新一类参数MAC计算函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">一类参数设置的写Esam命令头(字符型,长度10)</param>
            <param name="PutData">输入的一类参数明文(字符型)</param>
            <param name="OutEndata">输出的MAC数据(字符型,长度8)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_ParameterElseUpdate(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新二类参数加密函数
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的二类参数明文(字符型)</param>
            <param name="OutEndata">输出的密文(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_ParameterUpdate1(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新第一套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第一套费率参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_ParameterUpdate2(System.Int32,System.String,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新第二套费率电价设置
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的指令数据(字符型,长度10)</param>
            <param name="PutData">输入的第二套费率参数或当前套电价参数明文(字符型)</param>
            <param name="OutEndata">输出的明文和MAC(字符型)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_InintPurse(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新钱包初始化
            </summary>
            <param name="Flag">0:公钥状态(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的数据明文,包含预置金额</param>
            <param name="OutData">输出的数据,预置金额+MAC1+"00000000"+MAC2</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.DataClear1(System.Int32,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新电表清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_DataClear2(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder)">
            <summary>
            2013新事件或需量清零
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态,需要特殊授权(整型)</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入的清零数据</param>
            <param name="OutData">清零密文(20字节)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.InfraredAuth(System.Int32,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新红外认证
            </summary>
            <param name="Flag">0:公钥状态</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号,电能表红外查询命令返回的(字符型,长度16)</param>
            <param name="PutRand1">随机数1,创建随机数函数返回(长度16)</param>
            <param name="PutRand1Endata">随机数1密文,电能表红外查询命令返回的(长度16)</param>
            <param name="PutRand2">随机数2,电能表红外查询命令返回的(长度16)</param>
            <param name="OutRand2Endata">随机数2密文(长度16)</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.Meter_Formal_MacCheck(System.Int32,System.String,System.String,System.String,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新数据回抄
            </summary>
            <param name="Flag">0:公钥状态，1:私钥状态</param>
            <param name="PutRand">输入随机数1的高4字节</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutApdu">输入的命令头，5字节(04D686+起始地址+Len),(Len为数据长度+0x0C)</param>
            <param name="OutData">数据回抄返回的数据</param>
            <param name="OutMac">4字节数据回抄返回的MAC</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.KeyUpdateV2(System.Int32,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            2013新密钥更新，2013的标准，电能表密钥更新本地表和远程表都采用通信方式完成，共20条密钥，需5次调用本函数，所得密钥分5次下发给电能表
            </summary>
            <param name="PutKeySum">密钥总条数,固定为20</param>
            <param name="PutKeystate">密钥状态"00"密钥恢复(需特殊授权)，"01"密钥下装</param>
            <param name="PutKeyid">密钥编号，0x00-0x13,每次最多输出4条密钥"00010203"</param>
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutEsamNo">输入的ESAM序列号(字符型,长度16)</param>
            <param name="PutChipInfor">芯片发行信息文件(001A 文件)数据，通过(078001FF)命令从电表ESAM抄读所得，005AH字节</param>
            <param name="OutData">输出4*(4字节密钥信息+32字节密钥密文)+4字节MAC</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.EquipMent.AllEquipMent.SouthEncryptDllImport.SwitchChargeMode(System.Int32,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder,System.Text.StringBuilder)">
            <summary>
            费控模式切换
            </summary>
            <param name="Flag">电表秘钥状态：1</param>        
            <param name="PutRand">输入的随机数2(字符型,长度8)</param>
            <param name="PutDiv">输入的分散因子(字符型,长度16,"0000"+表号)</param>
            <param name="PutData">输入参数明文，包含费控模式状态字+购电金额+购电次数</param>        
            <param name="OutData">输出：费控模式状态字+4字节MAC1+购电金额+购电次数+MAC2</param>
            <returns>[0-成功,其它-失败]</returns>
        </member>
    </members>
</doc>
