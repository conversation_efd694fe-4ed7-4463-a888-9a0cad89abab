﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
  </configSections>
  <connectionStrings>
    <add name="CLDC.CLAT.CLWBS.UI.Properties.Settings.CL3000ATConnectionString" connectionString="Provider=Microsoft.Jet.OLEDB.4.0;Data Source=|DataDirectory|\CL3000AT.mdb" providerName="System.Data.OleDb"/>
  </connectionStrings>
  <runtime>
    <gcConcurrent enabled="true"/>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <publisherPolicy apply="yes"/>
      <probing privatePath="WCF"/>
      <!--<probing privatePath="CTP" />-->
      <!--<probing privatePath="CTPChangSha" />-->
      <!--<probing privatePath="CTPQingHai" />-->
      <!--<probing privatePath="CTPTongLing" />-->
      <!--<probing privatePath="CTPWeiFang" />-->
    </assemblyBinding>
  </runtime>
  <appSettings>
<add key="EquipEncryptFlag" value="1" />
    <add key="IsClearEnergy" value="true"/>
    <add key="PowerOff" value="false"/>
    <!--启动是否舍弃第一个脉冲，济南特殊要求-->
    <add key="IsDiscardFirstPul" value="false"/>
    <add key="ControlClass" value="PressMeterControl"/>
    <add key="Business" value="CLDC.CLAT.CLWBS.Business"/>
    <add key="Control" value="CLDC.CLAT.CLWBS.Controls"/>
    <add key="EquipMent" value="CLDC.CLAT.CLWBS.EquipMent"/>
    <add key="PortManage" value="CLDC.CLAT.CLWBS.PortManage"/>
    
    <add key="TrialType" value="Config\\XML\\TrialTypeItems.xml"/>
    <add key="SingleTrialType" value="Config\\SinglePhase\\TrialTypeItems.xml"/>
    <add key="ThreeTrialType" value="Config\\ThreePhase\\TrialTypeItems.xml"/>
    <add key="TerminalTrialType" value="Config\\Terminal\\TrialTypeItems.xml"/>
    
    <add key="TrialType698" value="Config\\XML\\TrialTypeItems698.xml"/>
    <add key="SingleTrialType698" value="Config\\SinglePhase\\TrialTypeItems698.xml"/>
    <add key="ThreeTrialType698" value="Config\\ThreePhase\\TrialTypeItems698.xml"/>
    <add key="TerminalTrialType698" value="Config\\Terminal\\TrialTypeItems698.xml"/>
    
    <add key="Server" value="Config\\XML\\ServerConfig.xml"/>
    <add key="SingleServer" value="Config\\SinglePhase\\ServerConfig.xml"/>
    <add key="ThreeServer" value="Config\\ThreePhase\\ServerConfig.xml"/>
    <add key="TerminalServer" value="Config\\Terminal\\ServerConfig.xml"/>
    
    <add key="Oad" value="Config\\XML\\698\\OadInfosConfig.xml"/>
    
    <add key="System" value="Config\\XML\\SystemParam.xml"/>
    <add key="SingleSystem" value="Config\\SinglePhase\\SystemParam.xml"/>
    <add key="ThreeSystem" value="Config\\ThreePhase\\SystemParam.xml"/>
    <add key="TerminalSystem" value="Config\\Terminal\\SystemParam.xml"/>
    
    <add key="Ports" value="Config\\XML\\PortManage.xml"/>
    <add key="SinglePorts" value="Config\\SinglePhase\\PortManage.xml"/>
    <add key="ThreePorts" value="Config\\ThreePhase\\PortManage.xml"/>
    <add key="TerminalPorts" value="Config\\Terminal\\PortManage.xml"/>
    
    <add key="EquipMents" value="Config\\XML\\EquipMentManage.xml"/>
    <add key="SingleEquipMents" value="Config\\SinglePhase\\EquipMentManage.xml"/>
    <add key="ThreeEquipMents" value="Config\\ThreePhase\\EquipMentManage.xml"/>
    <add key="TerminalEquipMents" value="Config\\Terminal\\EquipMentManage.xml"/>
    
    <!--增加Modbus协议XML路径节点-->
    <add key="ModbusXMLPath" value="Config\\XML\\PressuerXml\\ModbusValue.xml"/>
    
    <add key="ControlManage" value="Config\\XML\\ControlManage.xml"/>
    <add key="SingleControlManage" value="Config\\SinglePhase\\ControlManage.xml"/>
    <add key="ThreeControlManage" value="Config\\ThreePhase\\ControlManage.xml"/>
    <add key="TerminalControlManage" value="Config\\Terminal\\ControlManage.xml"/>
    
    <add key="InterfaceType" value="Config\\XML\\InterfaceManage.xml"/>
    <add key="SingleInterfaceType" value="Config\\SinglePhase\\InterfaceManage.xml"/>
    <add key="ThreeInterfaceType" value="Config\\ThreePhase\\InterfaceManage.xml"/>
    <add key="TerminalInterfaceType" value="Config\\Terminal\\InterfaceManage.xml"/>
    
    <add key="tempFilePath" value="D:\LabelData.txt"/>
    <add key="columnsCount" value="4"/>
    <add key="showCount" value="12"/>
    <add key="ClientSettingsProvider.ServiceUri" value=""/>
    <add key="cameraCount" value="3"/>
    <add key="meterCount" value="6"/>
    <add key="delayTime" value="4"/>
    <add key="ReadTempRature" value="false"/>
    <add key="AlarmTempValue" value="80"/>
    <add key="ReadTempInterval" value="60"/>
    <add key="PowerOffTempValue" value="100"/>
    <add key="ccdClosePowerST" value="3"/>
    <add key="CCDModelName" value="DDS208"/>
    <!--多功能表-->
    <add key="MultiMeterPassWord" value="03000000"/>
    <!--福建用 ：上电后等待拍照的延时时间(毫秒)-->
    <add key="cameraSleepTime" value="3500"/>
    <!--<上海专用  启动实验表等级：2.0改为 1.0 >-->
    <add key="MeterStartLevelChange" value="false"/>
    <!--是否上传不合格图片-->
    <add key="IsSendBuHeGepicture" value="false"/>
    <!--是否调用外部相机程序-->
    <add key="UseExternalCCD" value="true"/>
    <!--电能表组ID  单相|三相直接接入式|三相互感式接入式|采集终端直接接入式|采集终端互感接入式-->
    <add key="CameraManageMeterIds" value="1|2|3|4|5"/>
    <!--雕刻标准值-->
    <add key="CarveStandardValue" value="已检"/>
    <!--主控系统编号-->
    <add key="ToCheckSystemId" value="123"/>
     <!--终端表位232端口-->
    <add key="TerminalCom" value="1"/>
    <!--信道是否默认-->
    <add key="XinDaoHeGe" value="true"/>
    <!--起动电流倍数-->
    <add key="StartIbTimes" value="1"/>
    <!--数据完整性等待时间-->
    <add key="SleepTime" value="10000"/>
    <!--发送标准表实时数据等待时间-->
    <add key="MonitorTime" value="30000"/>
    <!--是否副表检主表-->
    <add key="IsCheckStdMeter" value="true"/>
    <!--主表检副表 脉冲校验圈数-->
    <add key="AppPulseCount" value="10000"/>
    <!--是否高精度表SRS400.3-->
    <add key="IsSrsStdMtr" value="false"/>
    <!--标准表误差限值-->
    <add key="StdMeterErrLimit" value="0.05"/>
    <!--WCF应用的协议-->
    <add key="WCFContract" value="CLDC.Framework.WCF.Contract.IWCTPService"/>
    <!--数据包压缩的最小长度 （字节）-->
    <add key="CompressLenth" value="10240"/>
    <add key="ClientSendTimeout" value="60"/>
    <!--检测序列号等待时间-->
    <add key="WaitingTime" value="9000"/>
    <!--工位ID-->
    <add key="StaionId" value="2401"/>
    <!--与数据管理通信包该发送到主控或物流，-1主控和物流都发，0为主控，1为物流-->
    <add key="Monitor_Ask" value="0"/>
    <add key="State_Ask" value="203"/>
    <add key="Meter_Ask" value="0"/>
    <add key="Scheme_Ask" value="0"/>
    <add key="Result_Ask" value="0"/>
    <add key="Ready_Ask" value="203"/>
    <add key="Alarm_Ask" value="203"/>
    <!--配置设备名称-->
    <add key="EquiqName" value="CCDCamera(相机)|CCO(集中器本地模块)|STA(电表远程模块)|CL1000F(多功能板)|CLX09(功率源)|CLX115(标准表)|SRS400(高精度标准表)|CLX115_1(副标准表)|CL188L(误差板)|CL191B(时基源)|CL2029B_1(三色灯)|CL2029B(三色灯)|CL2029C(LED灯)|Meter(表位)|CL2038(耐压仪)|CL2103(电流采样板)|CL2103x3(三相电流采样板)|CL2046(三相电压源)|CL2043(续流板)|CL2031B(功耗板)|CL2041(载波)|CL3013(源表一体)|CL2101(多功能板)|CL2041A(载波)|CL2151(温度监视器)|CL2035(红外)|CL2030(CT)|SJJ1009Servers(加密机)|SJJ1009ServersOld(旧加密机)|Scan(条码扫描枪)|Controller(操控器)|VirtualMeter(模拟表)|CL321(模拟控制器)|AN9632(绝缘冲击仪)|VoltageMeter(电压冲击仪)|SouthEncrypServers(南网加密机)|SC_DZ13Servers(达州自管13加密机)|SC_STServers(水投规范加密机)|SC_WS09Servers(威盛09加密机)|Pressure(推压力)|MeterService(表位服务板)"/>
    <add key="EquiqVsControl" value="CCDCamera(相机)\ccdcamera|CCO(集中器本地模块)\cco|STA(电表远程模块)\sta|CL1000F(多功能板)\cl1000f|CLX09(功率源)\clx09|CLX115(标准表)\clx115|SRS400(高精度标准表)\srs400|CLX115_1(副标准表)\clx115_1|CL188L(误差板)\cl188l|CL191B(时基源)\cl191b|CL2029B(三色灯)\cl2029b|CL2029B_1(三色灯)\cl2029b_1|CL2029B(CCD功率源)\cl2029bpower|CL2029C(LED灯)\cl2029c|Meter(表位)\meter|CL2038(耐压仪)\cl2038|CL2103(电流采样板)\cl2103|CL2103x3(三相电流采样板)\CL2103|CL2046(三相电压源)\cl2046|CL2043(续流板)\cl2043|CL2031B(功耗板)\cl2031b|CL2041(载波)\cl2041|CL3013(源表一体)\cl3013|CL2101(多功能板)\cl2101|CL2041A(载波)\cl2041a|CL2151(温度监视器)\cl2151|CL2035(红外)\cl2035|CL2030(CT)\cl2030|SJJ1009Servers(加密机)\sjj1009servers|SJJ1009ServersOld(旧加密机)\sjj1009serversold|Scan(条码扫描枪)\scan|Controller(操控器)\Controller|VirtualMeter(模拟表)\virtualmeter|CL321(模拟控制器)\cl321|AN9632(绝缘冲击仪)\AN9632|VoltageMeter(电压冲击仪)\VoltageMeter|SouthEncrypServers(南网加密机)\SouthEncrypServers|SC_DZ13Servers(达州自管13加密机)\SC_DZ13Servers|SC_STServers(水投规范加密机)\SC_STServers|SC_WS09Servers(威盛09加密机)\SC_WS09Servers|Pressure(推压力)\Pressure|MeterService(表位服务板)\MeterService"/>
    <!--软件初始化界面时使用，切换国内南方界面 true表示南方电网 -->
    <add key="IsSouthSetworkStyle" value="true"/>
    <!--软件初始化界面时使用，显示单位LOG 0 南网 1 科陆 2科陆-->
    <add key="IsSouthClou" value="0"/>
    <!--台体初始化设备时反射用-->
    <add key="InitCellEquipMents" value="InitEquipMentThreePhase"/>
    <!--三相三线控源输出时是否调整相位角度（309C功率源输出时除B相相位其他需先减去30°再进行夹角计算）-->
    <add key="IsAdjustFactor" value="true"/>
    <!--与外部服务器通讯是反射用-->
    <add key="InitCommServerClass" value="ExternalCommProcessCls"/>
    <!--与外部服务器通讯是反射用(青海扫描枪使用)-->
    <!--<add key="InitCommServerClass" value="WCFQingHaiScan"/>-->
    <add key="InitCommSpaceName" value="CLDC.CLAT.CLWBS.COMM.XMLWCF"/>
    <add key="DLLPath" value="\CLDC.CLAT.CLWBS.COMM.XMLWCF.dll"/>
   <!--是否0.2A以下电流改手动，默认为true-->
    <add key="StandAutoToManu" value="true"/>
   <!--台体电压电流回路是否通过继电器控制-->
    <add key="IsOutputLoopByRelay" value="false"/>
    <!--参数验证是否升电流-->
    <add key="IsCheckParamtedSetPower" value="false"/>
    <!--清远主副表原有误差入口 false关闭  true打开-->
    <add key="IsShowOriginalErroe" value="false"/>
    <!--日志保存时间 单位-月-->
    <add key="SaveLogMonths" value="12"/>
    <!--基本误差电流输出稳定时间 单位秒-->
    <add key="PowerOnWaitTime" value="15"/>
   <!--5A以上电流点是否剔除第一个误差值-->
    <add key="IsRemoveFirstErrorValue" value="false"/>
   <!--表位是否为兼容国网南网 默认false-->
   <add key="IsCompatibleTest" value="false"/>
    <!--不合格表是否接着检 默认false-->
    <add key="IsContinueCheckNoPassMeter" value="false"/>
    <!--功率源是否采用SCPI协议 默认false-->
    <add key="IsPowerUseSCPI" value="false"/>
    <!--标准表是否采用SCPI协议 默认false-->
    <add key="IsStdMeterUseSCPI" value="false"/>
    <!--期间核查是否调用3L动态库 默认false-->
    <add key="IsCall3LDLL" value="true"/>
</appSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/></startup></configuration>
