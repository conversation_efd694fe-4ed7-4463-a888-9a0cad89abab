<?xml version="1.0"?>
<doc>
    <assembly>
        "UniControl.NET"
    </assembly>
    <members>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.SetIPConfiguration(System.UInt32,AlliedVisionTech.UniFoundationNet.IPConfigurationInfo)">
            <summary> set the IP configuration for the camera with the given ID (GigE cameras only).
The property SupportedConfigurationModes of the IPConfigurationInfo will be ignored.
The properties IPAddress, SubnetMask, and DefaultGateway of the IPConfigurationInfo 
will be ignored when the ConfigurationMode is not set to Persistent IP.</summary>
            <param name="cameraId"> ID of the camera to configure</param>
            <param name="value"> A valid IP configuration object</param>
            <returns> IP configuration object</returns>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.GetReturnCodeInfo(System.UInt32)">
            <summary>Maps UniControl Return codes to string.</summary>
            <param name="ReturnCode"> UniReturnCode </param>
            <returns> human readable return code description</returns>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.GetModuleInfo(&lt;unknown type&gt;)">
            <summary> get numeric info about a specific module.</summary>
            <param name="InfoType"> Type of module information</param>
            <returns> Numerical information about the chosen module</returns>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControl.Version">
            <summary>get the version of the underlying UniControl "C" API.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.WriteUserLog(System.UInt32,System.String)">
            <summary> writes a string into the user log.</summary>
            <param name="Level"> Log level for the message</param>
            <param name="Msg"> String to log</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.CreateUserLog(System.UInt32,System.String)">
            <summary> set path of the user log file.</summary>
            <param name="MaxLevel"> maximal logging level to be written to the log, from fatal[0] to debug[6]</param>
            <param name="FileName"> path to write the log to</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.DisconnectAllCameras">
            <summary> disconnects all open cameras from the API</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.ReconnectAllCameras(System.Boolean)">
            <summary> reconnect all former open cameras to the API</summary>
            <param name="AutoReLoadSettings"> Switch to determine if settings should automatically be reloaded after the reconnect</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.CloseAllCameras">
            <summary> closes all open cameras</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControl.LicenseType">
            <summary> get global license type of FireGrab.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.GetCameras">
            <summary> get list of all found cameras.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControl.ListAllCameras">
            <summary> get or set a flag whether GetCameras returns also busy cameras (FireWire) rsp. unreachable cameras (GigE).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControl.DetectExternallyControlledCameras">
            <summary> get or set a flag whether UniControl should raise a node list changed event whenever another application 
on the same or a different host gained or released control over one of the currently known cameras (GigE only).</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.Release">
            <summary> frees [un]managed resources and unregisters events.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.Init(AlliedVisionTech.UniControlNet.UniControl.enUniControlInit)">
            <summary> Initialize the underlying API and all control logic.</summary>
            <remarks> this has to be the first call to UniControl and has to be paired with Release.</remarks>
            <param name="mode"> initializing mode for Unicontrol</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControl.Init">
            <summary> Initialize the underlying API and all control logic.</summary>
            <remarks> this has to be the first call to UniControl and has to be paired with Release.</remarks>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnFrameError">
            <summary> Event will be triggered when an error has been detected while grabbing.
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameErrorHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnFrameDropped">
            <summary> Event will be triggered when a Frame has been dropped.
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameDroppedHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnFrameStart">
            <summary> Event will be triggered when the reception of a new image has started (FireWire only).
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameStartHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnFrameReady">
            <summary> Event will be triggered when a new image is available to the user.
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameReadyHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnBusReset">
            <summary> Event will be triggered when a 1394 bus reset occurs.
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnBusResetHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniControl.OnNodeListChanged">
            <summary> Event will be triggered when the node list changes. 
<see cref="T:AlliedVisionTech.UniControlNet.UniControl.OnNodelistChangeHandler" /> for the expected signature of event subscribers.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameErrorHandler">
            <summary> delegate used for FrameError events </summary>
            <param name="cam_id"> Camera ID</param>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameDroppedHandler">
            <summary> delegate used for FrameDropped events </summary>
            <param name="cam_id"> Camera ID</param>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameStartHandler">
            <summary> delegate used for FrameStart events </summary>
            <param name="cam_id"> Camera ID</param>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnFrameReadyHandler">
            <summary> delegate used for FrameReady events </summary>
            <param name="cam_id"> Camera ID</param>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnBusResetHandler">
            <summary> delegate used for BusReset events </summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.OnNodelistChangeHandler">
            <summary> delegate used for NodeListChanged events </summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.enUniControlInit.enUseFrameStart">
            <summary>initializing with registering FrameStart notification</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.enUniControlInit.enNotSet">
            <summary> default init mode</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.enUniControlInit">
            <summary> types of initialization options for the UniAPI</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.FrameError">
            <summary> a frame error has been detected</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.FrameDropped">
            <summary> frame has been dropped</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.FrameStart">
            <summary> reception of a new image has started (FireWire only)</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.FrameReady">
            <summary> a new frame is available to the user</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.BusReset">
            <summary> a 1394 bus reset event occurred</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniControl.UniEventType.NodeListChanged">
            <summary> the node list has changed</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl.UniEventType">
            <summary> types of events supported by the UniAPI</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControl">
            <summary> API Control class controlling camera independent functionality.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.EventData">
            <summary> private event class.</summary>
private Event implementation
</member>
        <member name="M:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Update">
            <summary> Writes the trigger settings stored in the properties into the camera.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Refresh">
            <summary> Fills the properties with the current trigger settings from the camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Value">
            <summary> Trigger value. Setting this value will not be written into the camera immediately, please use the Update () method after changing the properties.</summary>
            <seealso cref="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo" />
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Mode">
            <summary> Trigger mode. Setting this value will not be written into the camera immediately, please use the Update () method after changing the properties.</summary>
            <seealso cref="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo" />
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Active">
            <summary> State of the trigger. Setting this value will not be written into the camera immediately, please use the Update () method after changing the properties.</summary>
            <seealso cref="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo" />
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.TriggerCtrlInfo.Invert">
            <summary> Polarity of the trigger (inversion). Setting this value will not be written into the camera immediately, please use the Update () method after changing the properties.</summary>
            <seealso cref="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo">
            <summary> Camera trigger control </summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.CopyTo(System.Array,System.Int32)">
            <summary>Copies the elements of the ICollection to an Array, starting at a particular Array index.</summary>
            <param name="index">The one-dimensional Array that is the destination of the elements copied from ICollection.</param>
            <param name="index">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.SyncRoot">
            <summary>Gets an object that can be used to synchronize access to the ICollection.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.IsSynchronized">
            <summary>Gets a value indicating whether access to the ICollection is synchronized (thread safe).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.Count">
            <summary>Gets the number of elements contained in the ICollection.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection.Item(System.Int32)">
            <summary>Get or set infos about the selected input pin.</summary>
            <param name="index">Number of the input pin to get / set.</param>
            <seealso cref="T:AlliedVisionTech.UniFoundationNet.OutputCtrlInfo" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.OutputCtrlInfoCollection">
            <summary> OutputCtrlInfoCollection.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.CopyTo(System.Array,System.Int32)">
            <summary>Copies the elements of the ICollection to an Array, starting at a particular Array index.</summary>
            <param name="index">The one-dimensional Array that is the destination of the elements copied from ICollection.</param>
            <param name="index">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.SyncRoot">
            <summary>Gets an object that can be used to synchronize access to the ICollection.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.IsSynchronized">
            <summary>Gets a value indicating whether access to the ICollection is synchronized (thread safe).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.Count">
            <summary>Gets the number of elements contained in the ICollection.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.GetEnumerator">
            <summary>Returns an enumerator that iterates through a collection.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection.Item(System.Int32)">
            <summary>Get or set infos about the selected input pin.</summary>
            <param name="index">Number of the input pin to get / set.</param>
            <seealso cref="T:AlliedVisionTech.UniFoundationNet.InputCtrlInfo" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.InputCtrlInfoCollection">
            <summary> InputCtrlInfoCollection.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.LutLoadFromCSV(System.String,System.UInt32,System.UInt32)">
            <summary> load LUT from csv file.</summary>
            <param name="path"> String containing the LUT location</param>
            <param name="LutNr"> the number of the target LUT to use</param>
            <param name="CsvPos"> the number (column) of the LUT set to load
 (for LUT files containing multiple sets)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.LutLoadFromCSV(System.String)">
            <summary> load LUT from csv file</summary>
            <param name="path"> String containing the LUT location</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ParameterUpdateEnd">
            <summary> apply all register changes to camera </summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ParameterUpdateBegin">
            <summary> start the registers update</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MaxRegisterListElements">
            <summary> get the maximum number of register list elements </summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetRegisterList(AlliedVisionTech.UniFoundationNet.UniRegisterListItem[])">
            <summary> set a list of registers to transfer to the camera</summary>
            <param name="Registers"> Array of registers to transfer to the camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetFeatureList(AlliedVisionTech.UniFoundationNet.UniFeatureListItem[])">
            <summary> set a list of features to the camera</summary>
            <param name="Features"> Array of features to transfer to the camera</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_Count">
            <summary> user profile count supported by the camera</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_Current">
            <summary> currently set user profile</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_ErrorStatus">
            <summary> user profile error status</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_SetDefault(System.UInt32)">
            <summary> set the default user profile</summary>
            <param name="ProfileID"> user profile to set as default profile</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_Save(System.UInt32)">
            <summary> save current camera setup to user profile</summary>
            <param name="ProfileID"> target user profile</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.UserProfile_Load(System.UInt32)">
            <summary> load user profile</summary>
            <param name="ProfileID"> user profile to load</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.AutoFunctionAOI">
            <summary> Set/Get the AutoFunction AOI and mode</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_Receive(System.Byte[],System.UInt32)">
            <summary>receive date from the serial IO interface</summary>
            <param name="Data"> data to be received </param>
            <param name="Timeout"> timeout for transmit</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_Transmit(System.Byte[],System.UInt32)">
            <summary>transmit date with the serial IO interface</summary>
            <param name="Data"> data to be send </param>
            <param name="Timeout"> timeout for transmit</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_Mode">
            <summary>receive/transmit/duplex mode of the serial IO</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_StopBits">
            <summary>stop bits of the serial IO</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_Parity">
            <summary>parity of the serial IO</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_CharType">
            <summary> character encoding of the serial IO. </summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_BaudRate">
            <summary> baud rate of the serial IO. </summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_RxBytesPending">
            <summary> Byte ready to receive in the internal serial IO buffer. </summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_RxReady">
            <summary>get the cameras receive buffer state. </summary>
            <remarks>if the camera has received serial date this returns true.</remarks>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialIO_Config">
            <summary> Get/Set the configuration of the serial IO.</summary>
            <param name="value"> UniSioConfig to set to camera </param>
            <seealso cref="T:AlliedVisionTech.UniFoundationNet.UniSioConfig" />
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.Trigger">
            <summary> Get/set camera trigger settings.</summary>
            <seealso cref="T:AlliedVisionTech.UniControlNet.TriggerCtrlInfo" />
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.OutputPin">
            <summary> camera output pin control</summary>
            <returns>            returns a collection of the current pin info </returns>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.InputPin">
            <summary> camera input pin control</summary>
            <returns>            returns a collection of the current pin info </returns>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.TestImage">
            <summary> currently used test image.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.TestImageCount">
            <summary> supported number of test images.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.WhiteBalance">
            <summary> White balance control</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.WhiteBalanceType">
            <summary> type of supported white balance. </summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ColorCorrectionMatrix">
            <summary>Get or set the color correction matrix.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HueValue">
            <summary>Get or set the value in degrees for hue feature.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HueUnitSize">
            <summary>Get the unit size in degrees for hue feature.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HueMax">
            <summary>Get the maximum value in degrees for hue feature.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HueMin">
            <summary>Get the minimum value in degrees for hue feature.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeBandwidth">
            <summary> get free band width in 1 per 1000.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.DataBitRateMax">
            <summary> get maximum supported data bit rate for image streaming.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.DataBitRate">
            <summary> get/set current data bit rate for image streaming.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.StreamSpeedMax">
            <summary> get maximum supported iso stream speed.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.StreamSpeed">
            <summary> get/set current iso streamspeed.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ShadingUpload(System.Drawing.Bitmap,System.Boolean)">
            <summary> load image into camera.</summary>
            <param name="Image"> shading image to upload, has to be 8bit</param>
            <param name="Blocking"> wait for finished or use OnAsyncReady for notification</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ShadingDownload(System.Drawing.Bitmap,System.Boolean)">
            <summary> download the shading image.</summary>
            <param name="Image"> shading image as Bitmap</param>
            <param name="Blocking"> wait for finish or use OnAsyncReady for notification</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ShadingBuild(System.UInt32,System.Boolean)">
            <summary> build shading image.</summary>
            <param name="FrameCount"> number of image used to build the shading image</param>
            <param name="Blocking"> wait till finished? use delegate OnAsyncReady for notification</param>
            <remarks> FrameCount has to be a power of 2</remarks>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ShadingImageSize">
            <summary> Shading image size. </summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.LutUpload(System.UInt32,System.Byte[])">
            <summary> write lookup table to camera.</summary>
            <param name="LutNr"> Camera LUT to write </param>
            <param name="Values"> LUT to write</param>
            <remarks> all LUTs are Byte arrays, with an 16bit LUT the values have to be in little endian (Intel)</remarks>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutCurPos">
            <summary> current lookup table.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutActivated">
            <summary> set/ get the LUT activation state.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutMaxDataSize">
            <summary> size of the lookup table in bytes.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutValueCount">
            <summary> maximum number of lookup table entries.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutMaxValue">
            <summary> maximum value for a lookup table entry.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LutCount">
            <summary> number of camera supported Lookup tables</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HasLut">
            <summary> true if the camera supports lookup table(s).</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetFeatureMax(&lt;unknown type&gt;)">
            <summary> get maximum value  for feature.</summary>
            <param name="Feature"> feature to get maximum for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetFeatureMin(&lt;unknown type&gt;)">
            <summary> get minimum value  for feature.</summary>
            <param name="Feature"> feature to get minimum for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetFeatureValue(&lt;unknown type&gt;,System.UInt32)">
            <summary> set value of given feature.</summary>
            <param name="Feature"> feature to set value</param>
            <param name="Value"> Value to set</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetFeatureValue(&lt;unknown type&gt;)">
            <summary> get value of given feature.</summary>
            <param name="Feature"> to get value for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetFeatureStatus(&lt;unknown type&gt;,&lt;unknown type&gt;)">
            <summary> set state of a given Feature.</summary>
            <param name="Feature"> Feature to  set state for</param>
            <param name="State"> State to set</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetFeatureStatus(&lt;unknown type&gt;)">
            <summary> get current state of a given Feature.</summary>
            <param name="Feature"> feature to query for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.HasFeatureStatus(&lt;unknown type&gt;,&lt;unknown type&gt;)">
            <summary> query if the given feature supports a given state.</summary>
            <param name="Feature"> Feature to query for</param>
            <param name="State"> State to query for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.HasFeature(&lt;unknown type&gt;)">
            <summary> query if camera feature is supported.</summary>
            <param name="Feature"> Feature to query for</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ReadBlock(System.UInt32,System.UInt32[])">
            <summary> reads a register block from camera.</summary>
            <param name="Address"> 32bit part of Firewire Address</param>
            <param name="Values"> array to write the values to</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.WriteBlock(System.UInt32,System.UInt32[])">
            <summary> writes a register block to the camera.</summary>
            <param name="Address"> 32bit part of Firewire Address</param>
            <param name="Values"> array to read the values from</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetRegister(&lt;unknown type&gt;)">
            <summary> read value from Camera register.</summary>
            <param name="Address"> 32bit part of Firewire Address</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetRegister(&lt;unknown type&gt;,System.UInt32)">
            <summary> write value to Camera register.</summary>
            <param name="Address"> enum name of Firewire Address</param>
            <param name="Value"> Value to write</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetRegister(System.UInt32)">
            <summary> read value from Camera register.</summary>
            <param name="Address"> 32bit part of Firewire Address</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SetRegister(System.UInt32,System.UInt32)">
            <summary> write value to Camera register.</summary>
            <param name="Address"> 32bit part of Firewire Address</param>
            <param name="Value"> Value to write</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.SaveSettings(System.String,AlliedVisionTech.UniFoundationNet.enUniSaveSettingsMode)">
            <summary> Save camera settings into xml file.</summary>
            <param name="FileName"> name of xml file to write to</param>
            <param name="mode"> determines what camera information is used to identify the camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.LoadSettings(System.String,AlliedVisionTech.UniFoundationNet.enUniSaveSettingsMode)">
            <summary> Load Camera settings from xml file.</summary>
            <param name="FileName"> qualified filename to load the settings from</param>
            <param name="mode"> determines what camera information is used to identify the camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetRGBYPlanesImage(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.UInt32)">
            <summary> grab image into red, green and blue components image</summary>
            <param name="Red"> red component of the image</param>
            <param name="Green"> green component of the image</param>
            <param name="Blue"> blue component of the image</param>
            <param name="Luma"> luma component of the image</param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetRGBPlanesImage(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.UInt32)">
            <summary> grab image into red, green and blue components image</summary>
            <param name="Red"> red component of the image</param>
            <param name="Green"> green component of the image</param>
            <param name="Blue"> blue component of the image</param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetImage(System.Drawing.Bitmap,System.UInt32@,System.UInt32)">
            <summary> get Image in continuous grabbing mode</summary>
            <param name="Image"> Bitmap to return the grabbed image</param>
            <param name="FrameCount"> returns frame count </param>
            <param name="TimeOut"> timeout in ms to wait for a grabbed image before abandoning (use in polling grabbing)</param>
            <remarks>
                <paramref name="Image" /> is expected to be appropriately initialized before function call.
Image dimensions need to match current camera configuration.
PixelFormat can be either Format8bppIndexed or Format24bppRgb.
Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetImage(System.Drawing.Bitmap,System.UInt32)">
            <summary> Get Image in continuous grabbing mode.</summary>
            <param name="Image"> Bitmap to be filled with image data </param>
            <param name="TimeOut"> timeout in ms to wait for a grabbed image before abandoning (use in polling grabbing)</param>
            <remarks>
                <paramref name="Image" /> is expected to be appropriately initialized before function call.
Image dimensions need to match current camera configuration.
PixelFormat can be either Format8bppIndexed or Format24bppRgb.
Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetNativeImageEx(System.Byte[],AlliedVisionTech.UniTransformNet.UniTransportFormatInfo,System.UInt32@,System.UInt32)">
            <summary> 
Get Image in continuous grabbing mode. Image Data is returned in native transport format. Image format 
conversions may be required to retrieve displayable data. For certain color codes (mostly 'deep' image formats), 
the output format differs between 1394 DCAM and GigE-Vision cameras. Relevant format details are returned 
in <paramref name="FormatInfo" />.
</summary>
            <param name="Image"> array to be filled with the received image data</param>
            <param name="FrameCount"> returns frame count </param>
            <param name="FormatInfo"> returns detailed format information <see cref="T:AlliedVisionTech.UniTransformNet.UniTransportFormatInfo" /></param>
            <param name="TimeOut"> timeout in ms</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetNativeImageEx(System.Byte[],AlliedVisionTech.UniTransformNet.UniTransportFormatInfo,System.UInt32)">
            <summary> 
Get Image in continuous grabbing mode. Image Data is returned in native transport format. Image format 
conversions may be required to retrieve displayable data. For certain color codes (mostly 'deep' image formats), 
the output format differs between 1394 DCAM and GigE-Vision cameras. Relevant format details are returned 
in <paramref name="FormatInfo" />.
</summary>
            <param name="Image"> array to be filled with the received image data</param>
            <param name="FormatInfo"> returns detailed format information <see cref="T:AlliedVisionTech.UniTransformNet.UniTransportFormatInfo" /></param>
            <param name="TimeOut"> timeout in ms</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetNativeImage(System.Byte[],System.UInt32)">
            <summary> 
Get image in continuous grabbing mode. Image data is returned in transport format as supported by 
1394 DCAM cameras. Image format conversions may be required to retrieve displayable data.
This function is provided for backwards compatibility. GigE-Vision image frames are 
converted to match DCAM image formats, when necessary (introducing processing overhead). 
See <see cref="M:AlliedVisionTech.UniControlNet.UniCamera.GetNativeImageEx(System.Byte[],AlliedVisionTech.UniTransformNet.UniTransportFormatInfo,System.UInt32@,System.UInt32)" />
for image acquisition in actual, interface-dependent transport format.
</summary>
            <param name="Image"> array to be filled with the received image data</param>
            <param name="TimeOut"> time to wait for a image t o be delivered</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.MultiShotStop">
            <summary> stop continuous grabbing.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.MultiShotStart(System.UInt32)">
            <summary> Start continuous grabbing. </summary>
            <param name="GrabCount"> images to grab</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabStop(AlliedVisionTech.UniFoundationNet.enUniStopOption)">
            <summary> stop continuous grabbing.</summary>
            <param name="Option"> imported UniFoundationNet::enUniStopOption determining the stopping behavior</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabStart(System.UInt32)">
            <summary> Start continuous grabbing. </summary>
            <param name="TimeOut"> time to wait for a image to be present</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabImage(System.Drawing.Bitmap,System.UInt32)">
            <summary> Synchronously grab a single image into a Bitmap.</summary>
            <param name="Image"> Bitmap to be filled with image data.</param>
            <param name="TimeOut"> timeout in ms</param>
            <remarks>
                <paramref name="Image" /> is expected to be appropriately initialized before function call.
Image dimensions need to match current camera configuration.
PixelFormat can be either Format8bppIndexed or Format24bppRgb.
Only Bitmaps with 'top-down' BitmapData (positive Stride) are supported.
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabRGBYPlanesImage(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.UInt32)">
            <summary> grab image into red, green and blue components image</summary>
            <param name="Red"> red component of the image</param>
            <param name="Green"> green component of the image</param>
            <param name="Blue"> blue component of the image</param>
            <param name="Luma"> luma component of the image</param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabRGBPlanesImage(System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.Drawing.Imaging.BitmapData,System.UInt32)">
            <summary> grab image into red, green and blue components image</summary>
            <param name="Red"> red component of the image</param>
            <param name="Green"> green component of the image</param>
            <param name="Blue"> blue component of the image</param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabNativeImageEx(System.Byte[],AlliedVisionTech.UniTransformNet.UniTransportFormatInfo,System.UInt32)">
            <summary> 
Grab a single image synchronously in native transport format. Image format conversions 
may be required to retrieve displayable data. For certain color codes (mostly 'deep' image formats), 
the output format differs between 1394 DCAM and GigE-Vision cameras. Relevant format details are returned 
in <paramref name="FormatInfo" />.
</summary>
            <param name="Image"> Buffer matching the size of the image in byte to grab into</param>
            <param name="FormatInfo"> returns detailed format information <see cref="T:AlliedVisionTech.UniTransformNet.UniTransportFormatInfo" /></param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabNativeImage(System.Byte[],System.UInt32)">
            <summary> 
Grab a single image synchronously in transport format as supported by 1394 DCAM cameras. 
Image format conversions may be required to retrieve displayable data.
This function is provided for backwards compatibility. GigE-Vision image frames are 
converted to match DCAM image formats, when necessary (introducing processing overhead). 
<see cref="M:AlliedVisionTech.UniControlNet.UniCamera.GrabNativeImageEx(System.Byte[],AlliedVisionTech.UniTransformNet.UniTransportFormatInfo,System.UInt32)" /> for image acquisition in actual, interface-dependent transport format.
</summary>
            <param name="Image"> Buffer matching the size of the image in byte to grab into</param>
            <param name="TimeOut"> time in ms to wait before the grab will be abandoned signaling timeout</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabPixelFormat(AlliedVisionTech.UniFoundationNet.enUniColorCode)">
            <summary> pixel format for a given color code.</summary>
            <param name="code"> the color code to compute the PixelFormat from </param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GrabPixelFormat">
            <summary> pixel format for the current image format.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.Aoi">
            <summary> get the current image AOI.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.PrepareFixedGrab(System.UInt32,System.UInt32,&lt;unknown type&gt;)">
            <summary> prepare camera for fixed format grabbing.</summary>
            <param name="DCamFormat"> desired IIDC format</param>
            <param name="DCamMode"> desired IIDC mode</param>
            <param name="Fps"> desired frames per second</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.PrepareFreeGrab(System.UInt32@,AlliedVisionTech.UniFoundationNet.enUniColorCode@,System.UInt32@,System.UInt32@,System.UInt32@,System.UInt32@,System.UInt32@,System.UInt32@)">
            <summary> prepare camera for free grabbing mode.</summary>
            <param name="Mode"> Format 7 Mode AlliedVisionTech::UniFoundationNet::FreeMode::Mode</param>
            <param name="ColorCode"> IIDC colorCode</param>
            <param name="Width"> image width  ( might be changed on return to a valid value)</param>
            <param name="Height"> image height ( might be changed on return to a valid value)</param>
            <param name="BufferCount"> number of buffers to grab ( at least 2 might be changed on return to a valid value)</param>
            <param name="XOffset"> Left offset of the free modes AOI ( might be changed on return to a valid value)</param>
            <param name="YOffset"> Top offset of the free modes AOI ( might be changed on return to a valid value)</param>
            <param name="BusLoad"> bus utilization in (1/1000) ( might be changed on return to a valid value)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.PrepareFreeGrab(System.UInt32@,AlliedVisionTech.UniFoundationNet.enUniColorCode@,System.UInt32@,System.UInt32@)">
            <summary> prepare camera for free grabbing mode.</summary>
            <param name="Mode"> Format 7 mode AlliedVisionTech::UniFoundationNet::FreeMode::Mode</param>
            <param name="ColorCode"> IIDC colorCode</param>
            <param name="Width"> image width  ( might be changed on return to a valid value)</param>
            <param name="Height"> image height ( might be changed on return to a valid value)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.PrepareFreeGrab(AlliedVisionTech.UniFoundationNet.FreeMode,System.UInt32@,System.UInt32@,System.UInt32@,System.UInt32@)">
            <summary> prepare camera for free grabbing mode.</summary>
            <param name="Mode"> the preferred mode to grab will on return be filled with valid values</param>
            <param name="BufferCount"> number of buffers to use for grabbing</param>
            <param name="XOffset"> AOI offset left</param>
            <param name="YOffset"> AOI offset Top</param>
            <param name="BusLoad"> bus utilization in (1/1000) ( might be changed on return to a vlaid value)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.PrepareFreeGrab(AlliedVisionTech.UniFoundationNet.FreeMode)">
            <summary> prepare camera for a free grabbing mode.</summary>
            <param name="Mode"> the preferred mode to grab will on return be filled with valid values</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeUnitPosV">
            <summary> current free mode vertical AOI move units.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeUnitPosH">
            <summary> current free mode horizontal AOI move units.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeUnitSizeV">
            <summary> current free mode vertical size change units.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeUnitSizeH">
            <summary> current free mode horizontal size change units.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeBytePerFrame">
            <summary> current free mode bytes per frame.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreePixelCount">
            <summary> current free mode pixel count.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeHeight">
            <summary> current free mode height.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeWidth">
            <summary> current free mode width.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetFreeModeInfo(System.UInt32,AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo)">
            <summary> get info about specific free mode.</summary>
            <param name="Mode"> the free mode to query</param>
            <param name="ModeInfo"> type of info to query for</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurFreeMode">
            <summary> current free mode</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurDcamMode">
            <summary> current IIDC mode (1394 DCAM cameras only)</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurDcamFormat">
            <summary> current IIDC Format (1394 DCAM cameras only)</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FreeModes">
            <summary> enum supported free modes.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.PacketResendReceived">
            <summary> number of missing packets resent by the camera and received by the host (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.PacketResendRequests">
            <summary> number of missing packets requested to the camera for resent (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ReceivedPackets">
            <summary> get the number of received packets with image data (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HostMACAddress">
            <summary> get MAC address of host currently controlling the camera (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.HostIPAddress">
            <summary> get IP address of host currently controlling the camera (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CameraMACAddress">
            <summary> get the camera's MAC address (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CameraIPAddress">
            <summary> get the camera's IP address (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurrentBusLoad">
            <summary> get currently configured bus load for this camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ImageAlignment">
            <summary> get alignment of image data for this camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ImageEndianness">
            <summary> get endianness of image data for this camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IsOpenedReadOnly">
            <summary> Is this camera opened in read-only mode?</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IsControlledExternally">
            <summary> Is this camera controlled by another application / host?.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IsReachable">
            <summary> is this camera reachable via IP (true) or misconfigured (false) (GigE cameras only).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IsOpen">
            <summary> if camera is currently opened.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.InterfaceType">
            <summary> get interface type for this camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SISType">
            <summary> get information about the camera support for Secure Image Signature.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ModelName">
            <summary> get model name for this camera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.VendorName">
            <summary> get vendor string for camera.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetInfoString(&lt;unknown type&gt;)">
            <summary> get a human readable info about the current camera.</summary>
            <param name="InfoType"> The type of info to receive</param>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.Interlaced">
            <summary> if camera is interlaced.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.BayerPattern">
            <summary> bayer pattern of the image sensor.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FPGAVersion">
            <summary> FPGA firmware version.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MCVersion">
            <summary> Micro controller Firmware version.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IIDCVersion">
            <summary> IIDC version of the camera.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.GetInfo(&lt;unknown type&gt;)">
            <summary> get numeric info about a specific parameter.</summary>
            <param name="InfoType"> The type of info to receive</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.Close">
            <summary> closes the camera.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OpenReadOnlyEx(System.UInt32,System.UInt32)">
            <summary> Open the camera with the given serial number and speed limit in read-only mode.</summary>
            <param name="SerialNumber"> The serial number for the camera to be opened</param>
            <param name="SpeedLimit"> The speed limit for this camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OpenReadOnlyEx(System.UInt32)">
            <summary> Open the camera with the given serial number in read-only mode.</summary>
            <param name="SerialNumber"> The serial number for the camera to be opened</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OpenReadOnly">
            <summary> Open current camera in read-only mode.</summary>
            <remarks> Serial number and extra info should have been set before.</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OpenEx(System.UInt32,System.UInt32)">
            <summary> Open the camera with the given serial number and speed limit.</summary>
            <param name="SerialNumber"> The serial number for the camera to be opened</param>
            <param name="SpeedLimit"> The speed limit for this camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OpenEx(System.UInt32)">
            <summary> Open the camera with the given serial number.</summary>
            <param name="SerialNumber"> The serial number for the camera to be opened </param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.Open(System.UInt32)">
            <summary> Open current camera with speed limit.</summary>
            <remarks> Serial number and extra info should have been set before.</remarks>
            <param name="SpeedLimit"> The speed limit for this camera</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.Open">
            <summary> Open current camera.</summary>
            <remarks> Serial number and extra info should have been set before.</remarks>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FixedFps">
            <summary> current Fps (mode 7 IIDC color code).</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FixedMode">
            <summary> current fixed IIDC mode.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.FixedFormat">
            <summary> current fixed IIDC Format.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurImageDepth">
            <summary> current image depth.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurColorCodeEn">
            <summary> current IIDC color code as enum.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurColorCode">
            <summary> current IIDC color code.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurImageHeight">
            <summary> current image height.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurImageWidth">
            <summary> current image width.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.IsColor">
            <summary> if camera supports color.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MaxImageDepth">
            <summary> maximal supported image depth.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MaxImageHeight">
            <summary> maximal supported image height.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MaxImageWidth">
            <summary> maximal supported image width.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CurImage">
            <summary> current image properties.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.MaxImage">
            <summary> maximal image properties.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.Available">
            <summary> check if camera is available.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.CardNumber">
            <summary> get card number for current  camera</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.ExtraInfo">
            <summary> additional identification number.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.LicenseType">
            <summary> get the license type for the current camera</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.SerialNumber">
            <summary> serial number of t he camera as unique id.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniCamera.Family">
            <summary> camera family.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.#ctor(System.UInt32,System.UInt32)">
            <summary> Constructor from serial number and extra info.</summary>
            <param name="SN">serial number of the camera</param>
            <param name="Extra">extra info of the camera</param>
            <remarks>
For filling <paramref name="SN" /> and <paramref name="Extra" />, use the values you get from from GetCameras()
</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.#ctor">
            <summary> default constructor for Camera.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.ShadingBuildAsync(System.Object)">
            <summary> build shading image asynchron.</summary>
            <param name="FrameCount"> number of image used to build the shading image</param>
            <remarks> FrameCount has to be a power of 2</remarks>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OnFrameErrorHandler(System.UInt32)">
            <summary> handling the Frame Error delegate and dispatching to the registered handlers.</summary>
            <param name="cam_id"> camera identifier(SerialNumber)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OnFrameDroppedHandler(System.UInt32)">
            <summary> handling the Frame Dropped delegate and dispatching to the registered handlers.</summary>
            <param name="cam_id"> camera identifier(SerialNumber)</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OnFrameStartHandler(System.UInt32)">
            <summary> Handling the frame start delegate and dispatch to the registered handlers.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.OnFrameReadyHandler(System.UInt32)">
            <summary> Handling the frame ready delegate and dispatch to the registered handlers.</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniCamera.RegisterDelegates">
            <summary> Registering camera for Frame ready and Frame Error.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniCamera.OnAsyncReady">
            <summary> Event that is raised when an asynchronous operation has finished.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameError">
            <summary> Event that is raised when an error occurred while grabbing a frame.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameDropped">
            <summary> Event that is raised when a frame is dropped for this camera.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameStart">
            <summary> Event that is raised when the first packet of a frame is received.</summary>
        </member>
        <member name="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameReady">
            <summary> Event that is raised when a frame is received completely from this camera.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.AsyncReadyHandler">
            <summary> Function prototype for methods handling the OnAsyncReady event.</summary>
            <seealso cref="E:AlliedVisionTech.UniControlNet.UniCamera.OnAsyncReady" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.FrameErrorHandler">
            <summary> Function prototype for methods handling the OnFrameError event.</summary>
            <seealso cref="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameError" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.FrameDroppedHandler">
            <summary> Function prototype for methods handling the OnFrameDropped event.</summary>
            <seealso cref="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameDropped" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.FrameStartHandler">
            <summary> Function prototype for methods handling the OnFrameStart event.</summary>
            <seealso cref="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameStart" />
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.FrameReadyHandler">
            <summary> Function prototype for methods handling the OnFrameReady event.</summary>
            <seealso cref="E:AlliedVisionTech.UniControlNet.UniCamera.OnFrameReady" />
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniCamera.AsyncResult.TimeOut">
            <summary> operations.timed out</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniCamera.AsyncResult.Fail">
            <summary> operations failed.</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniCamera.AsyncResult.Ok">
            <summary> operation returned ok.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.AsyncResult">
            <summary> result of async operation.</summary>
        </member>
        <member name="F:AlliedVisionTech.UniControlNet.UniCamera.AsyncOperation.ShadingBuild">
            <summary> async shading build operation.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera.AsyncOperation">
            <summary> supported async operations.</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniCamera">
            <summary> UniCamera.</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniRegisterListItem.m_Value">
            <summary> value of the feature </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniRegisterListItem.m_Address">
            <summary> address of the feature </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.UniRegisterListItem">
            <summary> register list update item </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniFeatureListItem.m_State">
            <summary> state of the feature</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniFeatureListItem.m_Value">
            <summary> value of the feature</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniFeatureListItem.m_Feature">
            <summary> feature </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.UniFeatureListItem">
            <summary> feature list update item </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniLicenseType">
            <summary> FireGrab license modes </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.NotLimited">
            <summary> free from limitations </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.FrameLimited">
            <summary>limited by frames</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.Device">
            <summary>device specific</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.DateLimited">
            <summary>expires with date</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.Vendor">
            <summary>vendor global</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.Ethernet">
            <summary>thernet adapter</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.HardDisk">
            <summary>hard disk</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.CardGUID">
            <summary>firewire card</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniLicenseType.NoLicense">
            <summary>no license </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI.FeatureState">
            <summary> feature state</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI.Aoi">
            <summary> AOI of the feature</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI.#ctor(&lt;unknown type&gt;,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
            <summary> init constructor</summary>
            <param name="state"> state of the feature</param>
            <param name="left"> left coordinate of the AOI</param>
            <param name="top"> top coordinate of the AOI</param>
            <param name="width"> width of the AOI</param>
            <param name="height"> height of the AOI</param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI.#ctor(&lt;unknown type&gt;,AlliedVisionTech.UniFoundationNet.AOI!System.Runtime.CompilerServices.IsByValue)">
            <summary> init constructor </summary>
            <param name="state"> state of the feature</param>
            <param name="aoi"> AOI for the feature</param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI.#ctor">
            <summary> default constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.UniAutoFunctionAOI">
            <summary> AOI and state for AutoFunctionAOI</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.BufferSize">
            <summary> size of the internal buffer</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.Mode">
            <summary> serial connection transport mode</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.StopBits">
            <summary> stop bits</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.Parity">
            <summary>parity</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.CharType">
            <summary> character encoding</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniSioConfig.BaudRate">
            <summary>transfer speed</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniSioConfig.#ctor(AlliedVisionTech.UniFoundationNet.enSioBaudRate,AlliedVisionTech.UniFoundationNet.enSioCharType,AlliedVisionTech.UniFoundationNet.enSioParity,AlliedVisionTech.UniFoundationNet.enSioStopBits,AlliedVisionTech.UniFoundationNet.enSioMode,System.UInt32)">
            <summary />
            <param name="baud_rate"> serial io transfer baud rate</param>
            <param name="char_type"> serial io character type</param>
            <param name="parity"> serial io parity</param>
            <param name="stop_bits"> serial io stop bits</param>
            <param name="mode"> serial io mode</param>
            <param name="buffer_size"> serial io buffer size</param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniSioConfig.#ctor(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
            <summary> init constructor </summary>
            <param name="baud_rate"> serial io transfer baud rate</param>
            <param name="char_type"> serial io character type</param>
            <param name="parity"> serial io parity</param>
            <param name="stop_bits"> serial io stop bits</param>
            <param name="mode"> serial io mode</param>
            <param name="buffer_size"> serial io buffer size</param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniSioConfig.#ctor">
            <summary> default constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.UniSioConfig">
            <summary>configuration of serial connection</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enSioCharType">
            <summary>character encoding</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioCharType.ASCII_8">
            <summary>8bit ascii code</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioCharType.ASCII_7">
            <summary>7bit ascii code</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enSioBaudRate">
            <summary>Enumeration for I/O speed (for configuration of serial I/O communication)</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Undefined">
            <summary>undefined</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate230400">
            <summary>230400 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate115200">
            <summary>115200 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate57600">
            <summary>57600 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate38400">
            <summary>38400 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate19200">
            <summary>19200 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate9600">
            <summary>9600 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate4800">
            <summary>4800 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate2400">
            <summary>2400 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate1200">
            <summary>1200 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate600">
            <summary>600 Baud</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioBaudRate.Rate300">
            <summary>300 Baud</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enSioParity">
            <summary> Enumeration for I/O parity (for configuration of serial I/O communication)</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioParity.Undefined">
            <summary>undefined parity</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioParity.Even">
            <summary>parity is even</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioParity.Odd">
            <summary>parity is odd</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioParity.None">
            <summary>no parity</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enSioStopBits">
            <summary> Enumeration for I/O stop bits (for configuration of serial I/O communication) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioStopBits.Undefined">
            <summary>undefined stop bits</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioStopBits.Bits_2">
            <summary>2 stop bits</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioStopBits.Bits_1_5">
            <summary> 1 1/2 stop bits</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioStopBits.Bita_1">
            <summary>1 stop bit</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enSioMode">
            <summary>Enumeration for I/O speed (for configuration of serial I/O communication) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioMode.Undefined">
            <summary>undefined tranfer mode</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioMode.ReceiveTransmit">
            <summary>allow both directions of the serial transfer</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioMode.Transmit">
            <summary>only allow transmission of serial output</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioMode.Receive">
            <summary>only allow reception of serial input</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enSioMode.Disable">
            <summary>no serial transfer</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.OutputCtrlInfo.State">
            <summary> state of the pin true(signaled) false(not signaled) </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.OutputCtrlInfo.OutputMode">
            <summary> operation mode of the output pin </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.OutputCtrlInfo.Polarity">
            <summary> polarity of the pin true(inverted) false(not inverted) </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.OutputCtrlInfo">
            <summary> Output pin control settings </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.InputCtrlInfo.State">
            <summary> state of the pin true(signaled) false(not signaled) </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.InputCtrlInfo.InputMode">
            <summary> operation mode of the pin </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.InputCtrlInfo.Polarity">
            <summary> polarity of the pin true(inverted) false(not inverted) </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.InputCtrlInfo">
            <summary> Input pin control settings </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enOutputMode">
            <summary>
Output pin operation modes.
</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.Unknown">
            <summary> Unknown mode encountered </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.WaitingForTrigger">
            <summary> Signaling according to the "Waiting For Trigger" signal </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.PWM">
            <summary> Create a pulse width modulation signal on this pin </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.FollowInput">
            <summary> Output pin follows corresponding input pin </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.Busy">
            <summary> Camera busy(while exposure, sensor read out, and data transmit) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.FrameValid">
            <summary> Frame Valid controls the pin (after intEna is low -&gt; after integration of the image) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.IntEna">
            <summary> Integration Enabled controls the pin (while image exposure) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.PinState">
            <summary> Pin State is used; do not combine with invert polarity </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enOutputMode.Off">
            <summary> Pin is switched off </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enInputMode">
            <summary>
Input pin operation modes.
</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enInputMode.Trigger">
            <summary> Pin is switched by trigger </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enInputMode.Off">
            <summary> Pin is switched off </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.MaxPinCount.Output">
            <summary>
maximal output pins supported by avt cameras.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.MaxPinCount.Input">
            <summary>
maximal input pins supported by avt cameras.
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.MaxPinCount">
            <summary>
definition for maximum pin number.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.UniWhiteBalanceBase.Type">
            <summary>
get white balance type information
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.UniWhiteBalanceBase.#ctor">
            <summary>
Default construction
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.UniWhiteBalanceBase">
            <summary>
Base class for white balance
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniWhiteBalanceType">
            <summary>
Type of white balance
</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniWhiteBalanceType.CYGM">
            <summary> CYGM Guppy interlaced sensor </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniWhiteBalanceType.RGB">
            <summary> RGB Bayer pattern sensor </summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.Equals(System.Object)">
            <summary>
Determines whether the specified System::Object is equal to the current ColorCorrectionMatrix.
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.SetCoefficient(System.Int32,System.Int32,System.Int32)">
            <summary>
Set a coefficient by the source and destination color index.
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.GetCoefficient(System.Int32,System.Int32)">
            <summary>
Get a coefficient by the source and destination color index.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.Maximum">
            <summary>
Get the maximum value for a coefficient.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.Minimum">
            <summary>
Get the minimum value for a coefficient.
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.#ctor(System.UInt32,System.UInt32)">
            <summary>
Constructor.
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix.#ctor">
            <summary>
Default construction
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.ColorCorrectionMatrix">
            <summary>
Color correction matrix
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.FreeMode.Equals(System.Object)">
            <summary>
Determines whether the specified System::Object is equal to the current FreeMode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.VerticalDecimation">
            <summary>
Get the vertical decimation of a virtual mode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.HorizontalDecimation">
            <summary>
Get the horizontal decimation of a virtual mode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.IsVirtual">
            <summary>
Is this a virtual (true) or real mode (false).
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.ColorCode">
            <summary>
IIDC color code of the free mode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.Height">
            <summary>
image height of the free mode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.Width">
            <summary>
image width of the free mode.
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.FreeMode.Mode">
            <summary>
IIDC mode.
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.FreeMode.ToString">
            <summary>
transform the free mode data into human readable text.
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.FreeMode">
            <summary> free scalable mode information </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Height">
            <summary> the height of the AOI</summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Width">
            <summary> the width of the AOI </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Left">
            <summary> the left position of the AOI </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Top">
            <summary> the top position of the AOI </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Offset">
            <summary>get/set the offset of the AOI </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.AOI.Dimension">
            <summary> Image properties of the AOI </summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.AOI.op_Implicit(AlliedVisionTech.UniFoundationNet.AOI)~System.ValueType!System.Drawing.Rectangle!System.Runtime.CompilerServices.IsBoxed">
            <summary> conversion from Handle to AOI to ::Drawing::Rectangle </summary>
            <param name="aoi"> The AOI handle to create a rectangle from </param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.AOI.op_Implicit(AlliedVisionTech.UniFoundationNet.AOI!System.Runtime.CompilerServices.IsByValue)~System.ValueType!System.Drawing.Rectangle!System.Runtime.CompilerServices.IsBoxed">
            <summary> conversion to ::Drawing::Rectangle </summary>
            <param name="aoi"> The AOI to create a rectangle from </param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.AOI.#ctor(System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
            <summary> constructor from top,left,width and height </summary>
            <param name="l"> Left position of the AOI to create </param>
            <param name="t"> Top position of the AOI to create </param>
            <param name="w"> Width of the AOI to create </param>
            <param name="h"> Height of the AOI to create </param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.AOI.#ctor(System.ValueType!System.Drawing.Rectangle!System.Runtime.CompilerServices.IsBoxed)">
            <summary> constructor from rectangle </summary>
            <param name="rect"> Rectangle for the AOI to create </param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.AOI.#ctor">
            <summary> default constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.AOI">
            <summary> rectangle defining an AOI </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.ImageProperties.Depth">
            <summary> color depth of the image </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.ImageProperties.Height">
            <summary> height of the image </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.ImageProperties.Width">
            <summary> width of the image </summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ImageProperties.#ctor(System.UInt32,System.UInt32,System.UInt32)">
            <summary> constructor from width height and depth </summary>
            <param name="width"> width of the image</param>
            <param name="height"> height of the image</param>
            <param name="depth"> depth of the image</param>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.ImageProperties.#ctor">
            <summary> default constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.ImageProperties">
            <summary> camera image properties </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.DefaultGateway">
            <summary> Default gateway </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.SubnetMask">
            <summary> Subnet mask </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.IpAddress">
            <summary> IP address </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.SupportedConfigurationModes">
            <summary> supported IP configuration modes (ignored when setting the IP configuration) </summary>
        </member>
        <member name="P:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.ConfigurationMode">
            <summary> current IP configuration mode </summary>
        </member>
        <member name="M:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo.#ctor">
            <summary> default constructor </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.IPConfigurationInfo">
            <summary> IP configuration settings </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniIPConfigurationMode">
            <summary> Enumeration for IP configuration modes (see ::E_UNI_IP_CONFIGURATION_MODE) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniIPConfigurationMode.AutoIP">
            <summary> Use an automatically assigned IP address (LLA = link-local address). </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniIPConfigurationMode.DHCP">
            <summary> Ask a DHCP server for an IP address. Fall back to Auto IP in case of failure. </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniIPConfigurationMode.PersistentIp">
            <summary> Use persistent (fixed) IP address stored in the camera. </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniInterfaceType">
            <summary> Enumeration for interface type </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniInterfaceType.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniInterfaceType.GigE">
            <summary> Gigabit Ethernet interface </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniInterfaceType.FireWire">
            <summary> FireWire interface </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniCameraFamily">
            <summary> AVT camera families </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.GuppyPRO">
            <summary> Guppy PRO camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Manta">
            <summary> Manta camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Stingray">
            <summary> Stingray camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Pike">
            <summary> Pike camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Oscar">
            <summary> Oscar camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Marlin">
            <summary> Marlin camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Guppy">
            <summary> Guppy camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Dolphin">
            <summary> Dolphin camera family </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniCameraFamily.Unknown">
            <summary> unknown camera </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniResolutionPresets">
            <summary> Enumeration for resolution presets </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.End">
            <summary> end </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Begin">
            <summary> begin </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DC4K">
            <summary> 4096 x 2160 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DC2K">
            <summary> 2048 x 1080    Digital Cinema  Line binning 16/9 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HD1080">
            <summary> 1920 x 1080 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HD720">
            <summary> 1280 x  720     HDTV </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WVGA">
            <summary> 854 x  480     asp rat 16:9  </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.QSXGA">
            <summary> 2580 x 2048 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SXGA">
            <summary> 1280 x 1024     asp rat 5:4 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.QXGA">
            <summary> 2048 x 1536 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UXGA">
            <summary> 1600 x 1200 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SXGAP">
            <summary> 1400 x 1050 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.XVGA">
            <summary> 1280 x  960  </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.XGA">
            <summary> 1024 x  768 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SVGA">
            <summary> 800 x  600 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.PAL">
            <summary> 768 x  576 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.VGA">
            <summary> 640 x  480 </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.QVGA">
            <summary> 320 x  240      asp,rat 4:3 </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniLogging">
            <summary> Enumeration for controlling logging </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.LevelMask">
            <summary> Mask for testing log levels </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Debug">
            <summary> Log everything </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Info">
            <summary> Log fatal events, errors, warnings and info messages </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Warning">
            <summary> Log fatal events, errors and warnings </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Fatal">
            <summary> Log only fatal events </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniFrameRate">
            <summary> Enumeration for frame rates </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_240">
            <summary> 240 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_120">
            <summary> 120 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_60">
            <summary> 60 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_30">
            <summary> 30 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_15">
            <summary> 15 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_7_5">
            <summary> 7.5 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_3_75">
            <summary> 3.75 FPS </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Rate_1_875">
            <summary> 1.875 FPS </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniDataBitRate">
            <summary> Enumeration for data bit rates (GigE and FireWire, needed for feature ::E_FEAT_PHYSPEED) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.G2000">
            <summary> Gigabit Ethernet 2 GBit/s (via Link Aggregate Group LAG) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.G1000">
            <summary> Gigabit Ethernet 1 GBit/s </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.G100">
            <summary> Gigabit Ethernet 100 MBit/s </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.G10">
            <summary> Gigabit Ethernet 10 MBit/s </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.S800">
            <summary> FireWire S800 (800 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.S400">
            <summary> FireWire S400 (400 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.S200">
            <summary> FireWire S200 (200 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.S100">
            <summary> FireWire S100 (100 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniDataBitRate.Auto">
            <summary> automatic speed detection by the driver </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUni1394Speed">
            <summary> Enumeration for 1394 speed (needed for feature ::E_FEAT_PHYSPEED) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.Last">
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.S800">
            <summary> S800 (800 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.S400">
            <summary> S400 (400 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.S200">
            <summary> S200 (200 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.S100">
            <summary> S100 (100 MBit/s) </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUni1394Speed.Auto">
            <summary> automatic speed detection by the driver </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo">
            <summary> Enumeration for Format 7 Mode information used by ::UCC_GetFreeModeInfo </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.PosV">
            <summary> free mode position step size in V </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.PosH">
            <summary> free mode position step size in H</summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.SizeV">
            <summary> free mode size step in Vt </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.SizeH">
            <summary> free mode size step in H </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.BytesPerFrame">
            <summary> free mode byte per frame </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.PixelCount">
            <summary> free mode pixel count </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.Height">
            <summary> free mode height </summary>
        </member>
        <member name="F:AlliedVisionTech.UniFoundationNet.enUniFreeModeInfo.Width">
            <summary> free mode width </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniDCamMode">
            <summary>DCam mode enum (should be used for 1394 DCAM cameras only)</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniDCamFormat">
            <summary>DCam format enum</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniModuleInfo">
            <summary> Enumeration for retrieval of API module information </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UniControlP_Version">
            <summary>version of the GigE UniControl module</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UniControlF_Version">
            <summary>version of the FireWire UniControl module</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UniControl_Version">
            <summary>version of the main UniControl module</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniCameraInfo">
            <summary> Enumeration for retrieval of camera information </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Last">
            <summary> last value for this enumeration </summary>
            <summary>last value for this enumeration</summary>
            <summary> last value for this enumeration </summary>
            <summary> last value for this enumeration </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CardId">
            <summary> ID of card the camera is connected to </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Open">
            <summary> Is the camera opened? </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.PacketResendReceived">
            <summary> number of missing packets resent by the camera and received by the host (GigE cameras only).</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.PacketResendRequests">
            <summary> number of missing packets requested to the camera for resent (GigE cameras only).</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ReceivedPackets">
            <summary> number of received packets with image data (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CameraMACAddress">
            <summary> camera's MAC address (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CameraIPAddress">
            <summary> camera's IP address (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ReadOnly">
            <summary> Is the camera opened in read-only mode? (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ControlledExternally">
            <summary> Is the camera controlled by another application / host? (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HostMACAddress">
            <summary> MAC address of host currently controlling the camera (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HostIPAddress">
            <summary> IP address of host currently controlling the camera (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.BusLoad">
            <summary> Currently configured bus load (in 1/1000)(see ::UCC_PrepareFreeGrab) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ImageAlignment">
            <summary> Alignment of the image data (see ::E_UNI_ENDIANNESS) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ImageEndianness">
            <summary> Endianness of the image data (see ::E_UNI_ENDIANNESS) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.InterfaceType">
            <summary> Type of of camera interface (see ::E_UNI_INTERFACE) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SISType">
            <summary> Type of supported Secure Image Signature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Interlaced">
            <summary> Is this an interlaced camera? </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.BayerPattern">
            <summary> Bayer pattern of this sensor (see ::E_UNI_BAYER_PATTERN) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ColorCamera">
            <summary />
        </member>
        <member name="F:&lt;unknown type&gt;.MaxDepth">
            <summary> maximum bit depth of the camera </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.MaxHeight">
            <summary> maximum height of the camera </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.MaxWidth">
            <summary> maximum width of the camera </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CustomerKey">
            <summary> customer key info </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.FPGAVersion">
            <summary> FPGA version info </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.MCVersion">
            <summary> Microcontroller version info </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.OrderID">
            <summary>  order ID </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SerialNumber">
            <summary> serial number </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.IIDCVersion">
            <summary> IIDC version info </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Vendor">
            <summary> vendor info </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Model">
            <summary> model info </summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniFeatureState">
            <summary> Enumeration for feature states </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Undefined">
            <summary> read:feature state is undefined </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Error">
            <summary> read:feature state could not be set </summary>
            <summary> Log fatal events and errors </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ClearData">
            <summary> read:feature is temporarily in "ClearData" state; write: enable "ClearData" state for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SaveData">
            <summary> read:feature is temporarily in "SaveData" state; write: enable "SaveData" state for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.LoadData">
            <summary> read:feature is temporarily in "LoadData" state; write: enable "LoadData" state for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ShowImage">
            <summary> read:feature is in "ShowImage" state; write: enable "ShowImage" state for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Busy">
            <summary> read:feature is in "Busy" state; </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Auto">
            <summary> read:feature is in "Auto" state until set to "Off" again; write: enable automatic adjustment mode for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.OnePush">
            <summary> read:feature is temporarily in "OnePush" state; write: Do "OnePush" action for this feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.On">
            <summary> read:feature is enabled; write: enable feature </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Off">
            <summary> read:feature state is off; write: disable feature</summary>
        </member>
        <member name="T:AlliedVisionTech.UniFoundationNet.enUniFeature">
            <summary>unified UniAPI features</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DoCardReset">
            <summary>reset the phy of a special card or card a camera is connected </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DetectExternallyControlledCameras">
            <summary>controls the detection of cameras controlled by an external application (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.OpenReadOnly">
            <summary>open camera in read-only mode (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DoMultiBusTrigger">
            <summary>broadcast an iso enable or single shot message to all cameras on multi busses. See \ref DESC_FEAT_DO_MULTIBUSTRIGGER "DoMultiBusTrigger" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DoBusTrigger">
            <summary>broadcast an iso enable or single shot message to all cameras. See \ref DESC_FEAT_DO_BUSTRIGGER "DoBusTrigger" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.BurstCount">
            <summary>number of images to grab in multi shot. See \ref DESC_FEAT_BURSTCOUNT "BurstCount" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CycleTime">
            <summary>The current cycle time on the bus Read-only. See \ref DESC_FEAT_CYCLETIME "CycleTime" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ISOChannel">
            <summary>A flag to signal if the driver should provide an isochronous resource manager. Enabled by default. See \ref DESC_FEAT_ISOCHANNEL "IsoChannnel" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CameraAcceptDelay">
            <summary>The time-out length (in milliseconds) for the time the driver waits for a reaction to an asynchronous command. See \ref DESC_FEAT_CAMACCEPTDELAY "CameraAcceptDelay" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UseIRMForChannel">
            <summary>A flag to signal if the driver-internal isochronous resource manager should manage the isochronous channel. See \ref DESC_FEAT_USEIRMFORCHN "UseIRMForChn" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.IRMFreeBandWidth">
            <summary>The size of the free bandwidth that is reported by the enabled isochronous resource manager. See \ref DESC_FEAT_IRMFREEBW "IRMFreeBandWidth" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.FrameMemorySize">
            <summary>The size of one complete image in bytes. Read-only. See \ref DESC_FEAT_FRAMEMEMORYSIZE "FrameMemorySize" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UseIRMForBandWidth">
            <summary>A flag to signal if the driver-internal isochronous resource manager should manage the bandwidth - see \ref DESC_FEAT_USEIRMFORBW "UseIRMForBandWidth" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.FrameBufferCount">
            <summary>The number of frame buffers to be used by the driver - see \ref DESC_FEAT_FRAMEBUFFERCOUNT "FrameBufferCount" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.PhySpeed">
            <summary>The physical transmission speed for a camera. See \ref DESC_FEAT_PHYSPEED "PhySpeed" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.StartImmediately">
            <summary>A flag to control the behavior of GrabStart() if false camera waits for trigger. See \ref DESC_FEAT_STARTIMMEDIATELY "StartImmediately" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ListAllCameras">
            <summary>A flag to control the behavior of GetCameras() true lists all cameras - see \ref DESC_FEAT_LIST_ALL_CAMERAS "ListAllCameras" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Logging">
            <summary>Parameter to control logging. See \ref DESC_FEAT_LOGGING "Logging" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.CameraBusy">
            <summary> Busy state of the camera supported get status - see \ref DESC_FEAT_CAMERABUSY "CameraBusy" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.TriggerCounter">
            <summary> access the camera trigger counter </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.NetworkPacketSize">
            <summary>the size of packets used for image transfer (GigE cameras only) </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ChannelGainBalance">
            <summary>controls the balance of the gain values for dual-tap cameras </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SISPike">
            <summary>this feature is used to distinguish between Marlin and Pike SIS Implementations  </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SISMarlin">
            <summary>this feature is used to distinguish between Marlin and Pike SIS Implementations</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SIS">
            <summary>secure image signature feature this feature is present if E_FEAT_SIS_MARLIN or E_FEAT_SIS_PIKE is present - see \ref DESC_FEAT_SIS "SIS" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ParameterUpdateTiming">
            <summary>parameter update timing mode control - see \ref DESC_FEAT_PARAMUPDATETIMING "ParameterUpdateTiming" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ReducedSmear">
            <summary>reduced smear mode control - see \ref DESC_FEAT_REDUCEDSMEAR "ReducedSmear" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceYE">
            <summary>advanced white balance for complementary sensors - YE[GR] setting - see \ref DESC_FEAT_WHITEBALANCE_YE "WhiteBalanceYE" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceCY">
            <summary>advanced white balance for complementary sensors - CY[MG] setting - see \ref DESC_FEAT_WHITEBALANCE_CY "WhiteBalanceCY" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceMG">
            <summary>advanced white balance for complementary sensors - MG[YE] setting - see \ref DESC_FEAT_WHITEBALANCE_MG "WhiteBalanceMG" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceGR">
            <summary>advanced white balance for complementary sensors - GR[CY] setting - see \ref DESC_FEAT_WHITEBALANCE_GR "WhiteBalanceGR" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HDRKneePoint3">
            <summary>HDR mode setting for third kneepoint - see \ref DESC_FEAT_HDR_KNEEPOINT3 "HDRKneePoint3" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HDRKneePoint2">
            <summary>HDR mode setting for second kneepoint - see \ref DESC_FEAT_HDR_KNEEPOINT2 "HDRKneePoint2" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HDRKneePoint1">
            <summary>HDR mode setting for first kneepoint - see \ref DESC_FEAT_HDR_KNEEPOINT1 "HDRKneePoint1"  </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HDRKneePointCount">
            <summary>HDR mode parameter: number of active kneepoints - see \ref DESC_FEAT_HDR_KNEEPOINTCOUNT "HDRKneePointCount" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.UserProfile">
            <summary>userprofile feature - see \ref DESC_FEAT_USERPROFILE "UserProfile" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.HighSNR">
            <summary>high SNR mode control - see \ref DESC_FEAT_HIGHSNR "HighSNR" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOIHeight">
            <summary>autofunction AOI height - see \ref DESC_FEAT_AUTOFUNCTIONAOI_HEIGHT "AutoFunctionAOIHeigh" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOIWidth">
            <summary>autofunction AOI width - see \ref DESC_FEAT_AUTOFUNCTIONAOI_WIDTH "AutoFunctionAOIWidth" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOITop">
            <summary>autofunction AOI top offset - see \ref DESC_FEAT_AUTOFUNCTIONAOI_TOP "AutoFunctionAOITop" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOILeft">
            <summary>autofunction AOI left offset - see \ref DESC_FEAT_AUTOFUNCTIONAOI_LEFT "AutoFunctionAOILeft" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOIYUnits">
            <summary>autofunction AOI detail information: y units - see \ref DESC_FEAT_AUTOFUNCTIONAOI_YUNITS "AutoFunctionAOIYUnits" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoFunctionAOIXUnits">
            <summary>autofunction AOI detail information: x units - see \ref DESC_FEAT_AUTOFUNCTIONAOI_XUNITS "AutoFunctionAOIXUnits" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoGainHigh">
            <summary> auto gain maximum feature - see \ref DESC_FEAT_AUTOGAIN_HIGH "AutoGainHigh" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoGainLow">
            <summary>auto gain minimum feature - see \ref DESC_FEAT_AUTOGAIN_LOW "AutoGainLow" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoShutterHigh">
            <summary>auto shutter maximum feature - see \ref DESC_FEAT_AUTOSHUTTER_HIGH "AutoShutterHigh" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoShutterLow">
            <summary>auto shutter minimum feature - see \ref DESC_FEAT_AUTOSHUTTER_LOW "AutoShutterLow" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DelayedIntegrationEnabled">
            <summary>delayed 'integration enable' feature - see \ref DESC_FEAT_DELAYEDINTEGRATIONENABLE "DelayedIntegrationEnable" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.BlemishCorrection">
            <summary>blemish pixel correction feature - see \ref DESC_FEAT_BLEMISHCORRECTION "BlemishCorrection" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.DSNUCorrection">
            <summary>dark signal non-uniformity correction feature - see \ref DESC_FEAT_DSNUCORRECTION "DSNUCorrection" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.TriggerDelayAdv">
            <summary>trigger delay feature - see \ref DESC_FEAT_TRIGGERDELAYADV "TriggerDelayAdv" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.XMirrorImage">
            <summary>horizontal mirror feature - see \ref DESC_FEAT_XMIRRORIMAGE "XMirrorImage" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.SoftReset">
            <summary>soft reset feature - see \ref DESC_FEAT_SOFTRESET "SoftReset" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ColorCorrection">
            <summary>color correction feature - see \ref DESC_FEAT_COLORCORRECTION "ColorCorrection" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.FrameCounter">
            <summary>framecounter feature - see \ref DESC_FEAT_FRAMECOUNTER "FrameCounter" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Shading">
            <summary>shading correction feature - see \ref DESC_FEAT_SHADING "Shading" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.LUTMaxSize">
            <summary>lookup table detail information: maximum table size - see \ref DESC_FEAT_LUT_MAXSIZE "LUTMaxSize" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.LUTBitsPerVal">
            <summary>lookup table detail information: bits per value - see \ref DESC_FEAT_LUT_BITSPERVAL "LUTBitsPerVal" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.LUT">
            <summary>lookup table (LUT) feature - see \ref DESC_FEAT_LUT "LUT" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ExtendedShutter">
            <summary>extended shutter feature - see \ref DESC_FEAT_EXTENDEDSHUTTER "ExtendedShutter" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.ExposureOffset">
            <summary>exposure offset feature - see \ref DESC_FEAT_EXPOSUREOFFSET "ExposureOffset" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.TimeBase">
            <summary>shutter timebase feature - see \ref DESC_FEAT_TIMEBASE "TimeBase" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.TriggerDelay">
            <summary>trigger delay feature - see \ref DESC_FEAT_TRIGGERDELAYADV "TriggerDelay" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Gain">
            <summary>signal gain feature - see \ref DESC_FEAT_GAIN "Gain"</summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Shutter">
            <summary>camera shutter feature - see \ref DESC_FEAT_SHUTTER "Shutter" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Gamma">
            <summary>gamma correction feature - see \ref DESC_FEAT_GAMMA "Gamma" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Saturation">
            <summary>color saturation feature - see \ref DESC_FEAT_SATURATION "Saturation" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Hue">
            <summary>color hue feature - see \ref DESC_FEAT_HUE "Hue" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceVR">
            <summary>whitebalance (vr) feature - see \ref DESC_FEAT_WHITEBALANCE_VR "WhiteBalanceVR" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.WhiteBalanceUB">
            <summary>whitebalance (ub) feature - see \ref DESC_FEAT_WHITEBALANCE_UB "WhiteBalanceUB" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Sharpness">
            <summary>sharpness feature - see \ref DESC_FEAT_SHARPNESS "Sharpness"  </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.AutoExposure">
            <summary>autoexposure feature - see \ref DESC_FEAT_AUTOEXPOSURE "AutoExposure" </summary>
        </member>
        <member name="F:&lt;unknown type&gt;.Brightness">
            <summary>image brightness feature - see \ref DESC_FEAT_BRIGHTNESS "Brightness" </summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControlException.ReturnCode">
            <summary>
UniAPI return code which triggered the exception
</summary>
        </member>
        <member name="P:AlliedVisionTech.UniControlNet.UniControlException.CameraId">
            <summary>
ID of camera involved in the exception (set to 0 for non-camera related methods).
</summary>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControlException.#ctor(System.UInt32,System.UInt32)">
            <summary>
constructor from return code
</summary>
            <param name="camId">camera ID</param>
            <param name="error">the error code to encapsulate in this exception</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControlException.#ctor(System.UInt32,System.String)">
            <summary>
constructor from error String
</summary>
            <param name="camId">camera ID</param>
            <param name="s">exception text</param>
        </member>
        <member name="M:AlliedVisionTech.UniControlNet.UniControlException.#ctor">
            <summary>
default constructor.
</summary>
        </member>
        <member name="T:AlliedVisionTech.UniControlNet.UniControlException">
Exception thrown by all UniControl function to report unexpected function results.
</member>
        <member name="M:UCC_EnumColorCode(System.UInt32*,System.Void*,System.UInt32)">
Select a color  code from an enumeration.
    \param[out]     ColorCode       return the selected color coding. See ::E_UNI_COLOR_CODE(may not be NULL) 
    \param[in]      ColorCodeEnum   the enumeration to select from (may not be NULL)
    \param[in]      CodeNr          position of the color coding to select
    \return         <dl><dt>S_OK</dt><dd>if all parameter are valid</dd></dl></member>
        <member name="M:UCC_DestroyColorCodeEnum(System.Void*)">
Clean up the data needed for the color code enumeration.
    \param[in]  ColorCodeEnum    the Enumeration to clean up
    \return     <dl><dt>S_OK</dt><dd>if the parameter is valid</dd></dl>
    \note       After destruction, the enumeration is not valid, but
                all selected color codings are still valid

</member>
        <member name="M:UCC_CreateColorCodeEnum(System.UInt32,System.Void**,System.UInt32*,System.UInt32)">
Create an enumeration of all supported color codings for a camera with a given preset.
    \param[in]  CamId           Camera to enumerate
    \param[out] ColorCodeEnum   Returns the enumeration of the color modes (may not be NULL)
    \param[out] ColorCodeCount  Element count of the returned color codes (may not be NULL)
    \param[in]  Preset          a ::UINT32_TYPE value representing a resolution preset,
                                the valid color codings of which will be enumerated
                                see the definition of ::E_UNI_RESOLUTION_PRESETS for valid values
    \return         <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
    \note       For a given Camera and preset enumerate all supported color codings.
                use ::UCC_EnumColorCode to select a coding.
                destroy the enumeration after use with ::UCC_DestroyColorCodeEnum
    @see    UCC_EnumColorCode, UCC_DestroyColorCodeEnum

</member>
        <member name="M:UCC_EnumPresets(System.UInt32*,System.Void*,System.UInt32)">
Select a preset from the enumeration.
    \param[out]     Preset      a ::UINT32_TYPE value representing a resolution preset
                                see the definition of ::E_UNI_RESOLUTION_PRESETS for valid values
    \param[in]      PresetsEnum Enumeration to select the preset from (may not be NULL)
    \param[in]      PresetNr    Position in the enumeration inside the range 
                                [0,value returned from ::UCC_CreatePresetsEnum]
    \return         <dl><dt>S_OK</dt><dd>if all parameter are valid</dd></dl>
    @see            ::UCC_CreatePresetEnum

</member>
        <member name="M:UCC_DestroyPresetsEnum(System.Void*)">
Clean up the data needed for the preset enumeration.
    \param[in]  PresetsEnum the preset enumeration to destroy
    \return     <dl><dt>S_OK</dt><dd>if all parameter are valid</dd></dl>
    \note       after ::UCC_DestroyPresetsEnum, the enumeration is no longer valid. 
                But all acquired presets are still valid.
    @see        ::UCC_CreatePresetsEnum, ::UCC_EnumPresets

</member>
        <member name="M:UCC_CreatePresetsEnum(System.UInt32,System.Void**,System.UInt32*)">
Create a screen mode resolution preset Enumeration for the Camera.
    \param[in]      CamId           Camera to test for supported presets
    \param[out]     PresetsEnum     Enumeration of supported presets (may not be NULL)
    \param[out]     PresetCount     Element count of returned presets (may not be NULL)
    \note           Enumerate all supported presets for your Camera, 
                    then use ::UCC_EnumPresets to get a individual preset for the camera.
                    A Preset may be converted to Width and Height with ::UCC_PresetToResolution.
                    you have to use ::UCC_DestroyPresets to clean up after use.
    \return         <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_PresetToResolution(System.UInt32,System.UInt32*,System.UInt32*)">
@name Resolution enumeration methods.
A group of methods for enumerating the resolutions of a camera.

Convert a preset to Width and Height.
    \param[in]      Preset  an ::UINT32_TYPE value defining a resolution preset
                            see the definition of ::E_UNI_RESOLUTION_PRESETS for valid values
    \param[out]     Width   return the width for this preset (may not be NULL)
    \param[out]     Height  return the Height for this preset (may not be NULL)
    \return         <dl><dt>S_OK</dt><dd>if all parameter are valid</dd></dl></member>
        <member name="M:UCC_GetRegisterListInfo(System.UInt32,System.UInt32*)">
 Provides information about the cameras 'register list update' feature. 

 \param[in] CamId                  the ID of the camera to use
 \param[out] pnMaxNumOfEntries     pointer to ::UINT32_TYPE that will receive the maximum possible size
                                   of register update lists.

 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the register list update feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_SetRegisterList(System.UInt32,System.UInt32*,System.UInt32*)">
 Apply a list of direct register writes simultaneously. 

 This function utilizes the camera's 'register list update' feature to change the contents of several
 configuration registers.
 Register addresses and values have to be provided as a ::UINT32_TYPE array. Each list entry is expected to
 span 2 ::UINT32_TYPE values, the first of which is interpreted as register address followed by the value to be written.

 In case of failure a partial list may have been applied. Parameter \c pnNumOfEntries is adjusted to the number
 of applied register entries at the time of function call. 

 \note The maximum supported register list size may be determined via ::UCC_GetRegisterListInfo.

 \param[in]    CamId           the ID of the camera to use
 \param[in]    pnListPtr       pointer to the first element of the register configuration list
 \param        pnNumOfEntries  Number of items contained by the register configuration list - will be adjusted
                               to the number of register writes actually performed at the time of function call.

 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the register list update feature is not supported by the camera</dd></dl>
 \note This function provides a flexible, low-level access to the camera's 'register list update' feature. Alternatively, ::UCC_SetFeatureList
 may be used as an easier access method.

</member>
        <member name="M:UCC_SetFeatureList(System.UInt32,System.UInt32*,System.UInt32,System.UInt32*)">
 Apply a list of feature configuration changes simultaneously. 

 Most of the camera-related features controllable by \ref unifeat "unified feature access" are supported.
 Feature configuration changes have to be provided as a ::UINT32_TYPE array. Each list entry is expected to
 span 3 ::UINT32_TYPE values, the first of which is interpreted as feature identifier according to ::E_UNI_FEATURE,
 followed by the desired value and state ( see ::E_UNI_FEATURE_STATE ). Value or state data set to ::UNI_UNDEFINED
 will be ignored. This allows to change only the feature state or the feature value while keeping the other
 unchanged.

 In case of failure a partial list may have been applied. Parameter \c pnErronousItem can be interpreted after 
 function call to determine erroneous items and failure cases which lead to partial applied lists.

 A simple example without any error handling is shown below:
 \code
 UINT32_TYPE arrFeatureList[] =
    {
        E_FEAT_SHUTTER,         UNI_UNDEFINED,  E_FEATSTATE_AUTO,
        E_FEAT_GAIN,            5,              E_FEATSTATE_ON
    };

    UINT32_TYPE index;
    UCC_SetFeatureList(m_CamId, arrFeatureList, 2);
 \endcode
 \param[in] CamId          the ID of the camera to use
 \param[in] pnListPtr      pointer to the first element of the feature configuration list
 \param[in] nNumOfEntries  Number of items contained by the feature configuration list
 \param[out]   pnErronousItem  reveals details in case of failure. The following values are possible:
                               <dl><dt>0</dt><dd>An error occured which could not be traced to
                                                               a certain list entry. Camera configuration remains unchanged.</dd><dt>1...nNumOfEntries</dt><dd>(one-based) index of the erronous item - 
                                                               Camera configuration remains unchanged.</dd><dt>nNumOfEntries+1</dt><dd>function succeeded without errors</dd><dt>::UNI_UNDEFINED</dt><dd>An error occured which could not be traced to
                                                               a certain list entry - the list has been applied partially.</dd></dl>

 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the register list update feature is not supported by the camera</dd></dl>
 \note This method provides easy but limited access to the camera's 'register list update' feature. Alternatively, ::UCC_SetRegisterList
 may be used for a more flexible, low-level control.

</member>
        <member name="M:UCC_ParamUpdate_Apply(System.UInt32)">
 Finish a configuration update procedure that has been started by ::UCC_ParamUpdate_Begin 
 and apply all changes simultaneously.

 \param[in] CamId          the ID of the camera to use

 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_ParamUpdate_Begin(System.UInt32)">
 Start a configuration update procedure that may consist of several configuration
 changes, which will take effect simultaneously when ::UCC_ParamUpdate_Apply is called.

 \param[in] CamId          the ID of the camera to use

 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_TestImage_Set(System.UInt32,System.UInt32)">
* Set the active test image.
*
* \param[in] CamId          the ID of the camera to use
* \param[in] nImageNr       test image to be used
*                           A value of 0 deactivates the test image feature. In this case the
*                           actual camera image will be received. (normal mode of operation)

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the test image feature is not supported by the camera</dd>
*          <dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if the chosen test image is not supported by the camera</dd></dl></member>
        <member name="M:UCC_TestImage_GetInfo(System.UInt32,System.UInt32*,System.UInt32*)">
* Get information about the test image feature.
*
* \param[in] CamId          the ID of the camera to use
* \param[in] pnImageNr      pointer to ::UINT32_TYPE that will receive the number of the
*                           currently activated test image
*                           A value of 0 indicates that the test image feature is
*                           not active. (normal mode of operation)
* \param[in] pnImageCount   pointer to ::UINT32_TYPE that will receive the number of
*                           supported test images

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the test image feature is not supported by the camera</dd></dl>
* \note For general information see \ref digiotrig.

</member>
        <member name="M:UCC_Trigger_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
* Configure the trigger feature.
*
* \param[in] CamId         the ID of the camera to use
* \param[in] nMode         trigger mode (see ::E_TRIGGER_MODES)
* \param[in] nActive       "boolean" long value to set the current activity status
* \param[in] nPolarity     "boolean" long value to set the status of the trigger
*                      signal inverter

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the trigger feature is not supported by the camera</dd>
*          <dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if the trigger mode uMode is not supported by the camera</dd></dl>
* \note For triggered image acquisition at least one input pin has to be chosen as trigger input 
* by using the UCC_InputPin functions. For general information see \ref digiotrig.

</member>
        <member name="M:UCC_Trigger_Get(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
* Get the current configuration of the trigger feature.
*
* \param[in]  CamId         the ID of the camera to use
* \param[out] pnMode        pointer to ::UINT32_TYPE that will receive the current
*                      trigger mode (see ::E_TRIGGER_MODES)
* \param[out] pnActive      a pointer to a "boolean" long value to receive the current activity status
* \param[out] pnPolarity    a pointer to a "boolean" long value to receive the status of the trigger
*                      signal inverter
* \param[out] pnValue       a pointer to a "boolean" long value to receive the status of the trigger
*                      signal

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the trigger feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_UserProfile_GetInfo(System.UInt32,System.UInt32*,System.UInt32*)">
* Get the currently selected user profile ID.
* This function may be used after camera startup to determine the
* loaded user set.
*
* \param[in]  CamId             the ID of the camera to use
* \param[out] pnProfileID       pointer to UINT32_TYPE that will receive the profile ID
* \param[out] pnProfileCount    pointer to UINT32_TYPE that will receive the number of 
*                               profiles supported by the camera

* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the user profile feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_UserProfile_SetDefault(System.UInt32,System.UInt32)">
* Set the default user profile ID.
* The default user profile will be loaded on startup.
*
* \param[in] CamId         the ID of the camera to use
* \param[in] nProfileID    new default profile ID

* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the user profile feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_UserProfile_Save(System.UInt32,System.UInt32)">
 Save a user profile to camera memory. The camera must not be grabbing for this function to succeed.
 user profile 0 is not writable

 \param[in] CamId         the ID of the camera to use
 \param[in] nProfileID    number of profile to be saved
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the user profile feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_UserProfile_Load(System.UInt32,System.UInt32)">
* Load a user profile from camera memory.
* The camera must not be grabbing for this function to succeed.
*
* \param[in] CamId         the ID of the camera to use
* \param[in] nProfileID    number of profile to be loaded

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the user profile feature is not supported by the camera</dd>
*          <dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera is grabbing</dd></dl></member>
        <member name="M:UCC_Whitebalance_GMCY_Get(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
* Get the status of the whitebalance feature for GMCY sensors.
* Parameters the caller is not interested in may be set to NULL
*
* \param[in]  CamId         The ID of the camera to use
* \param[out] pnMode        Pointer to UINT32_TYPE that will receive the current mode
*                           of operation. See ::UCC_Whitebalance_Set for possible values
* \param[out] pnGr_CyVal    Pointer to UINT32_TYPE that will receive the value: 
                            - Mode 0:Gr/Cy 
                            - Mode 1:Gr Value
* \param[out] pnMg_YeVal    Pointer to UINT32_TYPE that will receive the value:
                            - Mode 0:Mg/Ye
                            - Mode 1:Mg Value
* \param[out] pnCy_MgVal    Pointer to UINT32_TYPE that will receive the value:
                            - Mode 0:Cy/Mg
                            - Mode 1:Cy Value
* \param[out] pnYe_GrVal    Pointer to UINT32_TYPE that will receive the value: 
                            - Mode 0:Ye/Gr
                            - Mode 1:Ye Value

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the whitebalance feature for GMCY sensors</dd></dl></member>
        <member name="M:UCC_Whitebalance_Get(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*)">
* Get the status of the Whitebalance feature.
* Parameters the caller is not interested in may be set to NULL.
*
* \param[in]  CamId         The ID of the camera to use
* \param[out] pnMode        Pointer to ::UINT32_TYPE that will receive the current mode
*                           of operation. Possible values are:
                            - ::E_FEATSTATE_OFF
                            - ::E_FEATSTATE_ON
                            - ::E_FEATSTATE_AUTO
* \param[out] pnUBVal       Pointer to ::UINT32_TYPE that will receive the U or B value
* \param[out] pnVRVal       Pointer to ::UINT32_TYPE that will receive the V or R value

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the whitebalance feature</dd></dl>
* Get the status of the Whitebalance feature.
* Parameters the caller is not interested in may be set to NULL.
*
* \param[in]  CamId         The ID of the camera to use
* \param[out] pnMode        Pointer to ::UINT32_TYPE that will receive the current mode
*                           of operation. Possible values are:
                            - ::E_FEATSTATE_OFF
                            - ::E_FEATSTATE_ON
                            - ::E_FEATSTATE_AUTO
* \param[out] pnUBVal       Pointer to ::UINT32_TYPE that will receive the U or B value
* \param[out] pnVRVal       Pointer to ::UINT32_TYPE that will receive the V or R value

* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the whitebalance feature</dd></dl></member>
        <member name="M:UCC_Hue_Get(System.UInt32,System.Single*)">
* Get the hue value in degrees.
*
* \param[in]     CamId          The ID of the camera to use
* \param[out]    pfValue        The current hue value in degrees

* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the hue feature</dd></dl></member>
        <member name="M:UCC_Hue_Set(System.UInt32,System.Single*)">
* Set the hue value in degrees.
*
* \param[in]     CamId          The ID of the camera to use
* \param[in,out] pfValue        The hue value in degrees so set. On return this parameter contains 
*                               the actual hue value stored in the camera which might be different,
*                               if the unit size or allowed range is violated (see ::UCC_Hue_GetInfo)

* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the hue feature</dd></dl></member>
        <member name="M:UCC_Hue_GetInfo(System.UInt32,System.Single*,System.Single*,System.Single*)">
 * Get info about the hue feature.
 * Parameters the caller is not interested in may be set to NULL.
 *
 * \param[in]   CamId           The ID of the camera
 * \param[out]  pfMinValue      A pointer to ::FLOAT_TYPE that will receive the
 *                              minimum value for the hue feature (may be NULL)
 * \param[out]  pfMaxValue      A pointer to ::FLOAT_TYPE that will receive the
 *                              maximum value for the hue feature (may be NULL)
 * \param[out]  pfUnitSize      A pointer to ::FLOAT_TYPE that will receive the
 *                              step size for changing the hue feature (may be NULL)

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the Hue feature</dd></dl></member>
        <member name="M:UCC_AutoFunctionAOI_Get(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
* Get the current status of the Autofunction AOI feature.
* Parameters the caller is not interested in may be set to NULL.
*
* \param[in]  CamId         The ID of the camera to use
* \param[out] pnMode        Pointer to ::UINT32_TYPE that will receive the current
*                           mode of operation. For valid values see ::E_UNI_FEATURE_STATE.
* \param[out] pnTop         Pointer to ::UINT32_TYPE that will receive the top offset
* \param[out] pnLeft        Pointer to ::UINT32_TYPE that will receive the left offset
* \param[out] pnHeight      Pointer to ::UINT32_TYPE that will receive the height
* \param[out] pnWidth       Pointer to ::UINT32_TYPE that will receive the width

* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if autofunction AOI is not supported by the camera</dd></dl></member>
        <member name="M:UCC_AutoFunctionAOI_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
* Configure the AutoFunction AOI feature.
* Set the AOI that is used for controlling the "auto" mode of \ref ::E_FEAT_SHUTTER "Shutter"
* and \ref ::E_FEAT_GAIN "Gain" features.
*
* \param[in] CamId         The ID of the camera to use
* \param[in] nMode         Mode of operation see: ::E_UNI_FEATURE_STATE
* \param[in] nLeft         Left offset of the AutoFunction AOI (will be left untouched if set to ::UNI_UNDEFINED)
* \param[in] nTop          Top offset of the AutoFunction AOI (will be left untouched if set to ::UNI_UNDEFINED)
* \param[in] nWidth        Width of the AutoFunction AOI (will be left untouched if set to ::UNI_UNDEFINED)
* \param[in] nHeight       Height of the AutoFunction AOI (will be left untouched if set to ::UNI_UNDEFINED)

* \return <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*         <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if AutoFunction AOI is not supported by the camera</dd>
*         <dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if nMode is set to a value not supported</dd>
*         <dt>::UNI_RESULT_PARAMETER_INVALID_3</dt><dd>if nLeft is greater than the current image width</dd>
*         <dt>::UNI_RESULT_PARAMETER_INVALID_4</dt><dd>if nTop is geater than the current image height</dd>
*         <dt>::UNI_RESULT_PARAMETER_INVALID_5</dt><dd>if nWidth is greater than the current image width minus
*                                              the AutoFunction AOI left value</dd>
*         <dt>::UNI_RESULT_PARAMETER_INVALID_6</dt><dd>if nHeight is greater than the current image height minus
*                                              the AutoFunction AOI top value</dd></dl></member>
        <member name="M:UCC_SerialIO_Receive(System.UInt32,System.Byte*,System.UInt32*,System.UInt32)">
 * Receive data via the cameras serial I/O interface.
 *
 * \param[in] CamId         the ID of the camera to use
 * \param[out] pnData       pointer to the data buffer to be filled
 * \param pnLength          pointer to UINT32_TYPE that indicates the number of bytes to be received
 *                          after function call this value will be set to the number of bytes actually received
 * \param[in] nTimeout      the maximum period of time in milliseconds this function will wait for the data
 *                          to be received

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if serial I/O is not supported by the camera</dd>
 *          <dt>::UNI_RESULT_CAMERA_TIMEOUT</dt><dd>if the timeout condition occurred before all data was received</dd></dl></member>
        <member name="M:UCC_SerialIO_ReceiveInfo(System.UInt32,System.UInt32*,System.UInt32*)">
Get information of the internal state of the SIO receiver.
\param[in]    CamId           the ID of the camera to use
\param[out]   pbRxStatus      bool receiver state ready
\param[out]   puBytesPending  number of bytes pending for delivery

</member>
        <member name="M:UCC_SerialIO_Transmit(System.UInt32,System.Byte*,System.UInt32*,System.UInt32)">
 * Transmit data via the camera's serial I/O interface.
 *
 * \param[in] CamId         the ID of the camera to use
 * \param[in] pnData        pointer to the data to be transmitted
 * \param pnLength          pointer to UINT32_TYPE that indicates the number of bytes to transmit
 *                          after function call this value will be set to the number of bytes actually transmitted
 * \param[in] nTimeout      the maximum period of time in milliseconds this function will wait for the data
 *                          to be transmitted

 * \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if serial I/O is not supported by the camera</dd>
 *           <dt>::UNI_RESULT_CAMERA_TIMEOUT</dt><dd>if the timeout condition occurred before all data was transmitted</dd></dl></member>
        <member name="M:UCC_SerialIO_GetConfig(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
 * Get the configuration of the serial interface.
 *
 * \param[in]  CamId         the ID of the camera to use
 * \param[out] pnBaudRate    pointer to UINT32_TYPE that will receive the configured baud rate 
 *                           see the definition of ::E_UNI_SIO_BAUDRATE for valid values
 * \param[out] pnCharLength  pointer to UINT32_TYPE that will receive the character length setting
 *                           possible values are 7 and 8
 * \param[out] pnParity      pointer to UINT32_TYPE that will receive the parity setting 
 *                           see the definition of ::E_UNI_SIO_PARITY for valid values
 * \param[out] pnStopBit     pointer to UINT32_TYPE that will receive the current stop bit setting 
 *                           see the definition of ::E_UNI_SIO_STOPBITS for valid values
 * \param[out] pnMode        pointer to UINT32_TYPE that will receive the current Receive/Transmit Mode
 *                           see the definition of ::E_UNI_SIO_MODE for valid values
 * \param[out] pnBufferSize  pointer to UINT32_TYPE that will receive the maximum size of the receive/transmit 
 *                      data buffer

 * \return              <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *                      <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if serial I/O is not supported by the camera</dd></dl></member>
        <member name="M:UCC_SerialIO_SetConfig(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 * Configure the serial communication.
 *
 * \param[in] CamId         the ID of the camera to use
 * \param[in] nBaudRate     the baud rate to be used  
 *                          see the definition of ::E_UNI_SIO_BAUDRATE for valid values
 * \param[in] nCharLength   character length setting, may be set to 7 or 8
 * \param[in] nParity       parity setting - see the definition of ::E_UNI_SIO_PARITY  
 *                          for valid values
 * \param[in] nStopBit      stop bits - see the definition of ::E_UNI_SIO_STOPBITS 
 *                          for valid values
 * \param[in] nMode         receive/transmit mode - see the definition of ::E_UNI_SIO_MODE 
 *                          for valid values

 * \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if serial I/O is not supported by the camera</dd>
 *           <dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if nBaudRate is set to an undefined value</dd>
 *           <dt>::UNI_RESULT_PARAMETER_INVALID_3</dt><dd>if nCharLength is set to something other than 7 or 8</dd>
 *           <dt>::UNI_RESULT_PARAMETER_INVALID_4</dt><dd>if nParity is set to an undefined value</dd>
 *           <dt>::UNI_RESULT_PARAMETER_INVALID_5</dt><dd>if nStopBit is set to an undefined value</dd>
 *           <dt>::UNI_RESULT_PARAMETER_INVALID_6</dt><dd>if nMode is set to an undefined value</dd></dl></member>
        <member name="M:UCC_OutputPin_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 * Set state of output pins.
 *
 * \param[in] CamId     The ID of the camera to use
 * \param[in] nWhich    The number of the output pin see ::MAX_OUTPUT_PINS
 * \param[in] nMode     The output mode see ::E_OUTPUTPIN_MODES
 * \param[in] nPolarity The polarity
 * \param[in] nState    The output state

 * \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the chosen output pin is not supported by
 *                                              the camera</dd></dl>
 * \note For general information see \ref digiotrig.

</member>
        <member name="M:UCC_OutputPin_Get(System.UInt32,System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*)">
 * Get state of output pins.
 *
 * \param[in]  CamId         The ID of the camera to use
 * \param[in]  nWhich        The number of the output pin see ::MAX_OUTPUT_PINS
 * \param[out] pnMode        Pointer to ::UINT32_TYPE that will receive the input mode see ::E_OUTPUTPIN_MODES
 * \param[out] pnPolarity    Pointer to ::UINT32_TYPE that will receive the polarity
 * \param[out] pnState       Pointer to ::UINT32_TYPE that will receive the output state

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the chosen output pin is not supported by
 *                                              the camera</dd></dl>
 * \note For general information see \ref digiotrig.

</member>
        <member name="M:UCC_InputPin_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 * Set state of input pins. The current states are returned.
 *
 * \param[in] CamId     The ID of the camera to use
 * \param[in] nWhich    The number of the input pin see UNI_MAX_INPUT_PINS
 * \param[in] nMode     The input mode see ::E_INPUTPIN_MODES
 * \param[in] nPolarity The polarity

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the chosen input pin is not supported by
 *          the camera</dd></dl>
 * \note For general information see \ref digiotrig.

</member>
        <member name="M:UCC_InputPin_Get(System.UInt32,System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*)">
 * Get state of input pins.
 *
 * \param[in]  CamId         The ID of the camera to use
 * \param[in]  nWhich        The number of the input pin see UNI_MAX_INPUT_PINS
 * \param[out] pnMode        Pointer to ::UINT32_TYPE that will receive the input mode see E_INPUTPIN_MODES
 * \param[out] pnPolarity    Pointer to ::UINT32_TYPE that will receive the polarity
 * \param[out] pnState       Pointer to ::UINT32_TYPE that will receive the input state

 * \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the chosen input pin is not supported by the camera</dd></dl>
 * \note For general information see \ref digiotrig.

</member>
        <member name="M:UCC_Shading_Build(System.UInt32,System.UInt32,System.UInt32)">
 * Invoke the build operation of a shading image inside the camera.
 * This function blocks until the build process finishes if nBlock is true.
 * Otherwise it returns without blocking. 
 * The camera must not be grabbing for this function to succeed.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFrames     The number of images to use for the build operation
 * \param[in]   nBlock      A "boolean" long value to determine whether the function should block until the
 *                      build process finishes

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the shading correction feature is not supported by the camera</dd>
 *          <dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera is grabbing</dd>
 *          <dt>::UNI_RESULT_CAMERA_TIMEOUT</dt><dd>if the camera is still busy building the shading image at the
 *          time the function returns</dd></dl></member>
        <member name="M:UCC_Shading_Download(System.UInt32,System.Byte*,System.UInt32*)">
 * Download shading correction image data from the camera.
 * The camera must not be grabbing for this function to succeed.
 *
 * \param[in]   CamId           The ID of the camera to use 
 * \param[out]  pImage          Pointer to an image data buffer that will be filled with 
 *                          the downloaded shading image (MONO8)
 * \param   pnImageSize     Pointer to an UNIT32_TYPE that will receive the size of 
 *                          the downloaded image (in bytes) 

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the shading correction feature is not supported by the camera</dd>
 *          <dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera is grabbing</dd></dl></member>
        <member name="M:UCC_Shading_Upload(System.UInt32,System.Byte*,System.UInt32)">
 * Upload shading correction image data into the camera.
 *  The camera must not be grabbing for this function to succeed.
 *
 * \param[in]   CamId           The ID of the camera to use 
 * \param[in]   pImage          Pointer to image data to be uploaded
 * \param[in]   nImageSize      The size of the image in bytes

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the shading correction feature is not supported by the camera</dd>
 *          <dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera is grabbing</dd></dl></member>
        <member name="M:UCC_Shading_GetInfo(System.UInt32,System.UInt32*)">
 * Get information about the capabilities of the shading correction feature.
 *
 * \param[in]   CamId           The ID of the camera to use 
 * \param[out]  puSizeMax      A pointer to an integer that will receive the
 *                          maximum size of a shading image

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the shading correction feature is not supported by the camera</dd></dl></member>
        <member name="M:UCC_Lut_LoadFromCsv(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*,System.UInt32,System.UInt32,System.UInt32)">
Load LUT to camera from CSV file.
\param[in] CamId    id of the camera to use
\param[in] pszPath  full path to the CSV file
\param[in] nLength  length of the path string
\param[in] nLutNr   number of LUT to load to
\param[in] nCsvPos  position of the data in the CSV file

</member>
        <member name="M:UCC_Lut_Upload(System.UInt32,System.UInt32,System.Byte*,System.UInt32)">
 Upload the given LUT.
  The data array may contain 8 bit or 16 bit values depending on the cameras capabilities.
  Use UCC_Lut_GetInfo to determine the appropriate data format. LUT data may only be uploaded when
  the camera is not grabbing.

 \param[in]   CamId           the ID of the camera to configure 
 \param[in]   nLutNr          the number of the LUT to use for storing the given data
 \param[out]  arrValue        pointer to the LUT data
 \param[in]   nSize           size of LUT data in bytes
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera is grabbing</dd></dl></member>
        <member name="M:UCC_Lut_Use(System.UInt32,System.UInt32,System.UInt32)">
 * Use the given look-up table and set the status.
 *
 * \param[in]   CamId           The ID of the camera to configure
 * \param[in]   nLutNo          The number of the LUT to use or ::UNI_UNDEFINED
 * \param[in]   nActive         A "boolean" ::UINT32_TYPE value to determine the activity status or ::UNI_UNDEFINED

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the LUT feature</dd>
 *          <dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if nLutNo exceeds the range of [0 ... (NumOfLuts-1)]</dd></dl></member>
        <member name="M:UCC_Lut_GetStatus(System.UInt32,System.UInt32*,System.UInt32*)">
 * Get the current status of the LUT feature.
 *
 * \param[in]   CamId           The ID of the camera to get information about
 * \param[out]  *pbActive       A pointer to a "boolean" ::UINT32_TYPE value to receive the current activity status, optional can be NULL.
 * \param[out]  *pnLutIndex     A pointer to an ::UINT32_TYPE to receive the current LUT number, optional can be NULL.

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the LUT feature</dd></dl></member>
        <member name="M:UCC_Lut_GetInfo(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
@name AVT-specific camera features. 
A set of methods to deal with AVT-specific camera features.

 * Get a number of details about the look-up table feature.
 * Parameters the caller is not interested in may be set to
 * NULL. However, not all parameters may be set to NULL.
 * The size of single LUT entries is either 8 or 16 bit depending
 * on pnMaxValue: cameras with pnMaxValue&gt;255 expect a LUT entry size
 * of 16 bit.
 *
 * \param[in]   CamId           The ID of the camera to configure
 * \param[out]  pnNumOfLuts     A pointer to ::UINT32_TYPE that will receive the
 *                              maximum number of look-up tables (may be NULL)
 * \param[out]  pnMaxValue      A pointer to ::UINT32_TYPE that will receive the
 *                              maximum value possible for a LUT entry (may be NULL)
 * \param[out]  pnNumOfValues   A pointer to ::UINT32_TYPE that will receive the
 *                              number of entries per lookup table (may be NULL)
 * \param[out]  pnMaxDataSize   A pointer to ::UINT32_TYPE that will receive the
 *                              maximum size of a LUT in bytes (may be NULL)

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera does not support the LUT feature</dd></dl></member>
        <member name="M:UCC_ColorCorrectionMatrix_Set(System.UInt32,System.Int32*)">
Set the color correction matrix.
\param[in]    CamId           The ID of the camera to use
\param[in]    pnCoefficients  A pointer to an array of at least 9 elements holding the coefficients
\return       S_OK if successful
\note The coefficients are expected in the following order: RR, GR, BR, RG, GG, BG, RB, GB, BB.

</member>
        <member name="M:UCC_ColorCorrectionMatrix_Get(System.UInt32,System.Int32*)">
Get the color correction matrix.
\param[in]    CamId           The ID of the camera to use
\param[out]   pnCoefficients  A pointer to an array of at least 9 elements where the coefficients will be stored
\return       S_OK if successful
\note The coefficients will be stored in the following order: RR, GR, BR, RG, GG, BG, RB, GB, BB.

</member>
        <member name="M:UCC_ColorCorrectionMatrix_SetCoefficient(System.UInt32,System.UInt32,System.UInt32,System.Int32)">
Set the value of the color correction matrix coefficient with the given ID.
\param[in]    CamId           The ID of the camera to use
\param[in]    nSrcColorIx     The color index for the source part of the transformation
\param[in]    nDstColorIx     The color index for the destination part of the transformation
\param[in]    nValue          The factor that will be set for the given coefficient
\return       S_OK if successful
\note The allowed values for the color indices are: 0 for red, 1 for green, and 2 for blue.
The RG coefficient controlling the original color's red part in the resulting green value for example can be
accessed by setting \c nSrcColorIx to 0 and \c nDstColorIx to 1.

</member>
        <member name="M:UCC_ColorCorrectionMatrix_GetCoefficient(System.UInt32,System.UInt32,System.UInt32,System.Int32*)">
Get the value of the color correction matrix coefficient with the given ID.
\param[in]    CamId           The ID of the camera to use
\param[in]    nSrcColorIx     The color index for the source part of the transformation
\param[in]    nDstColorIx     The color index for the destination part of the transformation
\param[out]   pnValue         A pointer to ::UINT32_TYPE that will receive the factor for the given coefficient
\return       S_OK if successful
\note The allowed values for the color indices are: 0 for red, 1 for green, and 2 for blue.
The RG coefficient controlling the original color's red part in the resulting green value for example can be
accessed by setting \c nSrcColorIx to 0 and \c nDstColorIx to 1.

</member>
        <member name="M:UCC_ColorCorrectionMatrix_GetInfo(System.UInt32,System.Int32*,System.Int32*)">
Get the minimum and maximum value for a color correction matrix coefficient.
\param[in]    CamId           The ID of the camera to use
\param[out]   pnMin           A pointer to ::UINT32_TYPE that will receive the minimum value 
                              (NULL means the minimum will not be returned)
\param[out]   pnMax           A pointer to ::UINT32_TYPE that will receive the maximum value
                              (NULL means the maximum will not be returned)
\return       S_OK if successful

</member>
        <member name="M:UCC_SetDataBitRate(System.UInt32,System.UInt32)">
Set data bit rate for image streaming.
\param[in]    CamId       The ID of the camera to use
\param[in]    nSpeed      Data bit rate
                          (see ::E_UNI_DATABITRATE for valid values)
\return       S_OK if successful

</member>
        <member name="M:UCC_GetDataBitRate(System.UInt32,System.UInt32*)">
Get current data bit rate for image streaming.
\param[in]    CamId       The ID of the camera to use
\param[out]   pnSpeed     A pointer to ::UINT32_TYPE that will receive the currently used data bit rate
                          (see ::E_UNI_DATABITRATE for valid values)
\return       S_OK if successful

</member>
        <member name="M:UCC_GetDataBitRateMax(System.UInt32,System.UInt32*)">
Get maximum data bit rate for image streaming.
\param[in]    CamId       The ID of the camera to use
\param[out]   nMaxSpeed   A pointer to ::UINT32_TYPE that will receive the maximum supported data bit rate
                          (see ::E_UNI_DATABITRATE for valid values)
\return       S_OK if successful

</member>
        <member name="M:UCC_SetStreamSpeed(System.UInt32,System.UInt32)">
Set speed for 1394 image streaming.
\param[in]    CamId       The ID of the camera to use
\param[in]    nSpeed      FireWire speed (see ::E_UNI_1394_SPEED for valid values)
\return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera is connected to a GigE interface</dd></dl>
\note This method is for FireWire only and therefore deprecated, use \ref UCC_SetDataBitRate for all 
interface types.

</member>
        <member name="M:UCC_GetStreamSpeed(System.UInt32,System.UInt32*)">
Get current speed for 1394 image streaming.
\param[in]    CamId       The ID of the camera to use
\param[out]   pnSpeed     A pointer to ::UINT32_TYPE that will receive the currently used FireWire speed
                          (see ::E_UNI_1394_SPEED for valid values)
\return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera is connected to a GigE interface</dd></dl>
\note This method is for FireWire only and therefore deprecated, use \ref UCC_GetDataBitRate for all 
interface types.

</member>
        <member name="M:UCC_GetStreamSpeedMax(System.UInt32,System.UInt32*)">
Get maximum speed for 1394 image streaming.
\param[in]    CamId       The ID of the camera to use
\param[out]   nMaxSpeed   A pointer to ::UINT32_TYPE that will receive the maximum supported streaming speed
                          (see ::E_UNI_1394_SPEED for valid values)
\return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if the camera is connected to a GigE interface</dd></dl>
\note This method is for FireWire only and therefore deprecated, use \ref UCC_GetDataBitRateMax for all 
interface types.

</member>
        <member name="M:UCC_GetFeatureMax(System.UInt32,System.UInt32,System.UInt32*)">
 Get the maximum value of the mentioned feature.

 \param[in]   CamId       The ID of the camera to use 
 \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 \param[out]  pnValue     A pointer to ::UINT32_TYPE that will receive the features maximum value
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not imply a value</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd></dl>
 \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_GetFeatureMin(System.UInt32,System.UInt32,System.UInt32*)">
 * Get the minimum value of the mentioned feature.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 * \param[out]  pnValue     A pointer to ::UINT32_TYPE that will receive the features minimum value

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not imply a value</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_SetFeatureValue(System.UInt32,System.UInt32,System.UInt32)">
 * Set the value of the mentioned feature.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 * \param[in]   nValue      The value of the feature to set

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not imply a value or the value is read-only</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd>
 *          <dt>::UNI_RESULT_PARAMETER_INVALID_3</dt><dd>if nValue exceeds the range allowed for the chosen feature</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_GetFeatureValue(System.UInt32,System.UInt32,System.UInt32*)">
 * Get the value of the mentioned feature.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 * \param[out]  pnValue     A pointer to ::UINT32_TYPE that will receive the features current value

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not imply a value</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_SetFeatureStatus(System.UInt32,System.UInt32,System.UInt32)">
 * Set the status of the mentioned feature.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 * \param[in]   nState      Status of the feature to set ( see ::E_UNI_FEATURE_STATE for valid values )

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not support the state nState</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature isn't supported by the camera</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_GetFeatureStatus(System.UInt32,System.UInt32,System.UInt32*)">
 * Get the status of the mentioned feature.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 * \param[out]  pnState     A pointer to ::UINT32_TYPE that will receive the status of the feature.
 *                          ( see ::E_UNI_FEATURE_STATE for valid values )

 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
 *          <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature isn't supported by the camera</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_HasFeatureStatus(System.UInt32,System.UInt32,System.UInt32)">
 Check whether a specific state is supported for the mentioned feature.

 \param[in]   CamId       The ID of the camera to use 
 \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )
 \param[in]   nState      status of the feature to be checked ( see ::E_UNI_FEATURE_STATE for 
                          valid values )
 \return  <dl><dt>S_OK</dt><dd>if feature nFeature supports the state nState</dd><dt>::UNI_RESULT_INVALID_FEATURECAPABILITY</dt><dd>if feature nFeature does not support the state nState</dd><dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd></dl>
 For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_HasFeature(System.UInt32,System.UInt32)">
 * Check whether a specific unified feature is supported by the camera.
 *
 * \param[in]   CamId       The ID of the camera to use 
 * \param[in]   nFeature    ID of the feature ( see ::E_UNI_FEATURE for valid values )

 * \return  <dl><dt>S_OK</dt><dd>if feature nFeature is supported by the camera</dd>
 *           <dt>::UNI_RESULT_FEATURE_NOT_SUPPORTED</dt><dd>if feature nFeature is not supported by the camera</dd></dl>
 * \note For general information about unified features see \ref unifeat.

</member>
        <member name="M:UCC_ReadBlock(System.UInt32,System.UInt32,System.UInt32*,System.UInt32)">
 Read memory block from camera.

 \param[in]   CamId     The ID of the camera to use 
 \param[in]   uAddress  The start address of the register block
 \param[out]  pnValue   Pointer to ::UINT32_TYPE array that receives the registers values
 \param[in]   uLength   element count to read
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_WriteBlock(System.UInt32,System.UInt32,System.UInt32*,System.UInt32)">
 Write memory block to camera.

 \param[in]   CamId     The ID of the camera to use 
 \param[in]   uAddress  The start address of the register block
 \param[out]  pnValue   Pointer to ::UINT32_TYPE array that holds the registers values
 \param[in]   uLength   element count to write
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetRegister(System.UInt32,System.UInt32,System.UInt32*)">
 Get the value of the given register.

 \param[in]   CamId     The ID of the camera to use 
 \param[in]   uAddress  The address of the register
 \param[out]  pnValue   Pointer to ::UINT32_TYPE that receives the registers value
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_SetRegister(System.UInt32,System.UInt32,System.UInt32)">
 Set the given register value.

 \param[in]   CamId     The ID of the camera to use 
 \param[in]   uAddress  The address of the register
 \param[in]   uValue    The new value to set
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_SaveSettingsEx(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
 Save the camera settings as a template for the camera family to a file.

 \param[in]   CamId                   Camera identifier (serial number)
 \param[in]   pszFileName             The name of the file to use for storing settings
 \param[in]   nSelectMode             See ::E_UNI_SETTINGS_SELECT_MODE
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_SaveSettings(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*)">
 Save the camera settings to a file.

 \param[in]   CamId           Camera identifier (serial number)
 \param[in]   pszFileName     The name of the file to use for storing settings
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_LoadSettings(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*,System.UInt32)">
@name Camera configuration. 
A set of methods to deal with camera features and how to set them.

* Load the camera settings from file.
 *
 * \param[in]   CamId               Camera identifier (serial number)
 * \param[in]   pszFileName         The name of the file to use for storing settings
 * \param[in]   nSelectMode         See ::E_UNI_SETTINGS_SELECT_MODE
 * \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetNativeImageEx(System.UInt32,System.Byte*,S_UNI_TRANSPORT_FORMAT_INFO*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Request image data from the async receive buffer in native transport format.
 Image format conversions may be required to retrieve displayable data.
 For certain color codes (mostly 'deep' image formats), the output format
 differs between 1394 DCAM and GigE-Vision cameras.
 Relevant format details are returned in psFormatInfo.
 A byte pointer for the target image is used. Sufficient memory space has to be provided by the caller.

 \param[in]    CamId          Camera identifier (serial number)
 \param[out]   pImage         Pointer to image bitmap
 \param[out]   psFormatInfo   returns detailed input format specifications
 \param[out]   pnFrameCount   returns frame count - may be NULL
 \param[in]    nTimeOut       timeout
 \param[out]   pSISData       Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetNativeImage(System.UInt32,System.Byte*,System.UInt32,S_SIS_DATA*)">
 Request image data from the async receive buffer in transport format as supported by 1394 DCAM cameras. 
 Image format conversions may be required to retrieve displayable data.
 This function is provided for backwards compatibility. GigE-Vision image frames are converted to match 
 DCAM image formats, when necessary (introducing processing overhead). Use ::UCC_GetNativeImageEx to obtain 
 data in actual, interface-dependent transport format.
 A byte pointer for the target image is used. Sufficient memory space has to be provided by the caller.

 \param[in]    CamId       Camera identifier (serial number)
 \param[out]   pImage      Pointer to image bitmap
 \param[in]    nTimeOut    timeout
 \param[out]   pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetPlanesImage(System.UInt32,System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.UInt32,S_SIS_DATA*)">
 Request a planes image from async receive buffer.
  Uses byte pointers for all the target planes. Sufficient memory space has to be provided by the caller.

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pR          The pointer to the red plane
 \param[out]  pG          The pointer to the green plane 
 \param[out]  pB          The pointer to the blue plane
 \param[out]  pY          The pointer to the intensity plane ( may be set to NULL )
 \param[in]   nTimeOut    timeout
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetBitmap24ImageEx(System.UInt32,System.Byte*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Request a bitmap from the async receive buffer.
 Uses a byte pointer for the target image. Sufficient memory space has to be provided by the caller.
 Image data is always returned in 24 Bit BGR format. Where applicable, debayering is performed.

 \param[in]    CamId         Camera identifier (serial number)
 \param[out]   pImage        Pointer to image bitmap
 \param[out]   pnFrameCount  returns frame count - may be NULL
 \param[in]    nTimeOut      timeout
 \param[out]   pSISData      Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetBitmap8ImageEx(System.UInt32,System.Byte*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Request a bitmap from the async receive buffer.
 Uses a byte pointer for the target image. Sufficient memory space has to be provided by the caller.
 Image data is always returned in 8 Bit Mono format. Where applicable, debayering and/or 
 color-to-greyscale conversions are performed.

 \param[in]    CamId         Camera identifier (serial number)
 \param[out]   pImage        Pointer to image bitmap
 \param[out]   pnFrameCount  returns frame count - may be NULL
 \param[in]    nTimeOut      timeout
 \param[out]   pSISData      Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetBitmapImage(System.UInt32,System.Byte*,System.UInt32,S_SIS_DATA*)">
 Request a bitmap from the async receive buffer.
 Uses a byte pointer for the target image. Sufficient memory space has to be provided by the caller.
 This function is provided for backwards compatibility. Raw formats are not debayered, but instead
 treated as mono formats. Output data is in BGR24 or Mono8 format, depending on the camera's color code.
 It is not recommended to use this function for new code. Instead, ::UCC_GetBitmap8ImageEx and 
 ::UCC_GetBitmap24ImageEx should be used.

 \param[in]    CamId       Camera identifier (serial number)
 \param[out]   pImage      Pointer to image bitmap
 \param[in]    nTimeOut    timeout
 \param[out]   pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetImageEx(System.UInt32,S_UNI_IMAGE*,S_UNI_TRANSPORT_FORMAT_INFO*,System.UInt32*,System.UInt32)">
 Request an image from the async receive buffer.
 The image format has to be specified by the \ref S_UNI_IMAGE::uImageformat "uImageformat"
 member of the provided ::S_UNI_IMAGE struct. The corresponding data pointers of psImage
 have to point to sufficiently sized memory buffers.

 \param[in]    CamId         Camera identifier (serial number)
 \param[in]    psImage       Pointer to image struct \see ::S_UNI_IMAGE
 \param[out]   psFormatInfo  Will be filled with image format details, required for ::E_IF_NATIVE_EX output format. \see ::S_UNI_TRANSPORT_FORMAT_INFO
 \param[out]   pnFrameCount  returns frame count - may be NULL
 \param[in]    nTimeOut      timeout in ms
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
 \note Alternatively one the following functions may be used to acquire
 image data in a certain format:
 <ul><li>::UCC_GetBitmapImage() - returns image data in bitmap format</li><li>::UCC_GetPlanesImage() - returns image data in planar format</li><li>::UCC_GetNativeImageEx() - returns image data in native format, as transfered 
 by the camera</li></ul></member>
        <member name="M:UCC_GetImage(System.UInt32,S_UNI_IMAGE*,System.UInt32)">
 Request an image from the async receive buffer.
 The image format has to be specified by the \ref S_UNI_IMAGE::uImageformat "uImageformat"
 member of the provided ::S_UNI_IMAGE struct. The corresponding data pointers of psImage
 have to point to sufficiently sized memory buffers.
 This function is provided for backwards compatibility. When using GigE-Vision 
 cameras and ::E_IF_NATIVE_EX output format, necessary format details are not returned.
 Instead, ::UCC_GetImageEx should be used.

 \param[in]    CamId       Camera identifier (serial number)
 \param[in]    psImage     Pointer to image struct \see ::S_UNI_IMAGE
 \param[in]    nTimeOut    timeout in ms
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
 \note Alternatively one the following functions may be used to acquire
 image data in a certain format:
 <ul><li>::UCC_GetBitmapImage() - returns image data in bitmap format</li><li>::UCC_GetPlanesImage() - returns image data in planar format</li><li>::UCC_GetNativeImageEx() - returns image data in native format, as transfered 
 by the camera</li></ul></member>
        <member name="M:UCC_GrabStop(System.UInt32,System.UInt32)">
 Stops the grabbing of images.

 \param[in]    CamId       Camera identifier (serial number)
 \param[in]    nStopOption Option for stopping: 0 just stops grabbing (delivering all pending images), 1 cancels
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_MultiShotStop(System.UInt32)">
Stopping a running multi shot.
* \param[in]    CamId       Camera identifier (serial number)
* \return <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
* \note Get[xxx]Image might afterwards return UNI_RESULT_COMMAND_ABORTED

</member>
        <member name="M:UCC_MultiShotStart(System.UInt32,System.UInt32)">
 Start multi shot image grabbing asynchronously.

 \param[in]    CamId       Camera identifier (serial number)
 \param[in]    nGrabCount  Image count to grab (has to be greater then 0)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabStart(System.UInt32,System.UInt32)">
 Start grabbing images asynchronously.

 \param[in]    CamId       Camera identifier (serial number)
 \param[in]    nTimeOut    timeout to wait for image to be ready
 \note dependent on ::SetFeatureState with ::E_FEAT_STARTIMMEDIATELY the ISO stream is started or delayed.
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabNativeImageEx(System.UInt32,System.Byte*,S_UNI_TRANSPORT_FORMAT_INFO*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously in native transport format.
 Image format conversions may be required to retrieve displayable data.
 For certain color codes (mostly 'deep' image formats), the output format
 differs between 1394 DCAM and GigE-Vision cameras. Relevant format details are returned 
 in psFormatInfo.
 A byte pointer for the target image is used.
 Sufficient memory space has to be provided by the caller.


 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pImage      The pointer to the image
 \param[out]   psFormatInfo  Will be filled with image format details, required for ::E_IF_NATIVE_EX output format. \see ::S_UNI_TRANSPORT_FORMAT_INFO
 \param[out]  pbIsColor   returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabNativeImage(System.UInt32,System.Byte*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously in transport format as supported by 1394 DCAM cameras. 
 Image format conversions may be required to retrieve displayable data.
 This function is provided for backwards compatibility. GigE-Vision image frames are converted to match 
 DCAM image formats, when necessary (introducing processing overhead). Use ::UCC_GetNativeImageEx to obtain 
 data in actual, interface-dependent transport format.
 A byte pointer for the target image is used. Sufficient memory space has to be provided by the caller.

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pImage      The pointer to the image
 \param[out]  pbIsColor   returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabPlanesImage(System.UInt32,System.Byte*,System.Byte*,System.Byte*,System.Byte*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously into a planes image.
 Uses byte pointers for all target planes.
  
 \note Raw images are not debayered yet. Use debayering methods afterwards.
 \since 0.1.2

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pR          The pointer to the red plane
 \param[out]  pG          The pointer to the green plane 
 \param[out]  pB          The pointer to the blue plane
 \param[out]  pY          The pointer to the intensity plane ( may be set to NULL )
 \param[out]  pbIsColor   Returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabBitmap24Image(System.UInt32,System.Byte*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously in 24 BPP color bitmap format.
 Uses a byte pointer for the target image. Sufficient memory space has to be provided by the caller.
 Image data is always returned in 24 Bit BGR format. Where applicable, debayering is performed.

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pImage      The pointer to the image
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabBitmap8Image(System.UInt32,System.Byte*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously in 8 BPP grayscale format.
 Uses a byte pointer for the target image. Sufficient memory space has to be provided by the caller.
 Image data is always returned in 8 Bit Mono format. Where applicable, debayering and/or 
 color-to-grayscale conversions are performed.

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pImage      The pointer to the image
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabBitmapImage(System.UInt32,System.Byte*,System.UInt32*,System.UInt32,S_SIS_DATA*)">
 Grab a single image synchronously in Windows bitmap format.
 Uses a byte pointer for the target image.
 This function is provided for backwards compatibility. Raw formats are not debayered, but instead
 treated as mono formats. Output data is in BGR24 or Mono8 format, depending on the camera's color code.
 It is not recommended to use this function for new code. Instead, ::UCC_GrabBitmap8Image and 
 ::UCC_GrabBitmap24Image should be used.

 \param[in]   CamId       Camera identifier (serial number)
 \param[out]  pImage      The pointer to the image
 \param[out]  pbIsColor   Returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]   nTimeOut    Timeout in ms
 \param[out]  pSISData    Optional secure image signature data (set \ref DESC_FEAT_SIS "E_FEAT_SIS" to \ref E_FEATSTATE_ON to receive images with embedded signature)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GrabImageEx(System.UInt32,S_UNI_IMAGE*,S_UNI_TRANSPORT_FORMAT_INFO*,System.UInt32*,System.UInt32)">
 Synchronously grab a single image.
 The image format has to be specified by the \ref S_UNI_IMAGE::uImageformat "uImageformat"
 member of the provided ::S_UNI_IMAGE struct. The corresponding data pointers of psImage
 have to point to sufficiently sized memory buffers.

 \param[in]    CamId         Camera identifier (serial number)
 \param[in]    psImage       Pointer to image struct (see ::S_UNI_IMAGE)
 \param[out]   psFormatInfo  Will be filled with image format details, required for ::E_IF_NATIVE_EX output format (see ::S_UNI_TRANSPORT_FORMAT_INFO)
 \param[out]   pbIsColor     Returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]    nTimeOut      Timeout in ms
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
 \note Alternatively one the following functions may be used to acquire
 image data in a certain format:
 <ul><li>::UCC_GrabBitmap8Image() - returns grayscale image data in bitmap format</li><li>::UCC_GrabBitmap24Image() - returns color image data in bitmap format</li><li>::UCC_GrabPlanesImage() - returns image data in planar format</li><li>::UCC_GrabNativeImageEx() - returns image data in native format, as transfered 
 by the camera</li></ul></member>
        <member name="M:UCC_GrabImage(System.UInt32,S_UNI_IMAGE*,System.UInt32*,System.UInt32)">
 Synchronously grab a single image.
 The image format has to be specified by the \ref S_UNI_IMAGE::uImageformat "uImageformat"
 member of the provided ::S_UNI_IMAGE struct. The corresponding data pointers of psImage
 have to point to sufficiently sized memory buffers.
 This function is provided for backwards compatibility. When using GigE-Vision 
 cameras and ::E_IF_NATIVE_EX output format, necessary format details are not returned.
 Instead, ::UCC_GrabImageEx should be used.

 \param[in]    CamId       Camera identifier (serial number)
 \param[out]   psImage     Pointer to image struct
 \param[out]   pbIsColor   returns "boolean" ::UINT32_TYPE value indicating if output data contains multiple values per pixel
 \param[in]    nTimeOut    Timeout in ms
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetFreeBandwidth(System.UInt32,System.UInt32*)">
Get the free bandwidth.
*   The free bandwidth for the bus the camera is connected to is returned.
*   \param[in]  CamId        Camera identifier (serial number)
*   \param[out] pnBandwidth  Pointer to ::UINT32_TYPE receiving the free bandwith for the bus the camera is connected to
*   \return <dl><dt>S_OK</dt><dd>if all parameter are valid</dd></dl></member>
        <member name="M:UCC_SetAOI(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
Set AOI for the current scalable mode to the camera.
* \param[in,out]    CamId       Camera identifier
* \param[in,out]    pLeft       left corner of the AOI
* \param[in,out]    pTop        top corner of the AOI
* \param[in,out]    pWidth      width of the AOI
* \param[in,out]    pHeight     height of the AOI
* \returns
* \note This function only succeeds for free modes(scalebel modes).
* Setting the AOI to a grabbing camera will try to fast resize the DMA, this will only succeed if the new AOI will use less data for transport then the one set with UCC_PrepareFreeGrab.
* IMPORTAINT: due to unreliability of fast resize, this functionality will be deactivated and in case of running camera an error will be returned.
* Setting the AOIwhile the camera is not grabbing will set the AOI for the current scaleable mode.

</member>
        <member name="M:UCC_SetAOIPosition(System.UInt32,System.UInt32*,System.UInt32*)">
 Set AOI position only - image size will not be changed. This function is most useful when called
 during image acquisition. For general AOI configuration see ::UCC_PrepareFreeGrab.

 \param[in]    CamId       Camera identifier (serial number)
 \param[in,out]   pLeft    Pointer to new horizontal position - invalid data will be adjusted during function call. NULL will leave position unchanged.
 \param[in,out]   pTop    Pointer to new vertical position - invalid data will be adjusted during function call. NULL will leave position unchanged.
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetAOI(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
 Get the AOI for the current free IIDC mode (Format 7).
 Every AOI parameter may be omitted.

 \param[in]    CamId       Camera identifier (serial number)
 \param[out]   pLeft       Pointer to ::UINT32_TYPE receiving the left position of the current AOI.
 \param[out]   pTop        Pointer to ::UINT32_TYPE receiving the top position of the current AOI.
 \param[out]   pWidth      Pointer to ::UINT32_TYPE receiving the width of the current AOI.
 \param[out]   pHeight     Pointer to ::UINT32_TYPE receiving the height of the current AOI.
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetSupportedFixedFormats(System.UInt32,S_IIDC_VIDEOINFO_FORMAT*)">
Query the supported IIDC video modes of a camera.
* Used to identify the IIDC video modes implemented in the camera.
* Setting one of the modes can fail due to the current set bus speed.
* \param[in]  CamId       Camera identifier (serial number)
* \param[out] pFormats    pointer to a ::S_IIDC_VIDEOINFO_FORMAT struct

</member>
        <member name="M:UCC_PrepareFreeGrab(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
@name Image acquisition. 
A set of methods to deal with the acquisition of images.

* Prepare camera for format-free image acquisition.
* May be used for setting grabbing parameters for preparing your
*  image transport in just one call.
*
* \param[in]     CamId           Camera identifier (serial number)
* \param[in,out] pMode           As a rule, use the format 7 mode
* \param[in,out] pColorFormatId  Pointer to the color id (see ::E_UNI_COLOR_CODE)
* \param[in,out] pWidth          Pointer to the height of the image
* \param[in,out] pHeight         Pointer to the width of the image
* \param[in,out] pBufferCount    Pointer to the number of buffer (if NULL, the pre-defined value is taken)
* \param[in,out] pXOffset        Pointer to the horizontal position of the image
                            (if NULL, 0 is assumed)
* \param[in,out] pYOffset        Pointer to the vertical position of the image
                            (if NULL, 0 is assumed)
* \param[in,out] pBusLoad        Pointer to the desired bus load (in 1/1000) (return: the real bus load)
                            (if NULL, 1000 is assumed)
* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
* \note     This method automatically sets the packet size via the \c pBusload parameter, which is representing
            the payload in per mille.<ul><li>
            For FireWire, the following calculation is used:
            \code PacketSize = ROUND(Busload) x (1024 x 2^PhySpeed)/4000) x 4 \endcode
            Where PhySpeed is
            -   0 for S100,
            -   1 for S200,
            -   2 for S400,
            -   3 for S800.</li><li>
            For GigE, the full bandwidth is assumed.</li></ul>
            For examples see \ref codeformat.

</member>
        <member name="M:UCC_EnumFreeModes(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
 Enumerate through available free/scalable image formats.
 Start the complete enumeration by setting the index of the format to 0. On return,
 this value is incremented. If nothing is found, -1 is returned in *pnPos

 \param[in]     CamId       Camera identifier (serial number)
 \param[in,out] pnPos       Pointer to ::UINT32_TYPE holding the index of the format. Is updated after the call.
 \param[out]    pnMode      Pointer to ::UINT32_TYPE receiving the mode
 \param[out]    pnColorId   Pointer to ::UINT32_TYPE receiving the color coding id, see: ::E_UNI_COLOR_CODE
 \param[out]    pnXMax      Pointer to ::UINT32_TYPE receiving the maximum width in this mode
 \param[out]    pnYMax      Pointer to ::UINT32_TYPE receiving the maximum width in this mode
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetFreeModeInfo(System.UInt32,System.UInt32,System.UInt32,System.UInt32*)">
Retrieve information about the free format (IIDC:format 7) parameters of a given mode.
* \param[in]    CamId       Camera identifier (serial number)
* \param[in]    nMode       Mode returned from ::UCC_EnumFreeModes()
* \param[in]    nParamId    ::E_UNI_FREE_MODE_INFO identifier of the parameter 
* \param[out]   pnValue     returns the requested value
*\return    <dl><dt>S_OK</dt><dd>if the operation was successful)</dd></dl></member>
        <member name="M:UCC_GetCurrentFixedFormat(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*)">
 Get current IIDC format properties.

 \param[in]    CamId       Camera identifier (serial number)
 \param[out]   pFormat     Pointer to ::UINT32_TYPE receiving the current IIDC format
 \param[out]   pMode       Pointer to ::UINT32_TYPE receiving the current IIDC mode
 \param[out]   pFpsColId   Pointer to ::UINT32_TYPE receiving the current FPS id or color coding id see: :::E_UNI_FRAME_RATE
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetCurrentImageFormat(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
 Get current image format properties.

 \param[in]    CamId            Camera identifier (serial number)
 \param[out]   pnImageWidth     Pointer to ::UINT32_TYPE receiving the current image width
 \param[out]   pnImageHeight    Pointer to ::UINT32_TYPE receiving the current image height
 \param[out]   pnColorCoding    Pointer to ::UINT32_TYPE receiving the current color coding id see: ::E_UNI_COLOR_CODE
 \param[out]   pnImageDepth     Pointer to ::UINT32_TYPE receiving the current image depth
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_IpConfiguration_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
 Set current IP configuration of the specified camera.

 \note This method is for GigE only.

 \param[in]        CamId                   camera identifier (serial number)
 \param[in]        nConfigMode             IP configuration mode to set (see ::E_UNI_IP_CONFIGURATION_MODE)
 \param[in]        nIpAddress              IP address to set (if nConfigMode is set to persistent IP only)
 \param[in]        nSubnetMask             subnet mask to set (if nConfigMode is set to persistent IP only)
 \param[in]        nDefaultGateway         default gateway to set (if nConfigMode is set to persistent IP only)
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera has been opened by an application on the local or any remote host</dd></dl>
 \note    The values for the parameter nIpAddress, nSubnetMask, and nDefaultGateway should be given in network byte order (i.e. big endian).

</member>
        <member name="M:UCC_IpConfiguration_Get(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
* Get current IP configuration of the specified camera.
*
* \note This method is for GigE only.
*
* \param[in]        CamId                   camera identifier (serial number)
* \param[out]       pnConfigMode            current IP configuration mode (see ::E_UNI_IP_CONFIGURATION_MODE)
* \param[out]       pnSupportedConfigMode   supported IP configuration modes (see ::E_UNI_IP_CONFIGURATION_MODE)
* \param[out]       pnIpAddress             currently configured IP address
* \param[out]       pnSubnetMask            currently configured subnet mask
* \param[out]       pnDefaultGateway        currently configured default gateway
* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*              <dt>::UNI_RESULT_CAMERA_BUSY</dt><dd>if the camera has been opened by an application on the local or any remote host</dd></dl>
* \note <ul><li>The parameter nConfigMode tells which method was used to set the IP address information. If the camera was configured to use DHCP 
          and fell back to Auto IP this parameter will still be set to DHCP.</li><li>The parameter nSupportedConfigMode is in general a combination of several individual modes (see ::E_UNI_IP_CONFIGURATION_MODE) 
          and tells which methods to set the IP address information are supported by the given camera.</li><li>The parameter nIpAddress, nSubnetMask, and nDefaultGateway tell the current IP configuration of the camera. 
          These values are given in network byte order (i.e. big endian) and were set either by DHCP, Auto IP, or from the 
          persistent information stored inside the camera.</li><li>Each out parameter may be NULL if the corresponding value is of no interest.</li></ul></member>
        <member name="M:UCC_GetCameraCapabilities(System.UInt32,System.UInt32*,System.UInt32*,System.UInt32*,System.UInt32*)">
 Tell fundamental camera capabilities.

 \param[in]    CamId   Camera identifier (serial number)
 \param[out]   pnMaxImageWidth     Pointer to an ::UINT32_TYPE variable receiving maximal possible image width
 \param[out]   pnMaxImageHeight    Pointer to an ::UINT32_TYPE variable receiving maximal possible image height
 \param[out]   pbColorInfo         Pointer to an ::UINT32_TYPE boolean indicating colour camera
 \param[out]   pnMaxImageDepth     Pointer to an ::UINT32_TYPE variable receiving the maximal possible bit depth
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_GetCameraInfoString(System.UInt32,System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte*,System.UInt32*)">
* Get textual information about the specified camera.
*
* \param[in]        CamId           camera identifier (serial number)
* \param[in]        nId             identifier to the camera info you'd like to receive (see ::E_UNI_CAMERA_INFO)
* \param[out]       pszInfoString   pointer to a zero terminated String of length (*pLength) max.
* \param[in,out]    pLength         <ul><li>[in]pointer to an integer holding the available space in pszInfoString</li><li>[out]pointer to the length of the string that was filled</li></ul>
* \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
*

</member>
        <member name="M:UCC_GetCameraInfo(System.UInt32,System.UInt32,System.UInt32*)">
 Get numerical information about the specified camera.

 \param[in]        CamId           camera identifier (serial number)
 \param[in]        nId             identifier to the camera info you'd like to receive (see ::E_UNI_CAMERA_INFO)
 \param[out]       pnValue         pointer to the parameter info value
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_CheckAvailable(System.UInt32,System.UInt32*)">
@name Camera information methods. 
A set of methods to get information about the camera.

 Check if a camera is currently available.

 \param[in]    CamId       camera identifier (serial number)
 \param[out]   pbResult    pointer to a "boolean" ::UINT32_TYPE value for receiving the availability state
 \return   <dl><dt>S_OK</dt><dd>if the node is available</dd></dl>
 \note a camera is available if it is opened by the API or not in busy state

</member>
        <member name="M:UCC_CloseCamera(System.UInt32)">
 Close a specific camera.

 \param[in]    CamId   camera identifier (serial number)
 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_OpenCameraEx(System.UInt32,System.Int32,System.Int32)">
 Open a camera with extended functionality.
 Updates the internal camera object to a physical camera object.
 \note causes a NodeListChange event.
 \note if ::E_FEAT_OPENREADONLY is activated when opening a camera,
   registers can be only read, and writing registers will return an error.

 \param[in]    CamId       camera identifier (serial number)
 \param[in]    CardNr      if Camera on more then one card choose this card
 \param[in]    lSpeedLimit maximum communication bit rate to be used ::E_UNI_DATABITRATE

 \return <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_OpenCamera(System.UInt32,System.Int32)">
@name Camera methods. 
A set of methods to deal with all actions
related to cameras in general.

 Open a Camera.
 Updates the internal camera object to a physical camera object.
 \note causes a NodeListChange event.
 \note if ::E_FEAT_OPENREADONLY is activated when opening a camera,
   registers can be only read, and writing registers will return an error.

 \param[in]    CamId       camera identifier (serial number)
 \param[in]    lSpeedLimit maximum communication bit rate to be used ::E_UNI_DATABITRATE

 \return <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl></member>
        <member name="M:UCC_UnRegisterNotification(System.UInt32)">
Unregister a single notification object registration.
\param[out]  Handle         handle to registration to be canceled
\return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
\note For examples see \ref codenotif "code snippets"

</member>
        <member name="M:UCC_RegisterNotification(System.UInt32*,E_UNI_NOTIFICATION_EVENTS,S_UNI_NOTIFICATION*)">
Register a notification object for a specified event type.
\param[out]  pHandle             returns a handle to the notification object registration 
\param[in]   NotificationEvent   the type of event the notification object should be registered for
\param[in]   pNotification       notification object to be registered
\return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
@note 
The notification object pointed to by \c pNotification must not be deleted as long as its registration persists.
Therefore notification object registrations should be canceled immediately when they aren't needed anymore (see ::UCC_UnRegisterNotification()).
Notification objects may be registered for a certain camera or for all available cameras(\ref UNI_ALL_CAMERAS).

Although this method can be used to register frame start events (see ::E_UNI_NOTIFICATION_FRAME_START) for GigE cameras, 
it does not work as expected, because GigE camera will not send frame start events.

\note For examples see \ref codenotif "code snippets"

</member>
        <member name="M:UCC_RegisterFrameDroppedNotification(System.UInt32*,S_UNI_NOTIFICATION*)">
Register a notification object for 'frame dropped' events.
\param[out]  pHandle         returns a handle to the notification object registration 
\param[in]   pNotification   notification object to be registered for 'frame dropped' events
\return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
@note 
The notification object pointed to by \c pNotification must not be deleted as long as its registration persists.
Therefore notification object registrations should be canceled immediately when they aren't needed anymore (see ::UCC_UnRegisterNotification ).
Notification objects may be registered for a certain camera or for all available cameras(\ref UNI_ALL_CAMERAS).

</member>
        <member name="M:UCC_RegisterFrameReadyNotification(System.UInt32*,S_UNI_NOTIFICATION*)">
Register a notification object for 'frame ready' events.
\param[out]  pHandle         returns a handle to the notification object registration 
\param[in]   pNotification   notification object to be registered for 'frame ready' events
\return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
@note 
The notification object pointed to by \c pNotification must not be deleted as long as its registration persists.
Therefore notification object registrations should be canceled immediately when they aren't needed anymore (see ::UCC_UnRegisterNotification()).
Notification objects may be registered for a certain camera or for all available cameras(\ref UNI_ALL_CAMERAS).

</member>
        <member name="M:UCC_RegisterFrameStartNotification(System.UInt32*,S_UNI_NOTIFICATION*)">
Register a notification object for 'frame start' events.
\param[out]  pHandle         returns a handle to the notification object registration 
\param[in]   pNotification   notification object to be registered for 'frame start' events
\return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
@note 
The notification object pointed to by \c pNotification must not be deleted as long as its registration persists.
Therefore notification object registrations should be canceled immediately when they aren't needed anymore (see ::UCC_UnRegisterNotification()).
Notification objects may be registered for a certain camera or for all available cameras(\ref UNI_ALL_CAMERAS).

This method is for FireWire only, GigE camera will not send frame start events.

</member>
        <member name="M:UCC_RegisterNodeListChangedNotification(System.UInt32*,S_UNI_NOTIFICATION*)">
@name Notification methods. 
A set of methods to deal with user notifications.

Register a notification object for 'node list changed' events (bus reset end).
@param[out]  pHandle         returns a handle to the notification object registration 
@param[in]   pNotification   notification object to be registered for 'bus reset end' events

@return      <dl><dt>S_OK</dt><dd>if successful</dd></dl>
@note 
The notification object pointed to by \c pNotification must not be deleted as long as its registration persists.
Therefore notification object registrations should be canceled immediately when they aren't needed anymore (see ::UCC_UnRegisterNotification()).
Notification objects may be registered for a certain camera or for all available cameras(\ref UNI_ALL_CAMERAS).
\note For examples see \ref codenotif "code snippets"

</member>
        <member name="M:UCC_GetBitsPerPixel(System.UInt32,System.UInt32*)">
 Get the number of pixels required for the transport of an image with the given color coding.

 \param[in]    ColorCode     The color coding see: ::E_UNI_COLOR_CODE
 \param[out]   BitsPerPixel  The number of bits (8,12,16,24 or 48)
 \return <dl><dt>S_OK</dt><dd>if a known color coding was submitted</dd></dl></member>
        <member name="M:UCC_WriteUserLog(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*)">
 Log a message.

 \param[in]   nLevel   logging level
 \param[in]   pszMsg   text message to be logged
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
 For general information about logging see \ref otherLog 

</member>
        <member name="M:UCC_CreateUserLog(System.UInt32,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte!System.Runtime.CompilerServices.IsConst*)">
* Create a user log file.
*
* \param[in]   nMaxLevel      The maximum logging level to log see: ::E_UNI_LOGGING
* \param[in]   pszFileName    The name of the file to create
* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>::UCC_Init was not called</dd></dl>
   For general information about logging see \ref otherLog 

</member>
        <member name="M:UCC_ReconnectAllCameras(System.UInt32)">
* Reconnect all cameras.
*
* \param[in]   bAutoReLoadSettings (a "boolean" ::UINT32_TYPE value)
* \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>if ::UCC_Init was not called</dd></dl></member>
        <member name="M:UCC_DisconnectAllCameras">
* Disconnect all cameras.
*
* \return<dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>::UCC_Init was not called</dd></dl></member>
        <member name="M:UCC_CloseAllCameras">
* Close all cameras.
*
* \return <dl><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>::UCC_Init was not called</dd><dt>::UNI_RESULT_CAMERA_NOT_FOUND</dt><dd>meaning that the camera with the given Id
   was not found on the bus, either bacause it has not been opened or because
   it is not accessible</dd></dl></member>
        <member name="M:UCC_GetCamerasEx(System.UInt32*,System.UInt32*,System.UInt32*)">
 Count connected cameras and return arrays of camera IDs and extra information, if desired.
 The given arrays are filled up to the number of entries specified by *pnSize except if NULL is given as array pointer.

 \param[in,out]    pnSize          <ul><li>in: array size</li><li>out: actual number of cameras filled into the array</li></ul>
 \param[out]       vecIds          Pointer to an Id-array of nSize elements.
 \param[out]       vecExtraInfo    1:1 mapping of additional info for identifying of the vecIds 
 \note   vecExtraInfo gives bus specific information to a given camera [CardID:8][NodeId:8][reserved:16]
 \return <dl><dt>S_OK</dt><dd>if the operation was successful,</dd><dt>::UNI_RESULT_PARAMETER_INVALID_1</dt><dd>if pnSize is NULL or *pnSize==0 or *pnSize&gt;64</dd><dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if not the full range inside the array vecIds was writable</dd><dt>::UNI_RESULT_MORE_DATA</dt><dd>if there was more data than space provided by the given array</dd><dt>a FireGrab error</dt><dd /></dl></member>
        <member name="M:UCC_GetCameras(System.UInt32*,System.UInt32*)">
 Count connected cameras and return an array of camera IDs, if desired.
 The given array is filled up to the number of entries specified by *pnSize except if NULL is given as array pointer.

 \param[in,out] pnSize         <ul><li>in: array size</li><li>out: actual number of cameras filled into the array</li></ul>
 \param[out]    vecIds         pointer to an Id-array of nSize elements
               
 \return <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_PARAMETER_INVALID_1</dt><dd>if pnSize is NULL or *pnSize==0 or *pnSize&gt;64</dd><dt>::UNI_RESULT_PARAMETER_INVALID_2</dt><dd>if not the full range inside the array vecIds was writable</dd><dt>::UNI_RESULT_MORE_DATA</dt><dd>if there was more data than space provided by the given array</dd><dt>a FireGrab error</dt><dd /></dl></member>
        <member name="M:UCC_GetErrorInfo(System.Int32!System.Runtime.CompilerServices.IsLong,System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte*,System.UInt32)">
 Get error information.
 The error code you supply is evaluated and the given string is
  filled with an error description.

 \param[in]   nErrCode        error code to be described
 \param[out]  pszInfoString   the error code description
 \param[in]   nMaxStrLen      length of the provided string buffer
 \return  <dl><dt>S_OK</dt><dd>if the operation was successful</dd></dl>
 \note The following code shows an example of a UniAPI function call with subsequent
 error code evaluation:
 \code
 UNI_RETURN_TYPE ret = UCC_Init();
 if FAILED( ret )
 {
      char text[256];
      UCC_GetErrorInfo ( ret, text, 256 );
      printf( "UCC_Init() returned error code 0x%X (%s)", (unsigned long)ret, text );
 }
 \endcode

</member>
        <member name="M:UCC_GetLicenseType(System.UInt32,System.UInt32*)">
get active FireGrab license information for the camera.
\param[in]  CamId                id of the camera to query the information for
\param[out] pnUniLicenseType     the license type for the camera \see ::E_UNI_LICENSE_TYPE

</member>
        <member name="M:UCC_GetLicenseType_Global(System.UInt32*)">
get the UniAPI global FireGrab license information.
\param[out] pnUniLicenseType  active license type \see ::E_UNI_LICENSE_TYPE

</member>
        <member name="M:UCC_GetModuleInfo(System.UInt32,System.UInt32*)">
Get information about a module of the API.
* Used for returning the version numbers of the sub-modules of the UniAPI.
* The format for versions is 0x01020304 for version 1.2.3.4
*
* \param[in]   nModule  value indicating the index of the module of the UniAPI information is wanted for
                        (see ::E_UNI_MODULE_INFO)
* \param[out]  pnValue  pointer to ::UINT32_TYPE, which returns the desired information number
* \return      <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*              <dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>if this module was not initialized before this call</dd></dl></member>
        <member name="M:UCC_GetVersion(System.UInt32*)">
Get the version number of the API.
* The format is 0x01020304 for version 1.2.3.4
*
* \param[out]  pnValue  pointer to ::UINT32_TYPE, which returns the library version (Major,Minor,SubMinor,Build)
* \return      <dl><dt>S_OK</dt><dd>if the operation was successful</dd>
*              <dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>if this module was not initialized before this call</dd></dl></member>
        <member name="M:UCC_Release">
 Deinitialization of the API.
 All actions needed to clean up the API is done.
  This operation must be executed after the usage of this API has ended.

 \return   <dl><dt>S_OK</dt><dd>if the operation was successful</dd><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>if this module was not initialized before this call</dd></dl></member>
        <member name="M:UCC_Init">
@name Non-camera methods. 
A set of methods to deal with all actions not directly related to cameras.

* Initialization of the API.
* This operation must be executed before any other call in this API. To end your usage of this API,
*  use ::UCC_Release
* 
* \return   <dl>    
*           <dt>S_OK</dt><dd>if the Initialization was successful</dd><dt>::UNI_RESULT_API_NOT_INITIALIZED</dt><dd>if this module could not be initialized</dd></dl></member>
        <member name="T:E_UNI_NOTIFICATION_EVENTS">
Events you can register a notification for.
</member>
        <member name="T:S_UNI_NOTIFICATION">
UniAPI notification structure.
</member>
        <member name="M:S_UNI_WINDOWS_MESSAGE.#ctor(HWND__*,System.UInt32,System.UInt32,System.Int32!System.Runtime.CompilerServices.IsLong)">
constructor with args.
    \param[in]  hWnd        Handle to window
    \param[in]  Msg         Message to be send to window when triggered
    \param[in]  wParam      Reserved
    \param[in]  lParam      Camera id

</member>
        <member name="M:S_UNI_WINDOWS_MESSAGE.#ctor">
default constructor if c++
</member>
        <member name="T:S_UNI_WINDOWS_MESSAGE">
Windows message to send on notification.
    lParam will hold the sending camera id

</member>
        <member name="M:S_UNI_NOTIFICATION_EVENT.#ctor(System.Void**,System.UInt32)">
 constructor with variable init if c++.
\param[in] Event  windows event ::HANDLE
\param[in] CamId    Camera ID for which this notification is triggered

</member>
        <member name="M:S_UNI_NOTIFICATION_EVENT.#ctor">
default constructor if c++
</member>
        <member name="T:S_UNI_NOTIFICATION_EVENT">
Win API event notification.
</member>
        <member name="M:S_UNI_NOTIFICATION_CALLBACK.#ctor(=FUNC:System.Void(S_UNI_CALLBACK_ARGUMENT),S_UNI_CALLBACK_ARGUMENT)">
constructor with init.
\param[in] callback callback function for the notification
\param[in] arg      callback argument

</member>
        <member name="M:S_UNI_NOTIFICATION_CALLBACK.#ctor">
default constructor if c++.

</member>
        <member name="T:S_UNI_NOTIFICATION_CALLBACK">
Notification Callback struct.
Will hold the callback function and the function argument

</member>
        <member name="M:S_UNI_CALLBACK_ARGUMENT.#ctor(System.Void*,System.UInt32)">
constructor with init if c++.
\param[in] Prm    user defined parameter which is passed every time the callback is called
\param[in] CamId  Canera ID for which the notification is registered default: ::UNI_ALL_CAMERAS

</member>
        <member name="M:S_UNI_CALLBACK_ARGUMENT.#ctor">
default constructor if c++.

</member>
        <member name="T:S_UNI_CALLBACK_ARGUMENT">
Callback function argument.
</member>
        <member name="T:E_UNI_IP_CONFIGURATION_MODE">
IP configuration modes.
*
* \note This enumeration is for GigE only.

</member>
        <member name="T:E_UNI_SETTINGS_SELECT_MODE">
Mode for selecting the camera entry from a settings file.

</member>
        <member name="T:E_UNI_LICENSE_TYPE">
License modes of Firegrab.

</member>
        <member name="T:E_TRIGGER_MODES">
Trigger modes.

</member>
        <member name="T:E_OUTPUTPIN_MODES">
Output Pin Modes.

</member>
        <member name="T:E_INPUTPIN_MODES">
Input Pin Modes.

</member>
        <member name="T:S_IIDC_VIDEOINFO_FORMAT">
IIDC video formats.

</member>
        <member name="T:S_IIDC_VIDEOINFO_MODE">
IIDC video modes.

</member>
        <member name="T:S_IIDC_VIDEOINFO_FPS">
Frame rates for IIDC video modes.

</member>
        <member name="T:S_UNI_TRANSPORT_FORMAT_INFO">
Structure providing an exact description of the image transfer format. 
* This structure is used for unified processing of 16-bit mono/raw images from Firewire and GigE-Vision cameras.

</member>
        <member name="T:E_UNI_SIMPLE_IMAGE_FORMAT">
Simple image type enum. 
*   Enumeration for simple image types with one image plane consisting of consecutive pixel (starting at the top of the image).

</member>
        <member name="T:S_YUV444">
Structure for accessing data in the YUV 4:4:4 format (YUV)
    prosilica component order

</member>
        <member name="T:S_BGRA8">
Structure for accessing Windows RGBA data.
</member>
        <member name="T:S_RGBA8">
Structure for accessing non-Windows RGB data 
</member>
        <member name="T:S_BGR8">
Structure for accessing Windows RGB data 
</member>
        <member name="T:S_RGB8">
Structure for accessing Windows RGB data 
</member>
        <member name="T:E_UNI_IMAGE_FORMAT">
Enumeration for output image formats supported by ::UCC_GetImage, ::UCC_GetImageEx, ::UCC_GrabImage and ::UCC_GrabImageEx.
* \note Image formats ::E_IF_MONO8 and ::E_IF_BGR have been removed due to accuracy issues. Instead, ::E_IF_MONO_8BPP
* and ::E_IF_BGR_24BPP should be used. Unlike their outdated counterparts, ::E_IF_MONO_8BPP and ::E_IF_BGR_24BPP
* consistently result in the expected image format. As a consequence, existing applications may require additional
* changes when the new formats are adopted. Alternatively, the macro USE_LEGACY_IMAGE_FORMATS can be defined for 
* existing projects. This activates compatibility definitions, allowing recompilation of existing applications 
* without any changes.

</member>
        <member name="T:S_SIS_DATA">
SIS data structure
</member>
        <member name="T:S_SIS_ENABLE.S_SIS_ENABLE_FIELDS">
states of the SIS data
</member>
        <member name="T:S_SIS_ENABLE">
information about state of the fields of the ::S_SIS_DATA struct
</member>
        <member name="T:E_UNI_SIS_TYPE">
enum for the secure image signatures
</member>
        <member name="T:S_TECHNO_INFO">
states of the support for different multimedia technologies
</member>
        <member name="T:S_SUPPORT_STATE">
states of the multi media technology support for operating system and processor.

</member>
        <member name="T:E_UNI_COLOR_CODE">
Enumeration for color codes / transfer formats.
These codes have their origin in standard IIDC color codes and AVT-specific color codes
used in format 7, but they are used for cameras on both interfaces.

</member>
        <member name="T:E_UNI_ROPMODE">
\endcond
Enumeration for raster operation (used by re-sampling methods).
</member>
        <member name="D:__1394GUID">
\cond UNI_CONTROL 
</member>
        <member name="D:HRESULT">
@name Special types.
Types for special purposes.

</member>
        <member name="D:UNICODE_CHAR">
@name Character types.
Types for character handling.

</member>
        <member name="D:INT8_TYPE">
@name Portable types.
Use these types.

</member>
        <member name="D:int16_t">
\file UniControl.Net.cpp
* \brief This file describes all the available AVT Universal API .Net Camera Control methods.
*
* \version 1.0
*
* \date 09-11-2007
*
* \author Philipp Beyer, Holger Eddelbuettel, Matthias Heidenreich, Olaf Reuter
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file UniControl.h
* \brief This file describes all the available AVT Universal API Camera Control methods.
*
* All the AVT Universal API methods for controlling cameras that are publicly available are described
*  in this part of the help. For a description see the section "\link getstart Getting started
*  \endlink " of this help.
*
* \version 2.0
*
* \date 29-Sep-2009
*
* \author Philipp Beyer, Holger Eddelbuettel, Matthias Heidenreich, Olaf Reuter
*
* \par license
* This code is the property of Allied Vision Technologies. Nevertheless, you
* are absolutely free to use and modify it. The code is provided "as is" with
* no expressed or implied warranty. The author and AVT accept no liability if
* it causes any damage to your computer.
*

\file uni_defines.h
 * \brief Definitions for the Universal API methods.
 *
 * This file describes all necessary definitions for using the
 *  Universal API methods.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

\file UNI_types.h
 * \brief Definitions for the types used in the Universal API methods.
 *
 * This file describes all necessary type definitions for using
 * methods of AVT's Universal API. These type definitions are designed to be
 * portable in the sense of Microsoft, means: also usable in VB and C#.
 *
 * \par license
 * This code is the property of Allied Vision Technologies. Nevertheless, you
 * are absolutely free to use and modify it. The code is provided "as is" with
 * no expressed or implied warranty. The author and AVT accept no liability if
 * it causes any damage to your computer.
 *

@name Basic types used to define the types really used
Don't use these typedefs directly!

</member>
        <!-- Discarding badly formed XML document comment for member 'T:S_UNI_IMAGE'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UCC_WaitForBusReset(System.UInt32*,System.UInt32*,System.UInt32,System.UInt32*)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UCC_PrepareFixedGrab(System.UInt32,System.UInt32,System.UInt32,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UCC_Whitebalance_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UCC_Whitebalance_GMCY_Set(System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32,System.UInt32)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:UCC_UserProfile_GetErrorStatus(System.UInt32,System.UInt32*)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:AlliedVisionTech.UniControlNet.UniCamera.SetAOIPosition(System.UInt32@,System.UInt32@)'. -->
        <!-- Discarding badly formed XML document comment for member 'M:AlliedVisionTech.UniControlNet.UniControl.GetIPConfiguration(System.UInt32)'. -->
    </members>
</doc>