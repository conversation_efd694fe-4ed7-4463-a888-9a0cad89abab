# Syncthing 忽略模式规则详细文档

## 概述

Syncthing 使用 `.stignore` 文件来定义哪些文件和文件夹应该被排除在同步之外。这个文件使用类似于 `.gitignore` 的语法，但有一些独特的特性。

## 基本概念

### .stignore 文件位置
- 必须放置在共享文件夹的根目录中
- 文件名必须是 `.stignore`（注意前面的点）
- 每个共享文件夹可以有自己独立的 `.stignore` 文件
- 如果文件不存在，Syncthing 不会忽略任何文件

### 文件编码
- 使用 UTF-8 编码
- 支持 Unix (LF) 和 Windows (CRLF) 行结束符

## 语法规则

### 1. 基本通配符

#### 星号 (*)
- 匹配任意数量的字符，但不包括路径分隔符
```
*.txt          # 匹配所有 .txt 文件
temp*          # 匹配以 temp 开头的文件/文件夹
*cache*        # 匹配包含 cache 的文件/文件夹
```

#### 双星号 (**)
- 匹配任意数量的字符，包括路径分隔符
- 可以跨越多个目录层级
```
**/*.log       # 匹配所有子目录中的 .log 文件
**/node_modules/  # 匹配任意深度的 node_modules 目录
cache/**       # 匹配 cache 目录及其所有内容
```

#### 问号 (?)
- 匹配单个字符
```
file?.txt      # 匹配 file1.txt, fileA.txt 等
test???.log    # 匹配 test123.log, testabc.log 等
```

#### 方括号 ([])
- 匹配方括号内的任一字符
```
file[123].txt  # 匹配 file1.txt, file2.txt, file3.txt
[abc]*.log     # 匹配以 a, b, 或 c 开头的 .log 文件
[0-9]*.dat     # 匹配以数字开头的 .dat 文件
```

### 2. 路径规则

#### 绝对路径 vs 相对路径
```
# 相对路径（推荐）
temp/          # 匹配根目录下的 temp 文件夹
docs/draft.txt # 匹配 docs 目录下的 draft.txt

# 以斜杠开头表示从根目录开始
/temp/         # 只匹配根目录下的 temp 文件夹
/docs/draft.txt # 只匹配根目录 docs 下的 draft.txt
```

#### 目录匹配
```
# 匹配目录（以斜杠结尾）
build/         # 匹配名为 build 的目录
**/cache/      # 匹配任意深度的 cache 目录

# 匹配目录及其内容
node_modules   # 匹配 node_modules 文件或目录
node_modules/  # 只匹配 node_modules 目录
```

### 3. 特殊字符

#### 注释 (#)
```
# 这是注释行
*.tmp          # 行末注释
# TODO: 添加更多规则
```

#### 例外规则 (!)
- 用于排除某些文件不被忽略
```
*.log          # 忽略所有 .log 文件
!important.log # 但不忽略 important.log
```

#### 转义字符 (\)
```
\#file.txt     # 匹配名为 #file.txt 的文件
\!important    # 匹配名为 !important 的文件
file\*.txt     # 匹配名为 file*.txt 的文件
```

### 4. 正则表达式模式

以 `(?` 开头的行被视为正则表达式：

```
(?i)desktop\.ini           # 忽略大小写匹配 desktop.ini
(?i)thumbs\.db            # 忽略大小写匹配 thumbs.db
(?i).*\.tmp$              # 忽略大小写匹配所有 .tmp 文件
(?m)^\..*                 # 匹配以点开头的文件（隐藏文件）
```

#### 正则表达式标志
- `(?i)` - 忽略大小写
- `(?m)` - 多行模式
- `(?s)` - 单行模式（. 匹配换行符）

## 常用忽略模式示例

### 系统文件
```
# Windows 系统文件
Thumbs.db
Desktop.ini
$RECYCLE.BIN/
System Volume Information/

# macOS 系统文件
.DS_Store
.AppleDouble
.LSOverride
.Spotlight-V100
.Trashes

# Linux 系统文件
.directory
*.swp
*.swo
*~
```

### 开发环境
```
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/

# Java
*.class
*.jar
*.war
*.ear
target/
.gradle/
build/

# .NET
bin/
obj/
*.user
*.suo
*.cache
```

### 临时文件和缓存
```
# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old
*~

# 缓存目录
cache/
**/cache/
.cache/
temp/
tmp/

# 日志文件
*.log
logs/
**/logs/
```

### 媒体和大文件
```
# 视频文件
*.mp4
*.avi
*.mkv
*.mov

# 音频文件
*.mp3
*.wav
*.flac

# 图像文件（如果不需要同步）
*.psd
*.ai
*.eps

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz
```

## 高级用法

### 条件忽略
```
# 只在特定目录下忽略
src/**/*.test.js    # 只忽略 src 目录下的测试文件
docs/**/*.draft     # 只忽略 docs 目录下的草稿文件
```

### 复杂模式组合
```
# 忽略所有 .txt 文件，但保留重要的
*.txt
!README.txt
!LICENSE.txt
!important*.txt

# 忽略构建产物，但保留配置
build/
!build/config/
!build/*.json
```

### 大小写敏感处理
```
# 在大小写敏感的系统上
(?i)readme.*        # 匹配 README, readme, ReadMe 等
(?i)\.git           # 匹配 .git, .GIT, .Git 等
```

## 最佳实践

### 1. 文件组织
```
# 按类型分组
# ============= 系统文件 =============
.DS_Store
Thumbs.db

# ============= 开发文件 =============
node_modules/
*.log

# ============= 临时文件 =============
*.tmp
cache/
```

### 2. 性能考虑
- 将最常见的模式放在文件开头
- 使用具体的模式而不是过于宽泛的通配符
- 避免过度使用正则表达式

### 3. 维护性
- 添加注释说明规则用途
- 定期审查和清理不需要的规则
- 使用版本控制跟踪 `.stignore` 文件的变化

## 调试和测试

### 检查忽略状态
在 Syncthing Web UI 中：
1. 进入文件夹详情页面
2. 查看"忽略的文件"部分
3. 检查特定文件是否被正确忽略

### 常见问题
1. **规则不生效**：检查文件编码和路径分隔符
2. **意外忽略**：检查通配符是否过于宽泛
3. **例外规则无效**：确保例外规则在忽略规则之后

### 测试工具
可以使用命令行工具测试模式：
```bash
# 使用 syncthing 命令行工具测试
syncthing cli debug file <folder-id> <file-path>
```

## 注意事项

1. **已同步文件**：`.stignore` 规则不会影响已经同步的文件
2. **删除处理**：如果要停止同步已同步的文件，需要先删除它们
3. **平台差异**：在不同操作系统间同步时注意大小写敏感性
4. **性能影响**：过多或复杂的规则可能影响同步性能
5. **安全考虑**：不要依赖 `.stignore` 来保护敏感文件，它只是同步控制工具

## 参考资源

- [Syncthing 官方文档](https://docs.syncthing.net/)
- [忽略模式语法参考](https://docs.syncthing.net/users/ignoring.html)
- [正则表达式语法](https://golang.org/pkg/regexp/syntax/)
