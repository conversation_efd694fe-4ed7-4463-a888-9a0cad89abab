<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.Manage</name>
    </assembly>
    <members>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.IsPeriodCheckInitSuccess">
            <summary>
            是否初始化期间核查成功
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.LogReadWrite">
             <summary>
            Log
             </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.IsAcvMonitor">
            <summary>
            是否为监视耐压
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.IsOK">
            <summary>
            CCD是否默认合格
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.CurrentWorkItem">
            <summary>
            当前工作试验项目
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.SystemRunMode">
            <summary>
            软件运行模式
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.CameraId">
            <summary>
            相机位置
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._communication">
            <summary>
            通信接口
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._BusinessControlManage">
            <summary>
            业务管理对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._xmlInfoAssembly">
            <summary>
            配置文件和反射对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._systemXmlInfo">
            <summary>
            系统配制文件对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._interfaceOperate">
            <summary>
            界面操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage._sleepTime">
            <summary>
            数据完整性检验等待时间
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.CCDTime">
            <summary>
            屏显延时时间
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.BusinessManage.CCDTimeP">
            <summary>
            屏显延时时间
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.StReslultDataXmlId">
            <summary>
            芯片ID认证返回数据缓存
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.CellInit">
            <summary>
            界面开启时和开始工作时台体的初始化动作
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.BusinessManage.SerialMumberEventHandler">
            <summary>
            序列号检测委托事件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.BusinessManage.CustomerName">
            <summary>
            现场名称：中文首字母，例如： QY（清远）、GX（广西）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.Instance(CLDC.Framework.DataModel.Enum.EmLinkMode,CLDC.CLAT.CLWBS.ILog.ILog)">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.#ctor(CLDC.Framework.DataModel.Enum.EmLinkMode,CLDC.CLAT.CLWBS.ILog.ILog)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SetEncryptionCodes">
            <summary>
            标准表系列号处理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.TriggerEventStartTrial">
            <summary>
            各通信接口表信息和方案信息收集，实验开始事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.TriggerEventInsectCard">
            <summary>
            插卡事件，业务需继续
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.TriggerEverntCCD(System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.SculptureInfo})">
            <summary>
            CCD接到就绪后动作
            </summary>
            <param name="readIndex">1为第一次，2为第二次，3为第三次</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.InitSystem">
            <summary>
            初始化系统
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ReadMeterInfo">
            <summary>
            表位数据采集
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ReadHumAndTemp">
            <summary>
            读取温湿度
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ThreadSelfCheck">
            <summary>
            开机自检
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ThreadTempRatureRead">
            <summary>
            读温度线程
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ThreadSendUnSuccessTrialData">
            <summary>
            发送当前数据库中没有更新成功的检定数据
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.LoadMeterInfoAndScheme">
            <summary>
            从数据库中加载方案和表信息
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SaveSchemasAndMeterInfo(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            保存表信息和方案信息
            </summary>
            <param name="LstSchema"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.ResetMeterInfo(System.Collections.Generic.List{System.Int32})">
            <summary>
            重置表信息
            </summary>
            <param name="lstCheckMeter">选中表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.GetCheckMeterAddrss">
            <summary>
            获取表地址信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartSchemes">
            <summary>
            开始全部方案
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartSchemes(System.Int32,System.Boolean)">
            <summary>
            开始指定方案
            </summary>
            <param name="ID"></param>
            <param name="bluetoothInit">是否初始化蓝牙</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.FlipAcc_ErrorScheme">
            <summary>
            检定方案中的误差试验项倒叙
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.GetTrialDataBySourceParam(System.String)">
            <summary>
            获取误差试验数据
            </summary>
            <param name="sqlSourceParam">控源参数</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.CheckAddress(CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            单步或连续检测时，检查通讯地址是否存在，存在就不用做485；
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartOneSchemes(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local,System.Boolean)">
            <summary>
            开始一个项目
            </summary>
            <param name="TrialScheme">方案</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartScheme(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local,System.Collections.Generic.List{System.Int32}@,System.Boolean,System.Int32)">
            <summary>
            开始单项方案
            </summary>
            <param name="TrialScheme"></param>
            <param name="fialMeterId">当前实验项目的不合格表表位号集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.PeriodCheck_3LBusiness(CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local,CLDC.CLAT.CLWBS.Business.BusinessBase)">
            <summary>
            3L期间核查业务
            </summary>
            <param name="currnetTrialScheme"></param>
            <param name="currnetBusinessBase"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartThreadRead(System.Threading.ThreadStart)">
            <summary>
            开始线程读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SendAlarmMsgToCenterControl(System.Int32,System.String,System.String,System.String)">
            <summary>
            向主控发送警告消息
            </summary>
            <param name="strMsg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.AddTrialData">
            <summary>
            加载试验数据到界面显示
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SendTrialDataToCenter">
            <summary>
            上传数据库中所有试验结果
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.GetMeterInfoFromCenter(System.String,System.Int32)">
            <summary>
            手工申请表信息
            </summary>
            <param name="BarCode">表条码</param>
            <param name="MeterID">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SystemEvent_EquipMentResultEvent(CLDC.CLAT.CLWBS.DataModel.Struct.StResultData)">
            <summary>
            接收设备返回数据方法
            </summary>
            <param name="st"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SystemEvent_TempratureResultEvent(CLDC.CLAT.CLWBS.DataModel.Struct.StResultData)">
            <summary>
            接收设备返回数据方法
            </summary>
            <param name="st"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartTempratureEvent">
            <summary>
            开始启动注册接收溫度T事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StopTempratureEvent">
            <summary>
            取消注册的事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.GetMeterInfo">
            <summary>
            获取表位状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartEvent">
            <summary>
            开始启动注册事件
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.Finish">
            <summary>
            告诉主控完成
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.CheckMeterUpdateSuccess">
            <summary>
            检查全部数据全部上传成功
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.PowerOn(System.Int32,System.Int32)">
            <summary>
            升源
            </summary>       
            <param name="type">0，更新电压，1更新电流，2都更新</param>
            <param name="valueorph">更新类型 0：幅值，1：相位，2：都更新</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.BluetoothInit(CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            蓝牙初始化
            </summary>
            <param name="clMeterInfo"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SaveNowTrialData">
            <summary>
            保存当前已经做过的项目的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.UploadTrialData(CLDC.Framework.DataModel.Enum.EmTrialType,System.Int32,System.String@)">
            <summary>
            上次指定试验项目数据
            </summary>
            <param name="ItemId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.UpFailData">
            <summary>
            上传没有没有上传的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.UpPlanBModeData">
            <summary>
            上传B模式试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.LoadMeterInfoAndScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}@)">
            <summary>
            从数据库中加载方案和表信息（不发送到界面）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SendFinish">
            <summary>
            发送完成放行托盘
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.StartIBSThread">
            <summary>
            开启工位程序
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.BusinessManage.SendRunProcess(System.String,System.String)">
            <summary>
            发送检测进度
            </summary>
            <param name="currentItemName">当前实验项目名称</param>
            <param name="processValue">当前进度</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.EquipmentCategeManage.GetSystemPath(System.Int32)">
            <summary>
            查询路径
            </summary>
            <param name="type">0：按照表信息查询，1单相表，3：三相表</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan">
            <summary>
            新单相台体设备初始化过程
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.LogReadWrite">
            <summary>
            日志操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.EquipMentControl">
            <summary>
            界面操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.XmlInfo">
            <summary>
            配置文件读取
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.InitEquipInOpenForms(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{System.Int32})">
            <summary>
            窗体开启时初始化硬件
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="meterInfo">表信息</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.InitStartWork(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.Framework.DataModel.Enum.EmLinkType,System.Collections.Generic.List{System.Int32})">
            <summary>
            开始工作前设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="linkType">接线方式</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.InitEndWork(CLDC.Framework.DataModel.Enum.EmLightColor,System.Collections.Generic.List{System.Int32})">
            <summary>
            工作结束后设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.StartOtherThread">
            <summary>
            开启中间程序
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.SendAlarmMsgToCenterControl(System.Int32,System.String,System.String,System.String)">
            <summary>
            向主控发送警告消息
            </summary>
            <param name="strMsg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.UpFailData">
            <summary>
            上传没有成果上传的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.LoadMeterInfoAndScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}@)">
            <summary>
            从数据库中加载方案和表信息（不发送到界面）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.ThreadStdMeterRead">
            <summary>
            开始读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.StopThreadStdMeterRead">
            <summary>
            结束读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhaseJiNan.StartThreadRead(System.Threading.ThreadStart)">
            <summary>
            开始线程读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.IEquipMentInit.InitEquipInOpenForms(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{System.Int32})">
            <summary>
            窗体开始前设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="meterInfo">表信息</param>
            <param name="lstMeterId">表位号</param>
            <returns>true:成功，false:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.IEquipMentInit.InitStartWork(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.Framework.DataModel.Enum.EmLinkType,System.Collections.Generic.List{System.Int32})">
            <summary>
            开始工作前设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="linkType">接线方式</param>
            <param name="lstMeterId">表位号</param>
            <returns>true:成功，false:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.IEquipMentInit.InitEndWork(CLDC.Framework.DataModel.Enum.EmLightColor,System.Collections.Generic.List{System.Int32})">
            <summary>
            工作结束后设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="lstMeterId">表位号</param>
            <returns>true:成功，false:失败</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.IEquipMentInit.StopThreadStdMeterRead">
            <summary>
            结束读标准表
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase">
            <summary>
            新单相台体设备初始化过程
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.LogReadWrite">
            <summary>
            日志操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.EquipMentControl">
            <summary>
            界面操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.XmlInfo">
            <summary>
            配置文件读取
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.InitEquipInOpenForms(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{System.Int32})">
            <summary>
            窗体开启时初始化硬件
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="meterInfo">表信息</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.InitStartWork(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.Framework.DataModel.Enum.EmLinkType,System.Collections.Generic.List{System.Int32})">
            <summary>
            开始工作前设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="linkType">接线方式</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.InitEndWork(CLDC.Framework.DataModel.Enum.EmLightColor,System.Collections.Generic.List{System.Int32})">
            <summary>
            工作结束后设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.StartOtherThread">
            <summary>
            开启中间程序
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.SendAlarmMsgToCenterControl(System.Int32,System.String,System.String,System.String)">
            <summary>
            向主控发送警告消息
            </summary>
            <param name="strMsg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.UpFailData">
            <summary>
            上传没有成果上传的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.LoadMeterInfoAndScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}@)">
            <summary>
            从数据库中加载方案和表信息（不发送到界面）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.ThreadStdMeterRead">
            <summary>
            开始读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.StopThreadStdMeterRead">
            <summary>
            结束读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentSinglePhase.StartThreadRead(System.Threading.ThreadStart)">
            <summary>
            开始线程读标准表
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase">
            <summary>
            新三相台体初始化过程
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.LogReadWrite">
            <summary>
            日志操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.EquipMentControl">
            <summary>
            界面操作对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.XmlInfo">
            <summary>
            配置文件读取
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.#ctor(System.Int32)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.InitEquipInOpenForms(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local,System.Collections.Generic.List{System.Int32})">
            <summary>
            窗体开启时初始化硬件
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="meterInfo">表信息</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.InitStartWork(CLDC.Framework.DataModel.Enum.EmLightColor,CLDC.Framework.DataModel.Enum.EmLinkType,System.Collections.Generic.List{System.Int32})">
            <summary>
            开始工作前设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="linkType">接线方式</param>
            <param name="lstMeterId">表位号集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.InitEndWork(CLDC.Framework.DataModel.Enum.EmLightColor,System.Collections.Generic.List{System.Int32})">
            <summary>
            工作结束后设备初始化
            </summary>
            <param name="lightColor">灯颜色</param>
            <param name="lstMeterId">表位号集合</param> 
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.SendAlarmMsgToCenterControl(System.Int32,System.String,System.String,System.String)">
            <summary>
            向主控发送警告消息
            </summary>
            <param name="strMsg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.StartOtherThread">
            <summary>
            开启中间程序
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.SendMsgToCenterControl(System.Int32,System.String,System.String,System.String)">
            <summary>
            向主控发送警告消息
            </summary>
            <param name="strMsg"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.CheckMeterUpdateSuccess(System.String)">
            <summary>
            检查数据是否上传成功
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.UpFailData">
            <summary>
            上传没有成果上传的试验数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.LoadMeterInfoAndScheme(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Struct.StTrialScheme_Local}@,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local}@)">
            <summary>
            从数据库中加载方案和表信息（不发送到界面）
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.ThreadStdMeterRead">
            <summary>
            开始读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.StopThreadStdMeterRead">
            <summary>
            结束读标准表
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InitEquipMents.InitEquipMentThreePhase.StartThreadRead(System.Threading.ThreadStart)">
            <summary>
            开始线程读标准表
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.EnumLicense.CLoseSoftWare">
            <summary>
            关闭软件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.EnumLicense.Alarm">
            <summary>
            告警
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.EnumLicense.NormalRun">
            <summary>
            正常运行
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.localXmlInfoAssembly">
            <summary>
            控制设备对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InterfaceOperate._systemXmlInfo">
            <summary>
            系统配制文件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.InterfaceOperate._InterfaceOperate">
            <summary>
            界面操作类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetInstance(CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly)">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.#ctor(CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.InsultMeterID(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            要隔离的表位
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.CheckMeterID(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            要恢复隔离的表位
            </summary>
            <param name="MeterInfo"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetMagneticHoldingRelayPins(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelMhrPortPin})">
            <summary>
            设置脉冲切换板磁保持继电器引脚
            </summary>
            <param name="clServicePanelMhrPortPins"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.Light(CLDC.Framework.DataModel.Enum.EmLightColor)">
            <summary>
            灯
            </summary>
            <param name="color"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.InteractiveRadioAutoSetAddr(System.Int32[])">
            <summary>
            交互广播自动设置地址
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.UpgradeEnabled(System.Int32[])">
            <summary>
            升级使能
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SoftwareReset(System.Int32[])">
            <summary>
            软件重启
            </summary>
            <param name="meterIDs">表位数组</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.WriteUpgradeData(System.Int32[],System.Byte[],System.Int32)">
            <summary>
            写升级数据
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="writeData">写入数据</param>
            <param name="dataIndex">写入序号</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetPortParams(System.Int32[],System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            设置串口参数
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="dicPortParam">串口参数字典[key-串口编号 value-波特率,例：2400,e,8,1]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPort})">
            <summary>
            设置同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPorts">端口数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetNoSamePortPin(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin})">
            <summary>
            设置不同端口引脚
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚数据列表</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetPinPulseCapture(System.Int32[],System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},System.Int32)">
            <summary>
            设置引脚脉冲捕获
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="clServicePanelPortPins">端口引脚信息</param>
            <param name="actionType">捕获动作0.停止捕获 1.开始捕获</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ReadPinPulseCapture(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            查询不同端口引脚脉冲捕获状态
            </summary>
            <param name="MeterIds"></param>
            <param name="clServicePanelPortPins"></param>
            <param name="DicClServicePanelPortPin"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ReadNoSamePortPinState(System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClServicePanelPortPin},CLDC.Framework.DataModel.Enum.EmTrialType)">
            <summary>
            查询不同端口引脚状态
            </summary>
            <param name="MeterIds"></param>
            <param name="clServicePanelPortPins"></param>
            <param name="DicClServicePanelPortPin"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetCurrentVersions(System.Int32)">
            <summary>
            查询当前软件本号
            </summary>
            <param name="meterId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SendTakingDownDeviceData(System.Int32,System.Byte[],System.Int32,System.Byte[]@)">
            <summary>
            下发从站设备数据
            </summary>
            <param name="meterId">表位号</param>
            <param name="data">下发数据</param>
            <param name="takingDownDeviceId">下挂设备Id</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetPulseChannel(System.Int32[],CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmPulseChannel)">
            <summary>
            设置误差板脉冲通道
            </summary>
            <param name="chkType">检定类型[0-电能误差 1-走字计数 2-预付费功能检定 3-对标 4-日计时误差 5-需量误差]</param>
            <param name="countType">计数类型[0-脉冲计数 1-脉冲间隔时间]</param>m>
            <param name="channel">通道号</param>
            <param name="divisionFactor">分频系数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetErrPanelParam(System.Int32[],System.UInt32,System.UInt32,System.UInt32,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            设置误差检定参数
            </summary>
            <param name="sPlsConst">标准表脉冲常数(单位:imp/kw.h)</param>
            <param name="sPlsFreq">标准表脉冲频率(单位:HZ)</param>
            <param name="mPlsConst">被检表脉冲常数(单位:imp/kw.h)</param>
            <param name="pulseCount">校验圈数(10)</param>
            <param name="channel">通道号</param>
            <returns>[true-成功,false-失败]</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetErrorCurrent(System.Int32[],CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            查询当前误差
            </summary>
            <param name="meterIds"></param>
            <param name="chkType">检定类型[0-电能误差 1-需量误差 2-日计时误差 3-脉冲计数 4-对标 5-预付费功能检定 06-耐压实验 07-多功能脉冲计数试验</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.AbortAllFunction(System.Int32[],System.Byte,CLDC.Framework.DataModel.Enum.EmCheckType,System.Int32)">
            <summary>
            停止或者开始误差板相应功能
            </summary>
            <param name="meterIDs">表位数组</param>
            <param name="actTag">动作类型[0-开始计算 1-停止计算]</param>
            <param name="chkType">检定类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.InsultMeter(System.Int32[],System.Int32)">
            <summary>
            隔离恢复电压电流
            </summary>
            <param name="MeterId"></param>
            <param name="Type"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.InsulateFault(System.Int32[],System.Int32)">
            <summary>
            隔离表位电流电压
            </summary>
            <param name="meterId">表位号数组</param>
            <param name="ctlType">控制类型[0-隔离解除 1-隔离]</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetCommDoor(System.Int32[],System.Int32)">
            <summary>
            设置通讯通道选择
            </summary>
            <param name="frameData">帧数据</param>
            <param name="oneDoorType">1路485[0-485 1-蓝牙通讯 2-232通讯]]</param>
            <param name="twoDoorType">2路485[0-485 1-蓝牙通讯 2-232通讯]</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetUAndIOutputLoop(System.Int32[],System.Int32,System.Int32)">
            <summary>
            选择电压和电流输出回路（CL188L）
            </summary>
            <param name="uLoop">电压回路[0-直接接入式 1-互感器接入式 2-表位无电表接入]</param>
            <param name="iLoop">电流回路[0-第一回路 1-第二回路]</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.Clear188State(System.Collections.Generic.List{System.Int32})">
            <summary>
            清除误差板表位状态
            </summary>
            <param name="meterId">表位号</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetEncryptionCode(System.Collections.Generic.List{System.String})">
            <summary>
            设置标准表序列号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetEncryptionCode(System.Byte[]@)">
            <summary>
            获取标准表序列号
            </summary>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetStdMeterMonitorData(CLDC.CLAT.CLWBS.DataModel.Struct.StMonitorData@)">
            <summary>
            标准表数据
            </summary>
            <param name="monitorData"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ChangeStdMeterLineMode(CLDC.Framework.DataModel.Enum.EmStallMode)">
            <summary>
            设置标准表接线方式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetStdMeterstall(CLDC.Framework.DataModel.Enum.EmWireMode,CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam)">
            <summary>
            设置标准表接线方式和档位
            </summary>
            <param name="WireMode">接线方式</param>
            <param name="sourceparam">档位</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetStdMeterStall(System.Single,System.Single)">
            <summary>
            设置标准表档位
            </summary>
            <param name="sngMaxVolt"></param>
            <param name="sngMaxCurt"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetYuanEncryptionCode(System.Collections.Generic.List{System.String})">
            <summary>
            设置源序列号
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetYuanEncryptionCode(System.Byte[]@)">
            <summary>
            获取源序列号
            </summary>
            <param name="constan"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.PowerOFF">
            <summary>
            关源
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.PowerOFF(System.Int32)">
            <summary>
            关源
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode)">
            <summary>
            升源
            </summary>
            <param name="sourceparam">控源参数</param>
            <param name="type">0，更新电压，1更新电流，2都更新</param>
            <param name="valueorph">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="wireMode">接线模式</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.PowerOn(CLDC.CLAT.CLWBS.DataModel.Struct.StSourceParam,System.Int32,System.Int32,CLDC.Framework.DataModel.Enum.EmWireMode,System.Int32)">
            <summary>
            升源
            </summary>
            <param name="sourceparam">控源参数</param>
            <param name="type">0，更新电压，1更新电流，2都更新</param>
            <param name="valueorph">更新类型 0：幅值，1：相位，2：都更新</param>
            <param name="wireMode">接线模式</param>
            <param name="DingDang">定档升电流,0设置电流档位。1为直接更新电流</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.PowerSetCurrentGear(System.Single)">
            <summary>
            设置电流档位
            </summary>
            <param name="current"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.OpenLED">
            <summary>
            开LED
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.CloseLED">
            <summary>
            关LED
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetCt(System.Byte,System.Collections.Generic.List{System.Int32})">
            <summary>
            设置CT档位
            </summary>
            <param name="type">0:CT为100A档位，1:CT为2A档位</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetOutputLoopByRelay(CLDC.Framework.DataModel.Enum.EmLinkType)">
            <summary>
            通过继电器设置电压电流回路
            </summary>
            <param name="AccessType"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetZJHG(CLDC.Framework.DataModel.Enum.EmLinkType,System.Collections.Generic.List{System.Int32})">
            <summary>
            切换直接互感
            </summary>
            <param name="AccessType">接线方式</param>
            <param name="MeterIDs">表位号集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.GetMetersChances(System.Collections.Generic.List{System.Int32})">
            <summary>
            获取表位通道
            </summary>
            <param name="meterIds"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.CloseCL2038ControlWork(System.Byte)">
            <summary>
            控制耐压工作
            </summary>
            <param name="actTag">动作类型[0-关源 1-升源]</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ShutDownRelay">
            <summary>
            关掉所有继电器
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.RemotePowerOn">
            <summary>
            控制台体上电，一键启停功能
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ApplyPowerControl">
            <summary>
            手动操作源时进行注册方法
            </summary>
            <param name="regeditObject"></param>
            <param name="schemMothe"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.BluetoothConnection(System.Collections.Generic.Dictionary{System.Int32,System.String},CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local)">
            <summary>
            蓝牙连接
            </summary>
            <param name="dicMeterAddrss"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.BluetoothConnectionGW(System.Collections.Generic.List{System.Int32},CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel,System.Collections.Generic.Dictionary{System.Int32,System.String}@)">
            <summary>
            国网蓝牙连接
            </summary>
            <param name="dicMeterAddrss"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.ParseBluetoothParams(CLDC.CLAT.CLWBS.DataModel.BluetoothParams.BluetoothParamsModel@)">
            <summary>
            获取蓝牙参数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.SetMeterModeSwicth(System.Collections.Generic.Dictionary{System.Int32,System.String},System.String,CLDC.CLAT.CLWBS.DataModel.Enum.EmCommProtCode)">
            <summary>
            切换校表模式
            </summary>
            <param name="dicMeterAddrss">表地址</param>
            <param name="chancel">通道号  低4位有效（有功、无功）0F表示0路全开 00表示退出校表模式</param>
            <param name="emCommProtCode">协议类型</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.InterfaceOperate.MeterIDs(System.Collections.Generic.Dictionary{System.Int32,System.String})">
            <summary>
            表号
            </summary>
            <param name="lstIDAddrs">表地址</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.FunctionCodeModel">
            <summary>
            功能码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.FunctionCodeModel.Type">
            <summary>
            功能码类型 698，645
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.FunctionCodeModel.FunctionCode">
            <summary>
            实验类型
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.LicenseManage">
            <summary>
            权限管理
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.LicenseManage.SerialNo">
            <summary>
            序列号模型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.LicenseManage.FunctionItems">
            <summary>
            功能码模型
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.RunApplication(CLDC.CLAT.CLWBS.Manage.SerialNoModel,System.String@)">
            <summary>
            控制软件运行模式
            </summary>
            <param name="serialMode"></param>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.IsFileExists">
            <summary>
            判断序列号文件是否存在
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.GetSerialNo">
            <summary>
            获取并解码序列号
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.GetdeviceSerialNumberList(System.Collections.Generic.List{System.String}@)">
            <summary>
            获取设备序列号
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.CheckSerialNumber(CLDC.CLAT.CLWBS.Manage.SerialNoModel)">
            <summary>
            校验序列号
            </summary>
            <param name="Serial"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.LicenseManage.GetFunctionItems">
            <summary>
            获取功能码                                          
            </summary>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.SerialNoModel">
            <summary>
            序列号模型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SerialNoModel.RegisterCode">
            <summary>
            注册码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SerialNoModel.ValidityPeriod">
            <summary>
            有效期
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SerialNoModel.AlramDays">
            <summary>
            告警天数
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SerialNoModel.HaveFunCode">
            <summary>
            是否包含功能码
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SchemeGruop.CurrentScheme">
            <summary>
            当前实验项目
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SchemeGruop.LstSubSchemeGruop">
            <summary>
            需并行的实验项目集
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.SchemeGruop.MinSchemeOrder">
            <summary>
            该组实验项目最小顺序号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.SchemeParallelOrder">
            <summary>
            执行实验项目顺序号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.IsExeOk">
            <summary>
            执行完成功与否
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.LstFialMeterId">
            <summary>
            执行完成后的不合格表表位号
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.IsStartExe">
            <summary>
            是否已经开始执行
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.IsEndExe">
            <summary>
            是否结束执行
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.ExeParallelScheme.IsVotPower">
            <summary>
            是否升电压源
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly">
            <summary>
            与数据管理交互
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.ControlType">
            <summary>
            控制类型
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.LogReadWrite">
            <summary>
            LOG
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._businessAssmeblyManager">
            <summary>
            业务实际对象集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._ControlsManage">
            <summary>
            控制类集
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._EquipMentControl">
            <summary>
            开放设备控制
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._XmlInfoAndAssembly">
            <summary>
            配置文件和反射对象集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._systemXmlInfo">
            <summary>
            系统配制文件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly._CurrentEmCommProtCode">
            <summary>
            当前规约类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.ClassNames">
            <summary>
            业务类集合
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.GetInstance">
            <summary>
            单例
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.BusinessAssmebly(CLDC.CLAT.CLWBS.DataModel.Enum.EmCommProtCode)">
            <summary>
            反射业务类
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.LoadAssmebly">
            <summary>
            构造方法
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.LoadAssmebly(CLDC.CLAT.CLWBS.Core.ISimulation,CLDC.CLAT.CLWBS.DataModel.Enum.EmCommProtCode,System.String[],CLDC.Framework.DataModel.Enum.EmMeterType)">
            <summary>
            从DLL加载业务对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.SetConfigInfoToAssembly">
            <summary>
            将配置文件信息加载到反射类中去
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.FormatString(System.String)">
            <summary>
            Sonar问题
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.SetMeterParams(System.String)">
            <summary>
            设置CL2018波特率(设备)
            </summary>
            <param name="MeterParams"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.PingIPAddress(System.String)">
            <summary>
            Ping网口
            </summary>
            <param name="ipAddress"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.GetTcpSendFrame(System.String,System.Byte)">
            <summary>
            获取tcp方式设置服务器端口波特率发送数据
            </summary>
            <param name="MeterParams"></param>
            <param name="portId"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.XmlInfoAndAssembly.SetBusinessControl">
            <summary>
            配置业务对应控制方式
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.EquipMentsManage.LoadAssmebly(CLDC.CLAT.CLWBS.Core.ISimulation)">
            <summary>
            反射设备类
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.Manage.PortManage._portAssmeblyManager">
            <summary>
            设备管理对象集合
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.PortManage.#ctor(CLDC.CLAT.CLWBS.ILog.ILog,System.String)">
            <summary>
            构造函数 
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.PortManage.GetInstance(CLDC.CLAT.CLWBS.ILog.ILog,System.String)">
            <summary>
            单列
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.Manage.PortManage.LoadAssmebly(System.String)">
            <summary>
            反射端口类
            </summary>
        </member>
    </members>
</doc>
