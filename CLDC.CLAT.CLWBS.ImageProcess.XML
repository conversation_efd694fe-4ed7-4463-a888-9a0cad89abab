<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CLDC.CLAT.CLWBS.ImageProcess</name>
    </assembly>
    <members>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AVTCamera.#ctor(System.Int32)">
            <summary>
            构造
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AVTCamera.InitSigleCamera(System.Int32)">
            <summary>
            根据相机数量依次初始化各个相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.CameraIDs">
            <summary>
            摄像机数组
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.InitSigleCamera(System.Int32)">
            <summary>
            根据相机数量依次初始化各个相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.OnNotification">
            <summary>
            相机1接收照片
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.ChangeBitmapToHObject(System.Int32,System.Drawing.Bitmap)">
            <summary>
            将Bitmap转为HObject
            </summary>
            <param name="bitmap"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.OpenTrigger">
            <summary>
            打开外触发
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.CloseTrigger">
            <summary>
            关闭外触发
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.SoftTrigger">
            <summary>
            打开软触发
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.GetImage(System.Int32)">
            <summary>
            软件触发，自己拍照
            </summary>
            <param name="cameraID"></param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.WriteOutputOne(System.Int32,System.Int32)">
            <summary>
            发送信号给相机1
            </summary>
            <param name="camraID">相机ID</param>
            <param name="sleepTime">高低电平之间的延时</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.WriteOutputTwo(System.Int32,System.Int32)">
            <summary>
            发送高低平信号给相机2
            </summary>
            <param name="camraID"></param>
            <param name="sleepTime"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.AvtCameraWTrigger.WriteOutputThree(System.Int32,System.Int32)">
            <summary>
            发送高低平信号给相机3
            </summary>
            <param name="camraID"></param>
            <param name="sleepTime"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.HalconInitCameras.#ctor(System.Int32)">
            <summary>
            初始化相机
            </summary>
            <param name="cameraCount">台体相机总数</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.HalconInitCameras.InitSigleCamera(System.Int32)">
            <summary>
            根据相机数量依次初始化各个相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.HalconInitCameras.ReadString(System.String,System.String,System.String)">
            <summary>
            读取INI文件指定键文本数据
            </summary>
            <param name="Section">段名</param>
            <param name="Key">键</param>
            <param name="path">path</param>
            <returns>字符串键值</returns>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.CameraIDs">
            <summary>
            相机ID
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.GetImage(System.Int32)">
            <summary>
            获取图片
            </summary>        
            <param name="CameraID">相机ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.SoftTrigger">
            <summary>
            打开软触发
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.OpenTrigger">
            <summary>
            打开外触发
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.CloseTrigger">
            <summary>
            关闭外触发
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ICamera.InitSigleCamera(System.Int32)">
            <summary>
            根据指定相机编号初始化对应相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.ImageReadFromFile.InitSigleCamera(System.Int32)">
            <summary>
            根据相机数量依次初始化各个相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CCDLibarary.Camera.TheImagingSource.InitSigleCamera(System.Int32)">
            <summary>
            根据指定相机编号初始化对应相机
            </summary>
            <param name="CameraNum"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CDHVisionCamera.#ctor">
            <summary>
            CCD检查构造
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CDHVisionCamera.InitUniCamera">
            <summary>
            初始化相机
            </summary>
            <returns>相机对象</returns>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.CDHVisionCamera.obj_Ini_Lock">
            <summary>
            读取INI文件指定键文本数据
            </summary>
            <param name="Section">段名</param>
            <param name="Key">键</param>
            <param name="path">路径</param>
            <returns>字符串键值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.ReadString(System.String,System.String,System.String)">
            <summary>
            读取INI文件指定键文本数据
            </summary>
            <param name="Section">段名</param>
            <param name="Key">键</param>
            <param name="path">路径</param>
            <returns>字符串键值</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.DispBatchForeground">
            <summary>
            显示前景
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.m_roiUsedForDisp">
            <summary>
            感兴趣区域
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.m_roiSave">
            <summary>
            感兴趣区域
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.TryCreateScaledShapeModel(HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HObject,HalconDotNet.HTuple@)">
            <summary>
            用定位模板图片生成定位模板（获取定位模板ID）
            </summary>
            <param name="contrastHigh">高对比度</param>
            <param name="contrastLow">低对比度</param>
            <param name="ho_ImageReduced">定位模板图片</param>
            <param name="hv_ModelID">定位模板ID</param>
            <returns>定位模板ID</returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.CreateModelHandle(HalconDotNet.HObject,HalconDotNet.HObject,System.String,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,System.Int32,System.Int32@,System.Int32@)">
            <summary>
            建模板
            </summary>
            <param name="ho_idealImage">理想图片</param>
            <param name="ho_RectangleROI">感兴趣区域</param>
            <param name="modelName">模板名称</param>
            <param name="contrastHigh"></param>
            <param name="contrastLow"></param>
            <param name="roiThreshold">对比区域阈值</param>
            <param name="minFaultArea">最小缺陷面积</param>
            <param name="realContrastHigh"></param>
            <param name="realContrastLow"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.GetVariationModel(HalconDotNet.HObject,System.Int32,System.Double,HalconDotNet.HTuple@)">
            <summary>
            生成变化图片ID
            </summary>
            <param name="ho_idealImage">理想图片</param>
            <param name="ABS_THRESHOLD">变化权值</param>
            <param name="VAR_THRESHOLD">灰度阈值</param>
            <param name="VariationID">变化模板ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.GenerateStroke(HalconDotNet.HObject,System.Int32)">
            <summary>
            生成笔画
            </summary>
            <param name="ho_RegionForeground">前景图片</param>
            <param name="minFaultArea">最小缺陷面积</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageHandler.ImageHandler.CreateGeneralImageModel(System.String,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,System.Int32)">
            <summary>
            创建模板
            </summary>
            <param name="modelName"></param>
            <param name="contrastHigh"></param>
            <param name="contrastLow"></param>
            <param name="roiThreshold"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.SetOldImageShow">
            <summary>
            还原
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.button1_Click_1(System.Object,System.EventArgs)">
            <summary>
            创建LED对比模板
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.button2_Click(System.Object,System.EventArgs)">
            <summary>
            拍一张照片
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.CreateModel.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess">
            <summary>
            图像显示窗体所加载的控件
            2013-11-1 ZHRZ
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.ucImageProcess">
            <summary>
            图像处理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.ProcessImage">
            <summary>
            图像处理对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.MeterNum">
            <summary>
            表位数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.CameraNum">
            <summary>
            相机数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.MeterID">
            <summary>
            表位编号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.CameraID">
            <summary>
            相机编号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.MeterType">
            <summary>
            表类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.CurrentImg">
            <summary>
            当前图像
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.#ctor(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.GetInstance(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.UcImageProcessFuJian_Load(System.Object,System.EventArgs)">
            <summary>
            事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcess.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD">
            <summary>
            图像显示窗体所加载的控件
            2013-11-1 ZHRZ
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.ucImageProcess">
            <summary>
            图像处理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.MeterNum">
            <summary>
            表位数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.MeterID">
            <summary>
            表位编号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.CameraID">
            <summary>
            相机编号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.MeterType">
            <summary>
            表类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.CurrentImg">
            <summary>
            当前图像
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.#ctor(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.GetInstance(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.UcImageProcessFuJian_Load(System.Object,System.EventArgs)">
            <summary>
            事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.NewUcImageProcessGD.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageNanJingDLL">
            <summary>
            相片处理综合类
            2013-11-4
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageNanJingDLL.GetInstance">
            <summary>
            相片处理
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageNanJingDLL.#ctor">
            <summary>
            相片处理
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize">
            <summary>
            相片处理综合类
            2013-11-4
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ImageQueue">
            <summary>
            图像队列
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitImageProcessObject">
            <summary>
            图像处理集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitCameraImageShow">
            <summary>
            相机图片显示
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitCropImage">
            <summary>
            分解图像对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.MinFaultArea">
            <summary>
            最小缺陷面积集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.AngleExtent">
            <summary>
            选择角度范围集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CameraObject">
            <summary>
            相机
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ProcessImage">
            <summary>
            相片处理
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitHwindows">
            <summary>
            显示相片窗体集合
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ditCameraHwindows">
            <summary>
            相机对应窗体
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitCameraHwindows">
            <summary>
            相机对应窗体
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitTempHObject">
            <summary>
            临时存放图像文件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.cameraNum">
            <summary>
            相机数量
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CameraNum">
            <summary>
            相机数量
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.cameraBrand">
            <summary>
            相机品牌
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CameraBrand">
            <summary>
            相机品牌
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.checkNameplate">
            <summary>
            是否检测铭牌
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckNameplate">
            <summary>
            是否检测铭牌
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckBarCode">
            <summary>
            是否检测条码
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.saveImageOrNo">
            <summary>
            是否保存图像
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.SaveImageOrNo">
            <summary>
            是否检测铭牌
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DitCameraMeter">
            <summary>
            相机表位对应关系key为相机编号，value为相机对应表位
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.IsInitFinsh">
            <summary>
            初始化已经完成与否
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.MeterModel">
            <summary>
            表型号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ErrArea">
            <summary>
            缺陷面积
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CurrentImg">
            <summary>
            当前拍照窗口图像
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.#ctor">
            <summary>
            相片处理
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.GetInstance">
            <summary>
            相片处理
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.InitProcessImage">
            <summary>
            初始化图像处理对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.InitImageModel(System.Int32,System.String)">
            <summary>
            点击加载模板时初始化相机模板
            </summary>
            <param name="index">当前正在加载模板的控件索引</param>
            <param name="meterModel">表类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.InitImageModel(System.String)">
            <summary>
            做业务时自动初始化相机模板
            </summary>
            <param name="meterModel">表类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.InitCheck(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            初始化检测
            </summary>
            <param name="lstMeterInfo">表信息</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.Check(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Int32,System.String)">
            <summary>
            四川终端CCD三相检三种表
            
            ---------------------------
            直2    直1     互感2  互感1
            
            终端2  终端1   
            ---------------------------
            </summary>
            <param name="lstMeterInfo">相机对应的表位集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.Check(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Int32,System.Int32)">
            <summary>
            一个相机拍一个表位方式检测
            </summary>
            <param name="lstMeterInfo">相机对应的表位集合</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.Check(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local})">
            <summary>
            一个相机同时拍两个表的检测方式
            </summary> 
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="CameraID">相机ID</param>      
            <param name="ErrArea">错误面积</param>
            <param name="lstMeterInfo">要检的表信息集合</param>
            <returns></returns>       
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.Check(System.Collections.Generic.List{CLDC.CLAT.CLWBS.DataModel.Class.ClMeterInfo_Local},System.Int32)">
            <summary>
            外触发模式检测
            </summary>
            <param name="lstMeterInfo">表信息</param>
            <param name="type">类型区别</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckImage">
            <summary>
            图像检测线程
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ParamChangeHandle(System.String,System.Int32,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32@,System.Int32@,System.Boolean@,System.Int32)">
            <summary>
            拖动后窗体显示相应效果
            </summary>
            <param name="modelName">模板名</param>
            <param name="meterId">表位号</param>
            <param name="AutoCheckBoxChecked">按钮选中状态</param>
            <param name="hContrastHigh">对比度高</param>
            <param name="hContrastLow">对比度低</param>
            <param name="hROIThreshold">阈值</param>
            <param name="realContrastHigh">返回对比度高</param>
            <param name="realContrastLow">返回对比度低</param>
            <param name="result">返回是否成功</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.ParamChangeHandle(System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            拖动后窗体显示相应效果
            </summary>
            <param name="modelName">模板名</param>
            <param name="OCRTrainStr">训练字符串</param>
            <param name="meterId">表位号</param>
            <param name="index">字库索引号</param>
            <param name="height">字体高度</param>
            <param name="width">字体宽度</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CreateLEDModel(System.Int32,System.String,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,System.Int32)">
            <summary>
            创建LED模板
            </summary>
            <param name="meterId">表位号</param>
            <param name="modelName">模板名称</param>
            <param name="contrastHigh">对比度高</param>
            <param name="contrastLow">对比度低</param>
            <param name="roiThreshold">阈值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CropImageNoToMeterWindows(System.Int32,System.Collections.Generic.List{System.Int32},HalconDotNet.HObject[]@)">
            <summary>
            分解图像为2张照片
            </summary>
            <param name="cameraId">相机ID</param>
            <param name="lstMeterId">分开为多少表位照片集合</param>
            <param name="a_imageObject">分解之后的图片集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CropImageToMeterWindows(System.Int32,System.Collections.Generic.List{System.Int32},HalconDotNet.HObject[]@)">
            <summary>
            分解图像为2张照片
            </summary>
            <param name="cameraId">相机ID</param>
            <param name="lstMeterId">分开为多少表位照片集合</param>
            <param name="a_imageObject">分解之后的图片集合</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CopyImageToMeterWindows(System.Int32,System.Int32,System.Int32)">
            <summary>
            复制一张图像
            </summary>
            <param name="cameraId">相机ID</param>
            <param name="meterId">表位ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.GetImageFromCamera(System.Int32,System.Int32)">
            <summary>
            拍一张照片，显示到窗体
            </summary>
            <param name="cameraId">相机ID</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.getOneImg(System.Int32,System.Int32)">
            <summary>
            单帧采集
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.StartGrabImg(System.Int32)">
            <summary>
            连续采集
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.DisposeModel">
            <summary>
            释放模板
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.MeterZoomDisplayHandle(System.Int32,System.Int32,System.Double,System.Double)">
            <summary>
            表位图像缩放
            </summary>
            <param name="index">该表位ID</param>
            <param name="delta"></param>
            <param name="winPartX"></param>
            <param name="winPartY"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CameraZoomDisplayHandle(System.Int32,System.Int32,System.Double,System.Double)">
            <summary>
            相机图像缩放
            </summary>
            <param name="index">该相机ID</param>
            <param name="delta"></param>
            <param name="winPartX"></param>
            <param name="winPartY"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckGeneralImage(System.Int32,System.String,System.Int32)">
            <summary>
            检测当前窗体上显示的照片的LED
            </summary>
            <param name="index">当前窗体的索引号</param>
            <param name="ModelName">表类型</param>
            <param name="minFaultArea">最小缺陷面积</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckMeterLED(System.Int32,System.String,System.Int32)">
            <summary>
            检测当前窗体上显示的照片的LED
            </summary>
            <param name="index">当前窗体的索引号</param>
            <param name="ModelName">表类型</param>
            <param name="minFaultArea">最小缺陷面积</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckMeterType(System.String,System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            检测当前窗体表型号规格
            </summary>
            <param name="regionFileName">识别区域文件名</param>
            <param name="OCRFileName">字库类型</param>
             <param name="index">表位号</param>
            <param name="height">字体高度</param>
            <param name="width">字体宽度</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckMeterType(System.String,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            检测当前窗体表型号规格
            </summary>
            <param name="modelName">电能表类型名</param>
            <param name="meterId">表位号</param>
            <param name="OCRString1">表类型</param>
            <param name="OCRString2">表规格</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.ProcessImageSynthesize.CheckMeterBarcode(System.String,System.Int32)">
            <summary>
            检测当前窗体表条码
            </summary>
            <param name="modelName">表类型</param>
            <param name="meterId">表位号</param>
            <returns></returns>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess">
            <summary>
            图像显示窗体所加载的控件
            2013-11-1 ZHRZ
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.ucImageProcess">
            <summary>
            图像处理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.ProcessImage">
            <summary>
            图像处理对象
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.MeterNum">
            <summary>
            表位数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.CameraNum">
            <summary>
            相机数
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.CurrentImg">
            <summary>
            当前图像
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.hWndControl">
            <summary>
            halcon控件数组
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.#ctor(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.GetInstance(System.Int32,System.Int32)">
            <summary>
            图像显示
            </summary>
            <param name="meterNum">表数量</param>
            <param name="cameraNum">相机数量</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.UcImageProcessFuJian_Load(System.Object,System.EventArgs)">
            <summary>
            事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.ParamChangeHandle">
            <summary>
            拖动后窗体显示相应效果
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.Form.UcImageProcess.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger">
            <summary>
            图像处理管理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.p">
            <summary>
            图像处理管理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.TriggerImageQueue">
            <summary>
            相机一
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.MeterBarCode1">
            <summary>
            当前一号相机拍照的表位号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.MeterBarCode2">
            <summary>
            当前二号相机拍照的表位号
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.DITImageProcessObject">
            <summary>
            图像处理对象集
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.NowMeterType">
            <summary>
            当前表类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.Angle1">
            <summary>
            相机一默认角度为90
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.Angle2">
            <summary>
            相机二默认角度为90
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.ErrArea">
            <summary>
            默认不合格区域面积为40
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger._ImageShow">
            <summary>
            显示控件
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.imageShow">
            <summary>
            显示控件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.CameraObject">
            <summary>
            相机对象
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.CameraIDs">
            <summary>
            相机ID
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.GetInstance">
            <summary>
            实例化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.#ctor">
            <summary>
            构造方法
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.InitProcessImage(System.Int32,System.Int32,System.Int32)">
            <summary>
            初始化相机的参数(拍照角度，比对区域面积)
            </summary>
            <param name="angle1"></param>
            <param name="angle2"></param>
            <param name="errArea"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.SystemEvent_SendBitmapToProcess(System.Int32,HalconDotNet.HObject)">
            <summary>
            接收相机上传的图像
            </summary>
            <param name="cameraID"></param>
            <param name="bmp"></param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.CheckLEDThread">
            <summary>
            线程处理LED对比任务    相机1
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.WriteOutput(System.Int32,System.Int32,System.Int32,System.String,System.String)">
            <summary>
             相机三个输出口 高低电平输出
            </summary>
            <param name="cameraId">相机号</param>
            <param name="outPutId">相机输出口</param>
            <param name="sleepTime">休眠时间</param>
            <param name="meterBarCode1">一号相机需拍照的条码</param>
            <param name="meterBarCode2">二号相机需拍照的条码</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.OpenOrCloseTrigger(System.Boolean)">
            <summary>
            打开或关闭相机
            </summary>
            <param name="isOpen">true 为打开</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.ReadMeterModel(System.String)">
            <summary>
            读模板
            </summary>
            <param name="MeterType">表类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.CreateLEDModel(System.Int32,System.String,System.Int32,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HObject)">
            <summary>
            创建对比模板
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="MeterType">表类型</param>
            <param name="CameraID">相机ID</param>
            <param name="contrastHigh">对比度高</param>
            <param name="contrastLow">对比度低</param>
            <param name="roiThreshold">阈值</param>
            <param name="Image">要创建模版的照片</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.ParamChangeHandle(System.String,System.Int32,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple)">
            <summary>
            修改参数
            </summary>
            <param name="MeterType">表类型</param>
            <param name="contrastHigh">对比度高</param>
            <param name="contrastLow">对比度低</param>
            <param name="roiThreshold">阈值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.ParamChangeHandle(System.String,System.Int32,System.Int32@,System.Int32@,HalconDotNet.HTuple)">
            <summary>
            修改参数
            </summary>
            <param name="MeterType">表类型</param>
            <param name="realContrastHigh">对比度高</param>
            <param name="realContrastLow">对比度低</param>
            <param name="roiThresholdd">阈值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.GetImage(System.Int32,System.Int32,System.String,System.Int32)">
            <summary>
            拍照
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="CameraID">相机ID</param>
            <param name="MeterBarCode">表条码</param>
            <param name="ErrArea">错误面积</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.GetImage(System.Int32,System.Int32)">
            <summary>
            拍照并显示到窗口
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="CameraID">相机ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageTrigger.DisposeModel">
            <summary>
            释放模板
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.ImageShow.HControl">
            <summary>
            控件窗体显示图像
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ImageShow.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageShow.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ImageShow.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage">
            <summary>
            图像处理管理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.p">
            <summary>
            图像处理管理
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.DITImageProcessObject">
            <summary>
            处理对象
            </summary>
            <summary>
            图像处理对象集
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.ImageQueue">
            <summary>
            图像队列
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.NowMeterType">
            <summary>
            当前表类型
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage._ImageShow">
            <summary>
            显示控件
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.imageShow">
            <summary>
            显示控件
            </summary>
        </member>
        <member name="F:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.CameraObject">
            <summary>
            相机对象
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.GetInstance">
            <summary>
            实例化
            </summary>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.#ctor">
            <summary>
            构造方法
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.CameraIDs">
            <summary>
            相机ID
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.ReadMeterModel(System.String)">
            <summary>
            读模板
            </summary>
            <param name="MeterType">表类型</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.GetImage(System.Int32,System.Int32,System.String,System.Int32)">
            <summary>
            拍照
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="CameraID">相机ID</param>
            <param name="MeterBarCode">表条码</param>
            <param name="ErrArea">错误面积</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.GetImage(System.Int32,System.Int32)">
            <summary>
            拍照并显示到窗口
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="CameraID">相机ID</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.CreateLEDModel(System.Int32,System.String,System.Int32,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HObject,System.Int32)">
            <summary>
            创建对比模板
            </summary>
            <param name="angle">相片角度:-90度，:90度</param>
            <param name="MeterType">表类型</param>
            <param name="CameraID">相机ID</param>
            <param name="contrastHigh">对比度高</param>
            <param name="contrastLow">对比度低</param>
            <param name="roiThreshold">阈值</param>
            <param name="Image">要创建模版的照片</param>
            <returns></returns>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.ParamChangeHandle(System.String,System.Int32,HalconDotNet.HTuple,HalconDotNet.HTuple,HalconDotNet.HTuple,System.Int32)">
            <summary>
            修改参数
            </summary>
            <param name="MeterType">表类型</param>
            <param name="contrastHigh">对比度高</param>
            <param name="contrastLow">对比度低</param>
            <param name="roiThreshold">阈值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.ParamChangeHandle(System.String,System.Int32,System.Int32@,System.Int32@,HalconDotNet.HTuple,System.Int32)">
            <summary>
            修改参数
            </summary>
            <param name="MeterType">表类型</param>
            <param name="realContrastHigh">对比度高</param>
            <param name="realContrastLow">对比度低</param>
            <param name="roiThresholdd">阈值</param>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.CheckLEDThread">
            <summary>
            线程处理LED对比任务
            </summary>
        </member>
        <member name="M:CLDC.CLAT.CLWBS.ImageProcess.ProcessImageManage.DisposeModel">
            <summary>
            释放模板
            </summary>
        </member>
        <member name="T:CLDC.CLAT.CLWBS.ImageProcess.Properties.Resources">
            <summary>
              强类型资源类，用于查找本地化字符串等。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:CLDC.CLAT.CLWBS.ImageProcess.Properties.Resources.Culture">
            <summary>
              为使用此强类型资源类的所有资源查找
              重写当前线程的 CurrentUICulture 属性。
            </summary>
        </member>
        <member name="T:ViewROI.FunctionPlot">
            <summary>
            This class allows you to print a function into a Windows.Forms.Control.
            The function must be supplied as an array of either double, float or
            int values. When initializing the class you must provide the control that 
            actually draws the function plot. During the initialization you 
            can also decide whether the mouse event is used to return the (x,y) 
            coordinates of the plotted function. The scaling 
            of the x-y axis is performed automatically and depent on the length 
            of the function as well as the settings for the y-axis scaling: 
                * AXIS_RANGE_FIXED
                * AXIS_RANGE_INCREASING
                * AXIS_RANGE_ADAPTING 
            
            Constraints of the function plot class:
            So far only functions containing a positive range of y-values can
            be plotted correctly. Also only a positive and ascending x-range
            can be plotted. The origin of the coordinate system is set to
            be in the lower left corner of the control. Another definition 
            of the origin and hence the direction of the coordinate axis 
            is not implemented yet. 
            </summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.#ctor(System.Windows.Forms.Control,System.Boolean)">
            <summary>
            Initializes a FunctionPlot instance by providing a GUI control
            and a flag specifying the mouse interaction mode.
            The control is used to determine the available space to draw
            the axes and to plot the supplied functions. Depending on the
            available space, the values of the function as well as the axis
            steps are adjusted (scaled) to fit into the visible part of the 
            control.
            </summary>
            <param name="panel">
            An instance of the Windows.Forms.Control to plot the 
            supplied functions in
            </param>
            <param name="useMouseHandle">
            Flag that specifies whether or not mouse interaction should
            be used to create a navigation bar for the plotted function 
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.#ctor(System.Windows.Forms.Control)">
            <summary>
            Convenience method for constructor call. For this case the 
            useMouseHandle flag is set to false by default.
            </summary>
            <param name="panel">
            An instance of the Windows.Forms.Control to plot the 
            supplied function in.
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.setOrigin(System.Int32,System.Int32)">
            <summary>
            Changes the origin of the coordinate system to be 
            at the control positions x and y.
            </summary>
            <param name="x">
            X position within the control coordinate system
            </param>
            <param name="y">
            Y position within the control coordinate system. 
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.setAxisAdaption(System.Int32,System.Single)">
            <summary>
            Sets the type of scaling for the y-axis. If the 
            y-axis is defined to be of fixed size, then the upper 
            limit has to be provided with val. Otherwise,
            an 8-bit image is assumed, so the fixed size is set
            to be 255.
            </summary>
            <param name="mode">
            Class constant starting with AXIS_RANGE_*
            </param>
            <param name="val">
            For the mode AXIS_RANGE_FIXED the value 
            val must be positive, otherwise
            it has no meaning.
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.plotFunction(System.Double[])">
            <summary>
            Plots a function of double values.
            </summary>
            <param name="grayValues">
            Y-values defined as an array of doubles
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.plotFunction(System.Single[])">
            <summary>
            Plots a function of float values.
            </summary>
            <param name="grayValues">
            Y-values defined as an array of floats
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.plotFunction(System.Int32[])">
            <summary>
            Plots a function of integer values.
            </summary>
            <param name="grayValues">
            Y-values defined as an array of integers
            </param>
        </member>
        <member name="M:ViewROI.FunctionPlot.drawFunction(HalconDotNet.HTuple)">
            <summary>Plots a function provided as an HTuple</summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.resetPlot">
            <summary>
            Clears the panel and displays only the coordinate axes.
            </summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.repaint">
            <summary>
            Puts (=flushes) the current content of the graphics object on screen 
            again.
            </summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.drawLineCurve(HalconDotNet.HTuple,System.Single)">
            <summary>Plots the points of the function.</summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.scaleDispValue(HalconDotNet.HTuple)">
            <summary>
            Scales the function to the dimension of the graphics object 
            (provided by the control). 
            </summary>
            <param name="tup">
            Function defined as a tuple of y-values
            </param>
            <returns>
            Array of PointF values, containing the scaled function data
            </returns>
        </member>
        <member name="M:ViewROI.FunctionPlot.scaleDispBlockValue(HalconDotNet.HTuple)">
            <summary>
            Scales the function to the dimension of the graphics object 
            (provided by the control). If the stepsize  for the x-axis is
            1, the points are scaled in a block shape.
            </summary>
            <param name="tup">
            Function defined as a tuple of y-values 
            </param>
            <returns>
            Array of PointF values, containing the scaled function data
            </returns>
        </member>
        <member name="M:ViewROI.FunctionPlot.drawXYLabels">
            <summary>Draws x- and y-axis and its labels.</summary>
            <returns>Step size used for the x-axis</returns>
        </member>
        <member name="M:ViewROI.FunctionPlot.mouseMoved(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            Action call for the Mouse-Move event. For the x-coordinate
            supplied by the MouseEvent, the unscaled x and y coordinates
            of the plotted function are determined and displayed 
            on the control.
            </summary>
        </member>
        <member name="M:ViewROI.FunctionPlot.paint(System.Object,System.Windows.Forms.PaintEventArgs)">
            <summary>
            Action call for the Paint event of the control to trigger the
            repainting of the function plot. 
            </summary>
        </member>
        <member name="T:ViewROI.CLGraphicsContext">
            <summary>
            This class contains the graphical context of an HALCON object. The
            set of graphical modes is defined by the hashlist 'graphicalSettings'.
            If the list is empty, then there is no difference to the graphical
            setting defined by the system by default. Otherwise, the provided
            HALCON window is adjusted according to the entries of the supplied
            graphical context (when calling applyContext()) 
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_COLOR">
            <summary>
            Graphical mode for the output color (see dev_set_color)
            </summary>        
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_COLORED">
            <summary>
            Graphical mode for the multi-color output (see dev_set_colored)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_LINEWIDTH">
            <summary>
            Graphical mode for the line width (see set_line_width)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_DRAWMODE">
            <summary>
            Graphical mode for the drawing (see set_draw)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_SHAPE">
            <summary>
            Graphical mode for the drawing shape (see set_shape)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_LUT">
            <summary>
            Graphical mode for the LUT (lookup table) (see set_lut)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_PAINT">
            <summary>
            Graphical mode for the painting (see set_paint)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.GC_LINESTYLE">
            <summary>
            Graphical mode for the line style (see set_line_style)
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.graphicalSettings">
            <summary> 
            Hashlist containing entries for graphical modes (defined by GC_*), 
            which is then linked to some HALCON object to describe its 
            graphical context.
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.stateOfSettings">
            <summary> 
            Backup of the last graphical context applied to the window.
            </summary>
        </member>
        <member name="F:ViewROI.CLGraphicsContext.gcNotification">
            <summary> 
            Option to delegate messages from the graphical context 
            to some observer class 
            </summary>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.#ctor">
            <summary> 
            Creates a graphical context with no initial 
            graphical modes 
            </summary> 
        </member>
        <member name="M:ViewROI.CLGraphicsContext.#ctor(System.Collections.Hashtable)">
            <summary> 
            Creates an instance of the graphical context with 
            the modes defined in the hashtable 'settings' 
            </summary>
            <param name="settings"> 
            List of modes, which describes the graphical context 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.applyContext(HalconDotNet.HWindow,System.Collections.Hashtable)">
            <summary>Applies graphical context to the HALCON window</summary>
            <param name="window">Active HALCON window</param>
            <param name="cContext">
            List that contains graphical modes for window
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setColorAttribute(System.String)">
            <summary>Sets a value for the graphical mode GC_COLOR</summary>
            <param name="val"> 
            A single color, e.g. "blue", "green" ...etc. 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setColoredAttribute(System.Int32)">
            <summary>Sets a value for the graphical mode GC_COLORED</summary>
            <param name="val"> 
            The colored mode, which can be either "colored3" or "colored6"
            or "colored12" 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setDrawModeAttribute(System.String)">
            <summary>Sets a value for the graphical mode GC_DRAWMODE</summary>
            <param name="val"> 
            One of the possible draw modes: "margin" or "fill" 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setLineWidthAttribute(System.Int32)">
            <summary>Sets a value for the graphical mode GC_LINEWIDTH</summary>
            <param name="val"> 
            The line width, which can range from 1 to 50 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setLutAttribute(System.String)">
            <summary>Sets a value for the graphical mode GC_LUT</summary>
            <param name="val"> 
            One of the possible modes of look up tables. For 
            further information on particular setups, please refer to the
            Reference Manual entry of the operator set_lut.
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setPaintAttribute(System.String)">
            <summary>Sets a value for the graphical mode GC_PAINT</summary>
            <param name="val"> 
            One of the possible paint modes. For further 
            information on particular setups, please refer refer to the
            Reference Manual entry of the operator set_paint.
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setShapeAttribute(System.String)">
            <summary>Sets a value for the graphical mode GC_SHAPE</summary>
            <param name="val">
            One of the possible shape modes. For further 
            information on particular setups, please refer refer to the
            Reference Manual entry of the operator set_shape.
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.setLineStyleAttribute(HalconDotNet.HTuple)">
            <summary>Sets a value for the graphical mode GC_LINESTYLE</summary>
            <param name="val"> 
            A line style mode, which works 
            identical to the input for the HDevelop operator 
            'set_line_style'. For particular information on this 
            topic, please refer to the Reference Manual entry of the operator
            set_line_style.
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.addValue(System.String,System.Int32)">
            <summary> 
            Adds a value to the hashlist 'graphicalSettings' for the 
            graphical mode described by the parameter 'key' 
            </summary>
            <param name="key"> 
            A graphical mode defined by the constant GC_* 
            </param>
            <param name="val"> 
            Defines the value as an int for this graphical
            mode 'key' 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.addValue(System.String,System.String)">
            <summary>
            Adds a value to the hashlist 'graphicalSettings' for the 
            graphical mode, described by the parameter 'key'
            </summary>
            <param name="key"> 
            A graphical mode defined by the constant GC_* 
            </param>
            <param name="val"> 
            Defines the value as a string for this 
            graphical mode 'key' 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.addValue(System.String,HalconDotNet.HTuple)">
            <summary> 
            Adds a value to the hashlist 'graphicalSettings' for the 
            graphical mode, described by the parameter 'key'
            </summary>
            <param name="key">
            A graphical mode defined by the constant GC_* 
            </param>
            <param name="val"> 
            Defines the value as a HTuple for this 
            graphical mode 'key' 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.clear">
            <summary> 
            Clears the list of graphical settings. 
            There will be no graphical changes made prior 
            before drawing objects, since there are no 
            graphical entries to be applied to the window.
            </summary>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.copy">
            <summary> 
            Returns an exact clone of this graphicsContext instance 
            </summary>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.getGraphicsAttribute(System.String)">
            <summary> 
            If the hashtable contains the key, the corresponding 
            hashtable value is returned 
            </summary>
            <param name="key"> 
            One of the graphical keys starting with GC_* 
            </param>
        </member>
        <member name="M:ViewROI.CLGraphicsContext.copyContextList">
            <summary> 
            Returns a copy of the hashtable that carries the 
            entries for the current graphical context 
            </summary>
            <returns> current graphical context </returns>
        </member>
        <member name="T:ViewROI.CLHObjectEntry">
            <summary>
            This class is an auxiliary class, which is used to 
            link a graphical context to an HALCON object. The graphical 
            context is described by a hashtable, which contains a list of
            graphical modes (e.g GC_COLOR, GC_LINEWIDTH and GC_PAINT) 
            and their corresponding values (e.g "blue", "4", "3D-plot"). These
            graphical states are applied to the window before displaying the
            object.
            </summary>
        </member>
        <member name="F:ViewROI.CLHObjectEntry.gContext">
            <summary>Hashlist defining the graphical context for HObj</summary>
        </member>
        <member name="F:ViewROI.CLHObjectEntry.HObj">
            <summary>HALCON object</summary>
        </member>
        <member name="M:ViewROI.CLHObjectEntry.#ctor(HalconDotNet.HObject,System.Collections.Hashtable)">
            <summary>Constructor</summary>
            <param name="obj">
            HALCON object that is linked to the graphical context gc. 
            </param>
            <param name="gc"> 
            Hashlist of graphical states that are applied before the object
            is displayed. 
            </param>
        </member>
        <member name="M:ViewROI.CLHObjectEntry.clear">
            <summary>
            Clears the entries of the class members Hobj and gContext
            </summary>
        </member>
        <member name="T:ViewROI.CLHWndCtrl">
            <summary>
            This class works as a wrapper class for the HALCON window
            HWindow. HWndCtrl is in charge of the visualization.
            You can move and zoom the visible image part by using GUI component 
            inputs or with the mouse. The class HWndCtrl uses a graphics stack 
            to manage the iconic objects for the display. Each object is linked 
            to a graphical context, which determines how the object is to be drawn.
            The context can be changed by calling changeGraphicSettings().
            The graphical "modes" are defined by the class GraphicsContext and 
            map most of the dev_set_* operators provided in HDevelop.
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.MODE_VIEW_NONE">
            <summary>No action is performed on mouse events</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.MODE_VIEW_ZOOM">
            <summary>Zoom is performed on mouse events</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.MODE_VIEW_MOVE">
            <summary>Move is performed on mouse events</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.MODE_VIEW_ZOOMWINDOW">
            <summary>Magnification is performed on mouse events</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.EVENT_UPDATE_IMAGE">
            <summary>
            Constant describes delegate message to signal new image
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.ERR_READING_IMG">
            <summary>
            Constant describes delegate message to signal error
            when reading an image from file
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.ERR_DEFINING_GC">
            <summary> 
            Constant describes delegate message to signal error
            when defining a graphical context
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.MAXNUMOBJLIST">
            <summary> 
            Maximum number of HALCON objects that can be put on the graphics 
            stack without loss. For each additional object, the first entry 
            is removed from the stack again.
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.viewPort">
            <summary>HALCON window</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.roiManager">
            <summary>
            Instance of ROIController, which manages ROI interaction
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.exceptionText">
            <summary>Error message when an exception is thrown</summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.addInfoDelegate">
            <summary>
            Delegate to add information to the HALCON window after 
            the paint routine has finished
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.NotifyIconObserver">
            <summary>
            Delegate to notify about failed tasks of the HWndCtrl instance
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.HObjList">
            <summary> 
            List of HALCON objects to be drawn into the HALCON window. 
            The list shouldn't contain more than MAXNUMOBJLIST objects, 
            otherwise the first entry is removed from the list.
            </summary>
        </member>
        <member name="F:ViewROI.CLHWndCtrl.mGC">
            <summary>
            Instance that describes the graphical context for the
            HALCON window. According on the graphical settings
            attached to each HALCON object, this graphical context list 
            is updated constantly.
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.#ctor(HalconDotNet.HWindowControl)">
            <summary> 
            Initializes the image dimension, mouse delegation, and the 
            graphical context setup of the instance.
            </summary>
            <param name="view"> HALCON window </param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.useROIController(ViewROI.CLROIController)">
            <summary>
            Registers an instance of an ROIController with this window 
            controller (and vice versa).
            </summary>
            <param name="rC"> 
            Controller that manages interactive ROIs for the HALCON window 
            </param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setImagePart(HalconDotNet.HImage)">
            <summary>
            Read dimensions of the image to adjust own window settings
            </summary>
            <param name="image">HALCON image</param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setImagePart(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Adjust window settings by the values supplied for the left 
            upper corner and the right lower corner
            </summary>
            <param name="r1">y coordinate of left upper corner</param>
            <param name="c1">x coordinate of left upper corner</param>
            <param name="r2">y coordinate of right lower corner</param>
            <param name="c2">x coordinate of right lower corner</param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setViewState(System.Int32)">
            <summary>
            Sets the view mode for mouse events in the HALCON window
            (zoom, move, magnify or none).
            </summary>
            <param name="mode">One of the MODE_VIEW_* constants</param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setDispLevel(System.Int32)">
            <summary>
            Paint or don't paint the ROIs into the HALCON window by 
            defining the parameter to be equal to 1 or not equal to 1.
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.zoomImage(System.Double)">
            <summary>
            Scales the image in the HALCON window according to the 
            value scaleFactor
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.scaleWindow(System.Double)">
            <summary>
            Scales the HALCON window according to the value scale
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setZoomWndFactor">
            <summary>
            Recalculates the image-window-factor, which needs to be added to 
            the scale factor for zooming an image. This way the zoom gets 
            adjusted to the window-image relation, expressed by the equation 
            imageWidth/viewPort.Width.
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setZoomWndFactor(System.Double)">
            <summary>
            Sets the image-window-factor to the value zoomF
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.resetAll">
            <summary>
            Resets all parameters that concern the HALCON window display 
            setup to their initial values and clears the ROI list.
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setGUICompRangeX(System.Int32[],System.Int32)">
            <summary>
            To initialize the move function using a GUI component, the HWndCtrl
            first needs to know the range supplied by the GUI component. 
            For the x direction it is specified by xRange, which is 
            calculated as follows: GuiComponentX.Max()-GuiComponentX.Min().
            The starting value of the GUI component has to be supplied 
            by the parameter Init
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.setGUICompRangeY(System.Int32[],System.Int32)">
            <summary>
            To initialize the move function using a GUI component, the HWndCtrl
            first needs to know the range supplied by the GUI component. 
            For the y direction it is specified by yRange, which is 
            calculated as follows: GuiComponentY.Max()-GuiComponentY.Min().
            The starting value of the GUI component has to be supplied 
            by the parameter Init
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.resetGUIInitValues(System.Int32,System.Int32)">
            <summary>
            Resets to the starting value of the GUI component.
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.moveXByGUIHandle(System.Int32)">
            <summary>
            Moves the image by the value valX supplied by the GUI component
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.moveYByGUIHandle(System.Int32)">
            <summary>
            Moves the image by the value valY supplied by the GUI component
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.zoomByGUIHandle(System.Double)">
            <summary>
            Zooms the image by the value valF supplied by the GUI component
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.repaint">
            <summary>
            Triggers a repaint of the HALCON window
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.repaint(HalconDotNet.HWindow)">
            <summary>
            Repaints the HALCON window 'window'
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.addIconicVar(HalconDotNet.HObject)">
            <summary>
            Adds an iconic object to the graphics stack similar to the way
            it is defined for the HDevelop graphics stack.
            </summary>
            <param name="obj">Iconic object</param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.clearList">
            <summary>
            Clears all entries from the graphics stack 
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.getListCount">
            <summary>
            Returns the number of items on the graphics stack
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.changeGraphicSettings(System.String,System.String)">
            <summary>
            Changes the current graphical context by setting the specified mode
            (constant starting by GC_*) to the specified value.
            </summary>
            <param name="mode">
            Constant that is provided by the class GraphicsContext
            and describes the mode that has to be changed, 
            e.g., GraphicsContext.GC_COLOR
            </param>
            <param name="val">
            Value, provided as a string, 
            the mode is to be changed to, e.g., "blue" 
            </param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.changeGraphicSettings(System.String,System.Int32)">
            <summary>
            Changes the current graphical context by setting the specified mode
            (constant starting by GC_*) to the specified value.
            </summary>
            <param name="mode">
            Constant that is provided by the class GraphicsContext
            and describes the mode that has to be changed, 
            e.g., GraphicsContext.GC_LINEWIDTH
            </param>
            <param name="val">
            Value, provided as an integer, the mode is to be changed to, 
            e.g., 5 
            </param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.changeGraphicSettings(System.String,HalconDotNet.HTuple)">
            <summary>
            Changes the current graphical context by setting the specified mode
            (constant starting by GC_*) to the specified value.
            </summary>
            <param name="mode">
            Constant that is provided by the class GraphicsContext
            and describes the mode that has to be changed, 
            e.g.,  GraphicsContext.GC_LINESTYLE
            </param>
            <param name="val">
            Value, provided as an HTuple instance, the mode is 
            to be changed to, e.g., new HTuple(new int[]{2,2})
            </param>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.clearGraphicContext">
            <summary>
            Clears all entries from the graphical context list
            </summary>
        </member>
        <member name="M:ViewROI.CLHWndCtrl.getGraphicContext">
            <summary>
            Returns a clone of the graphical context list (hashtable)
            </summary>
        </member>
        <member name="T:ViewROI.CLROI">
            <summary>
            This class is a base class containing virtual methods for handling
            ROIs. Therefore, an inheriting class needs to define/override these
            methods to provide the ROIController with the necessary information on
            its (= the ROIs) shape and position. The example project provides 
            derived ROI shapes for rectangles, lines, circles, and circular arcs.
            To use other shapes you must derive a new class from the base class 
            ROI and implement its methods.
            </summary>    
        </member>
        <member name="F:ViewROI.CLROI.OperatorFlag">
            <summary>
            Flag to define the ROI to be 'positive' or 'negative'.
            </summary>
        </member>
        <member name="F:ViewROI.CLROI.flagLineStyle">
            <summary>Parameter to define the line style of the ROI.</summary>
        </member>
        <member name="F:ViewROI.CLROI.POSITIVE_FLAG">
            <summary>Constant for a positive ROI flag.</summary>
        </member>
        <member name="F:ViewROI.CLROI.NEGATIVE_FLAG">
            <summary>Constant for a negative ROI flag.</summary>
        </member>
        <member name="M:ViewROI.CLROI.#ctor">
            <summary>Constructor of abstract ROI class.</summary>
        </member>
        <member name="M:ViewROI.CLROI.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position.</summary>
            <param name="midX">
            x (=column) coordinate for ROI
            </param>
            <param name="midY">
            y (=row) coordinate for ROI
            </param>
        </member>
        <member name="M:ViewROI.CLROI.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window.</summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.CLROI.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y)
            </summary>
            <param name="x">x (=column) coordinate</param>
            <param name="y">y (=row) coordinate</param>
            <returns> 
            Distance of the closest ROI handle.
            </returns>
        </member>
        <member name="M:ViewROI.CLROI.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window. 
            </summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.CLROI.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y).
            </summary>
            <param name="x">x (=column) coordinate</param>
            <param name="y">y (=row) coordinate</param>
        </member>
        <member name="M:ViewROI.CLROI.getRegion">
            <summary>Gets the HALCON region described by the ROI.</summary>
        </member>
        <member name="M:ViewROI.CLROI.getModelData">
            <summary>
            Gets the model information described by 
            the ROI.
            </summary> 
        </member>
        <member name="M:ViewROI.CLROI.getNumHandles">
            <summary>Number of handles defined for the ROI.</summary>
            <returns>Number of handles</returns>
        </member>
        <member name="M:ViewROI.CLROI.getActHandleIdx">
            <summary>Gets the active handle of the ROI.</summary>
            <returns>Index of the active handle (from the handle list)</returns>
        </member>
        <member name="M:ViewROI.CLROI.getOperatorFlag">
            <summary>
            Gets the sign of the ROI object, being either 
            'positive' or 'negative'. This sign is used when creating a model
            region for matching applications from a list of ROIs.
            </summary>
        </member>
        <member name="M:ViewROI.CLROI.setOperatorFlag(System.Int32)">
            <summary>
            Sets the sign of a ROI object to be positive or negative. 
            The sign is used when creating a model region for matching
            applications by summing up all positive and negative ROI models
            created so far.
            </summary>
            <param name="flag">Sign of ROI object</param>
        </member>
        <member name="T:ViewROI.ROICircle">
            <summary>
            This class demonstrates one of the possible implementations for a 
            circular ROI. ROICircle inherits from the base class ROI and 
            implements (besides other auxiliary methods) all virtual methods 
            defined in ROI.cs.
            </summary>
        </member>
        <member name="M:ViewROI.ROICircle.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position</summary>
        </member>
        <member name="M:ViewROI.ROICircle.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window</summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROICircle.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y)
            </summary>
        </member>
        <member name="M:ViewROI.ROICircle.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window 
            </summary>
        </member>
        <member name="M:ViewROI.ROICircle.getRegion">
            <summary>Gets the HALCON region described by the ROI</summary>
        </member>
        <member name="M:ViewROI.ROICircle.getModelData">
            <summary>
            Gets the model information described by 
            the  ROI
            </summary> 
        </member>
        <member name="M:ViewROI.ROICircle.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y)
            </summary>
        </member>
        <member name="T:ViewROI.ROICircularArc">
            <summary>
            This class implements an ROI shaped as a circular
            arc. ROICircularArc inherits from the base class ROI and 
            implements (besides other auxiliary methods) all virtual methods 
            defined in ROI.cs.
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position</summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window</summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROICircularArc.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y)
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window 
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y)
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.getRegion">
            <summary>Gets the HALCON region described by the ROI</summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.getModelData">
            <summary>
            Gets the model information described by the ROI
            </summary> 
        </member>
        <member name="M:ViewROI.ROICircularArc.determineArcHandles">
            <summary>
            Auxiliary method to determine the positions of the second and
            third handle.
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.setStartHandle">
            <summary> 
            Auxiliary method to recalculate the start handle for the arc 
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.setExtentHandle">
            <summary>
            Auxiliary method to recalculate the extent handle for the arc
            </summary>
        </member>
        <member name="M:ViewROI.ROICircularArc.updateArrowHandle">
            <summary>
            Auxiliary method to display an arrow at the extent arc position
            </summary>
        </member>
        <member name="T:ViewROI.CLROIController">
            <summary>
            This class creates and manages ROI objects. It responds 
            to  mouse device inputs using the methods mouseDownAction and 
            mouseMoveAction. You don't have to know this class in detail when you 
            build your own C# project. But you must consider a few things if 
            you want to use interactive ROIs in your application: There is a
            quite close connection between the ROIController and the HWndCtrl 
            class, which means that you must 'register' the ROIController
            with the HWndCtrl, so the HWndCtrl knows it has to forward user input
            (like mouse events) to the ROIController class.  
            The visualization and manipulation of the ROI objects is done 
            by the ROIController.
            This class provides special support for the matching
            applications by calculating a model region from the list of ROIs. For
            this, ROIs are added and subtracted according to their sign.
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.MODE_ROI_POS">
            <summary>
            Constant for setting the ROI mode: positive ROI sign.
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.MODE_ROI_NEG">
            <summary>
            Constant for setting the ROI mode: negative ROI sign.
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.MODE_ROI_NONE">
            <summary>
            Constant for setting the ROI mode: no model region is computed as
            the sum of all ROI objects.
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.EVENT_UPDATE_ROI">
            <summary>Constant describing an update of the model region</summary>
        </member>
        <member name="F:ViewROI.CLROIController.EVENT_MOVING_ROI">
            <summary>Constant describing an update of the model region</summary>
        </member>
        <member name="F:ViewROI.CLROIController.activeROIidx">
            <summary>Index of the active ROI object</summary>
        </member>
        <member name="F:ViewROI.CLROIController.ROIList">
            <summary>List containing all created ROI objects so far</summary>
        </member>
        <member name="F:ViewROI.CLROIController.ModelROI">
            <summary>
            Region obtained by summing up all negative 
            and positive ROI objects from the ROIList 
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.viewController">
            <summary>
            Reference to the HWndCtrl, the ROI Controller is registered to
            </summary>
        </member>
        <member name="F:ViewROI.CLROIController.NotifyRCObserver">
            <summary>
            Delegate that notifies about changes made in the model region
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:ViewROI.CLROIController.setViewController(ViewROI.CLHWndCtrl)">
            <summary>Registers the HWndCtrl to this ROIController instance</summary>
        </member>
        <member name="M:ViewROI.CLROIController.getModelRegion">
            <summary>Gets the ModelROI object</summary>
        </member>
        <member name="M:ViewROI.CLROIController.getROIList">
            <summary>Gets the List of ROIs created so far</summary>
        </member>
        <member name="M:ViewROI.CLROIController.getActiveROI">
            <summary>Get the active ROI</summary>
        </member>
        <member name="M:ViewROI.CLROIController.setROIShape(ViewROI.CLROI)">
            <summary>
            To create a new ROI object the application class initializes a 
            'seed' ROI instance and passes it to the ROIController.
            The ROIController now responds by manipulating this new ROI
            instance.
            </summary>
            <param name="r">
            'Seed' ROI object forwarded by the application forms class.
            </param>
        </member>
        <member name="M:ViewROI.CLROIController.setROISign(System.Int32)">
            <summary>
            Sets the sign of a ROI object to the value 'mode' (MODE_ROI_NONE,
            MODE_ROI_POS,MODE_ROI_NEG)
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.removeActive">
            <summary>
            Removes the ROI object that is marked as active. 
            If no ROI object is active, then nothing happens. 
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.defineModelROI">
            <summary>
            Calculates the ModelROI region for all objects contained 
            in ROIList, by adding and subtracting the positive and 
            negative ROI objects.
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.reset">
            <summary>
            Clears all variables managing ROI objects
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.resetROI">
            <summary>
            Deletes this ROI instance if a 'seed' ROI object has been passed
            to the ROIController by the application class.
            
            </summary>
        </member>
        <member name="M:ViewROI.CLROIController.setDrawColor(System.String,System.String,System.String)">
            <summary>Defines the colors for the ROI objects</summary>
            <param name="aColor">Color for the active ROI object</param>
            <param name="inaColor">Color for the inactive ROI objects</param>
            <param name="aHdlColor">
            Color for the active handle of the active ROI object
            </param>
        </member>
        <member name="M:ViewROI.CLROIController.paintData(HalconDotNet.HWindow)">
            <summary>
            Paints all objects from the ROIList into the HALCON window
            </summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.CLROIController.mouseDownAction(System.Double,System.Double)">
            <summary>
            Reaction of ROI objects to the 'mouse button down' event: changing
            the shape of the ROI and adding it to the ROIList if it is a 'seed'
            ROI.
            </summary>
            <param name="imgX">x coordinate of mouse event</param>
            <param name="imgY">y coordinate of mouse event</param>
            <returns></returns>
        </member>
        <member name="M:ViewROI.CLROIController.mouseMoveAction(System.Double,System.Double)">
            <summary>
            Reaction of ROI objects to the 'mouse button move' event: moving
            the active ROI.
            </summary>
            <param name="newX">x coordinate of mouse event</param>
            <param name="newY">y coordinate of mouse event</param>
        </member>
        <member name="T:ViewROI.ROILine">
            <summary>
            This class demonstrates one of the possible implementations for a 
            linear ROI. ROILine inherits from the base class ROI and 
            implements (besides other auxiliary methods) all virtual methods 
            defined in ROI.cs.
            </summary>
        </member>
        <member name="M:ViewROI.ROILine.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position.</summary>
        </member>
        <member name="M:ViewROI.ROILine.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window.</summary>
        </member>
        <member name="M:ViewROI.ROILine.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y).
            </summary>
        </member>
        <member name="M:ViewROI.ROILine.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window. 
            </summary>
        </member>
        <member name="M:ViewROI.ROILine.getRegion">
            <summary>Gets the HALCON region described by the ROI.</summary>
        </member>
        <member name="M:ViewROI.ROILine.getModelData">
            <summary>
            Gets the model information described by 
            the ROI.
            </summary> 
        </member>
        <member name="M:ViewROI.ROILine.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y).
            </summary>
        </member>
        <member name="M:ViewROI.ROILine.updateArrowHandle">
            <summary> Auxiliary method </summary>
        </member>
        <member name="T:ViewROI.ROIRectangle1">
            <summary>
            This class demonstrates one of the possible implementations for a 
            (simple) rectangularly shaped ROI. ROIRectangle1 inherits 
            from the base class ROI and implements (besides other auxiliary
            methods) all virtual methods defined in ROI.cs.
            Since a simple rectangle is defined by two data points, by the upper 
            left corner and the lower right corner, we use four values (row1/col1) 
            and (row2/col2) as class members to hold these positions at 
            any time of the program. The four corners of the rectangle can be taken
            as handles, which the user can use to manipulate the size of the ROI. 
            Furthermore, we define a midpoint as an additional handle, with which
            the user can grab and drag the ROI. Therefore, we declare NumHandles
            to be 5 and set the activeHandle to be 0, which will be the upper left
            corner of our ROI.
            </summary>
        </member>
        <member name="M:ViewROI.ROIRectangle1.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:ViewROI.ROIRectangle1.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position</summary>
            <param name="midX">
            x (=column) coordinate for interactive ROI
            </param>
            <param name="midY">
            y (=row) coordinate for interactive ROI
            </param>
        </member>
        <member name="M:ViewROI.ROIRectangle1.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window</summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROIRectangle1.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y)
            </summary>
            <param name="x">x (=column) coordinate</param>
            <param name="y">y (=row) coordinate</param>
            <returns> 
            Distance of the closest ROI handle.
            </returns>
        </member>
        <member name="M:ViewROI.ROIRectangle1.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window
            </summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROIRectangle1.getRegion">
            <summary>Gets the HALCON region described by the ROI</summary>
        </member>
        <member name="M:ViewROI.ROIRectangle1.getModelData">
            <summary>
            Gets the model information described by 
            the interactive ROI
            </summary> 
        </member>
        <member name="M:ViewROI.ROIRectangle1.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI instance. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y)
            </summary>
            <param name="newX">x mouse coordinate</param>
            <param name="newY">y mouse coordinate</param>
        </member>
        <member name="T:ViewROI.ROIRectangle2">
            <summary>
            This class demonstrates one of the possible implementations for a 
            (simple) rectangularly shaped ROI. To create this rectangle we use 
            a center point (midR, midC), an orientation 'phi' and the half 
            edge lengths 'length1' and 'length2', similar to the HALCON 
            operator gen_rectangle2(). 
            The class ROIRectangle2 inherits from the base class ROI and 
            implements (besides other auxiliary methods) all virtual methods 
            defined in ROI.cs.
            </summary>
        </member>
        <member name="F:ViewROI.ROIRectangle2.length1">
            <summary>Half length of the rectangle side, perpendicular to phi</summary>
        </member>
        <member name="F:ViewROI.ROIRectangle2.length2">
            <summary>Half length of the rectangle side, in direction of phi</summary>
        </member>
        <member name="F:ViewROI.ROIRectangle2.midR">
            <summary>Row coordinate of midpoint of the rectangle</summary>
        </member>
        <member name="F:ViewROI.ROIRectangle2.midC">
            <summary>Column coordinate of midpoint of the rectangle</summary>
        </member>
        <member name="F:ViewROI.ROIRectangle2.phi">
            <summary>Orientation of rectangle defined in radians.</summary>
        </member>
        <member name="M:ViewROI.ROIRectangle2.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:ViewROI.ROIRectangle2.createROI(System.Double,System.Double)">
            <summary>Creates a new ROI instance at the mouse position</summary>
            <param name="midX">
            x (=column) coordinate for interactive ROI
            </param>
            <param name="midY">
            y (=row) coordinate for interactive ROI
            </param>
        </member>
        <member name="M:ViewROI.ROIRectangle2.draw(HalconDotNet.HWindow)">
            <summary>Paints the ROI into the supplied window</summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROIRectangle2.distToClosestHandle(System.Double,System.Double)">
            <summary> 
            Returns the distance of the ROI handle being
            closest to the image point(x,y)
            </summary>
            <param name="x">x (=column) coordinate</param>
            <param name="y">y (=row) coordinate</param>
            <returns> 
            Distance of the closest ROI handle.
            </returns>
        </member>
        <member name="M:ViewROI.ROIRectangle2.displayActive(HalconDotNet.HWindow)">
            <summary> 
            Paints the active handle of the ROI object into the supplied window
            </summary>
            <param name="window">HALCON window</param>
        </member>
        <member name="M:ViewROI.ROIRectangle2.getRegion">
            <summary>Gets the HALCON region described by the ROI</summary>
        </member>
        <member name="M:ViewROI.ROIRectangle2.getModelData">
            <summary>
            Gets the model information described by 
            the interactive ROI
            </summary> 
        </member>
        <member name="M:ViewROI.ROIRectangle2.moveByHandle(System.Double,System.Double)">
            <summary> 
            Recalculates the shape of the ROI instance. Translation is 
            performed at the active handle of the ROI object 
            for the image coordinate (x,y)
            </summary>
            <param name="newX">x mouse coordinate</param>
            <param name="newY">y mouse coordinate</param>
        </member>
        <member name="M:ViewROI.ROIRectangle2.updateHandlePos">
            <summary>
            Auxiliary method to recalculate the contour points of 
            the rectangle by transforming the initial row and 
            column coordinates (rowsInit, colsInit) by the updated
            homography hom2D
            </summary>
        </member>
    </members>
</doc>
